name: <PERSON><PERSON>za Fab Development

on:
  push:
    branches:
      - development

jobs:
  build_job:
    name: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - uses: actions/checkout@v2
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm install
      - run: echo "VITE_API_ENV=DEV_V3" >> .env
      - run: npm run build --if-present
      - run: ls -al dist
      - name: Zip the buid/dist file
        run: zip dist.zip ./dist -r
      - name: Upload Build to artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: dist.zip
          retention-days: 1

  deploy_job:
    name: deploy
    needs: build_job
    runs-on: ubuntu-latest
    steps:
      - name: install ssh keys in image vm (- runs-on)
        run: |
          install -m 600 -D /dev/null ~/.ssh/id_rsa
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.VM_IP }} > ~/.ssh/known_hosts
      - name: Download Build artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: build
      - name: list files in build file
        run: ls -al
      - name: Unzip the downloaded build file
        run: unzip dist.zip
      - name: list files in build file
        run: ls -al
      - name: move build file to server
        run: scp -r dist/* ${{ secrets.VM_USER }}@${{ secrets.VM_IP }}:~/dist/
      - name: run setup file in server
        run: ssh ${{ secrets.VM_USER }}@${{ secrets.VM_IP }} "~/fabui3.setup.sh && exit"
      - name: cleanup ssh keys in image vm (- runs-on)
        run: rm -rf ~/.ssh
