{"name": "forms-react", "private": true, "version": "3.0.0", "type": "module", "scripts": {"dev": "vite --port=3000", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "lint:fix": "eslint . --fix"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@loadable/component": "^5.16.4", "@mui/icons-material": "^5.15.19", "@mui/lab": "^5.0.0-alpha.135", "@mui/material": "^5.15.18", "@mui/x-data-grid": "^7.10.0", "@mui/x-date-pickers": "^7.22.2", "@react-pdf/renderer": "^4.2.1", "@reduxjs/toolkit": "^2.2.5", "@types/lodash": "^4.17.5", "@types/react-signature-canvas": "^1.0.5", "axios": "^1.7.2", "dayjs": "^1.11.13", "formik": "^2.4.6", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "mui-tel-input": "^5.1.2", "node": "^18.19.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-signature-canvas": "^1.0.6", "react-tagsinput": "^3.20.3", "react-toastify": "^11.0.2", "striptags": "^3.2.0", "to-words": "^4.1.0", "yup": "^1.4.0"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.5", "@types/loadable__component": "^5.13.9", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.12.12", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-tagsinput": "^3.20.6", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "^3.3.3", "react-toastify": "^11.0.2", "sass": "^1.77.4", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-dynamic-import": "^1.5.0"}}