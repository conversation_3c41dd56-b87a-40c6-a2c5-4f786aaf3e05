import api, { handleError, apiRoutes, authHeaders } from './config';

export const createApplicant = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any,
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.applicant, data, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateApplicant = async (
  payload: { id: string, data: any },
  { rejectWithValue, fulfillWithValue }: any,
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.applicant}/${payload.id}`,
      payload.data,
      {
        headers,
      },
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getApplicant = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any,
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.applicant}/${id}`, {
      headers,
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const deleteApplicant = async (
  payload: { id: string, data: any },
  { rejectWithValue, fulfillWithValue }: any,
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.applicant}/${payload.id}`,
      payload.data,
      {
        headers,
      },
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
