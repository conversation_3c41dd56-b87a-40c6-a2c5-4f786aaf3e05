import api, { apiRoutes, authHeaders, handleError } from './config';

export const getClients = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.clientsRepository, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getClientforms = async (
  payload: { id: any; type: string },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const organizationId = localStorage.getItem('org_id');

    let url = '';
    switch (payload.type) {
      case 'client-assessment':
        url = `${apiRoutes.formValue}?client_id=${payload.id}&app_id=${localStorage.getItem('assesst-app-id')}&organization_id=${organizationId}`;
        break;
      case 'onboarding-employee':
        url = `${apiRoutes.formValue}?user_id=${payload.id}&app_id=${localStorage.getItem('onboard-app-id')}&organization_id=${organizationId}`;
        break;
      case 'caregiver':
        url = `${apiRoutes.formValue}?user_id=${payload.id}&organization_id=${organizationId}`;
        break;
      default:
        break;
    }

    const headers = await authHeaders();
    const response = await api.get(url, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getAssessmentClients = async (
  appId: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.clientsRepository}?app_id=${appId}`,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const checkFolder = async (
  payload: { clientId: any; folderName?: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.form}/check-folder/${payload.clientId}${
        payload?.folderName ? `?path=${payload?.folderName}` : ``
      }`,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const sendPdf = async (
  payload: { clientId: any; data?: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(
      `${apiRoutes.form}/pdf/${payload.clientId}`,
      payload.data,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const exportToCsv = async (
  payload: { client_id: any; exportToExcel: boolean },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();

    const response = await api.get(apiRoutes.formValue, {
      params: payload,
      responseType: 'blob',
      headers
    });

    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
