import api, { handleError, apiRoutes, authHeaders } from './config';

const dashBoardAppsList = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any,
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.apps}/me`, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export default dashBoardAppsList;
