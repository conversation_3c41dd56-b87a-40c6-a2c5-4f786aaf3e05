import api, { apiRoutes, authHeaders, handleError } from './config';

export const submitLoginForm = async (
  payload: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const loginRoute = localStorage.getItem('login_route');
    const response = await api.post(
      loginRoute === '/login' ? apiRoutes.auth.login : apiRoutes.auth.orgAuth,
      payload
    );
    if (response.data.access_token) {
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem(
        'user_type',
        loginRoute === '/login' ? 'super_admin' : 'organization'
      );
    }
    if (loginRoute === '/login') {
      window.location.assign('/apps');
    }
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const fetchSuperAdmin = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const { data } = await api.get(apiRoutes.admin, { headers });
    // dispatch(firstTimeUserFetching({ user: data.data }));
    return fulfillWithValue(data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const fetchOrganizationAdmin = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const { data } = await api.get(`${apiRoutes.organization}/${id}`, {
      params: {
        relation: 'apps,industry_type',
        onboarding_apps: true
      },
      headers
    });
    return fulfillWithValue(data.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const userValidation = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const { data }: any = await api.get(apiRoutes.userValidation, { headers });
    if (data) {
      localStorage.setItem('org_id', data?.organization_id);
      localStorage.setItem('org_name', data?.name);
    }
    window.location.assign('/apps');

    return fulfillWithValue(data?.organization_id);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
