import api, { handleError, apiRoutes, authHeaders } from './config';

export const getOrganizations = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.organization, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getOrganizationDetails = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.organization}/${id}`, {
      params: {
        relation: 'apps,industry_type',
        onboarding_apps: true
      },
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getApps = async (
  industry_type_id: string,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.apps}?industry_type_id=${industry_type_id}`,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getIndustryTypes = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.industryTypes, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const createOrganization = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.organization, data, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const deleteOrganization = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.delete(`${apiRoutes.organization}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateOrganization = async (
  payload: { id: string; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.organization}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const AppFormConfiguration = async (
  payload: { orgId: string; data: any; type: string },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.organization}${payload.type === 'add' ? '/add-form/' : '/remove-form/'}${payload.orgId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
