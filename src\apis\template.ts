import api, { apiRoutes, authHeaders, handleError } from './config';

export const getTemplateForm = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.formsSection}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const CreateTemplate = async (
  // data: any,
  payload: { formId: string; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    if (payload.formId !== '') {
      const response = await api.patch(
        `${apiRoutes.formsSection}/${payload.formId}`,
        payload.data,
        {
          headers
        }
      );

      return fulfillWithValue(response.data);
    }

    const response = await api.post(apiRoutes.formsSection, payload.data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
