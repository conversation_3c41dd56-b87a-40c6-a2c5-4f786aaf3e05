import api, { handleError, apiRoutes, authHeaders } from './config';

export const getOrgData = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.organization, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getUsersData = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.user, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getUser = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.user}/${id}?relation=organization,apps`,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getOrgApps = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.organization}/${id}?relation=apps`,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const createUser = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.user, data, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateUser = async (
  payload: { id: string; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.user}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getEmployees = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.onboardingEmployee, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getEmployee = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.onboardingEmployee}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getEmpChecklist = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.onboardEmpChecklist}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getFormValues = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.formValue}/documents/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getChecklistData = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.onboardOrgChecklist, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const saveChecklist = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(
      `${apiRoutes.onboardEmpChecklist}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const createChecklist = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.onboardOrgChecklist, data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const deleteListElement = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.onboardOrgChecklist, data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getOnBoaedApps = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.organization}/${id}?relation=apps`,
      { headers }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const Onboardapllicant = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.applicant}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const sendMailAfterScheduleInterview = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post('organization/send-email', data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const deleteUser = async (
  id: string,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.delete(`${apiRoutes.user}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const fetchUserPaginationData = async (
  { limit, page }: { limit: number; page: number },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const params = new URLSearchParams({
      pagination: 'true',
      page: page.toString(),
      limit: limit.toString()
    });
    const response = await api.get(`${apiRoutes.user}?${params.toString()}`, {
      headers
    });

    return fulfillWithValue(response.data.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
