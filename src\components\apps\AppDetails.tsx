/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import {
  Box,
  Button,
  Grid,
  Typography,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  CardMedia
} from '@mui/material';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import { SubMenu } from '../form.elements';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  changeformstatus,
  clearGetAppState,
  clearAppState
} from '../../redux/reducers/apps.reducer';
import '../../css/app-details-styles.scss';
import { CreateForm } from '../reusable/CreateForm';
import Shell from '../layout/Shell';

// Define types for form data
interface Form {
  form_id: string;
  name: string;
  icon?: string;
  status?: boolean;
}

// Styled Components for consistent theming and cleaner code
const ContainerBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[200],
  padding: theme.spacing(2, 12) // roughly 100px on left/right
}));

const ContentBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8),
  backgroundColor: theme.palette.common.white
}));

interface FormCardProps {
  selected?: boolean;
}

const FormCard = styled(Card, {
  shouldForwardProp: (prop) => prop !== 'selected'
})<FormCardProps>(({ theme, selected }) => ({
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  padding: 0,
  width: '100%',
  height: 80,
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  position: 'relative',
  backgroundColor: theme.palette.background.paper,
  border: selected
    ? `1px solid ${theme.palette.primary.main}`
    : '1px solid transparent',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.background.default,
    border: `1px solid ${theme.palette.primary.main}`,
    transform: 'scale(1.05)',
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
  }
}));

const AppDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { app, getApp }: any = useSelector((state: AppState) => state.app);
  const { userType } = useSelector((state: AppState) => state.auth);
  const [appData, setAppData] = useState(getApp);

  useEffect(() => {
    setAppData(getApp);
  }, [getApp]);

  useEffect(() => {
    return () => {
      clearGetAppState();
      clearAppState();
    };
  }, [app]);

  const revertFormStatus = (formId: string) => {
    const revertedFormsList = appData?.forms?.map((form: any) =>
      form.form_id === formId ? { ...form, status: !form.status } : form
    );
    setAppData((prevState: any) => ({
      ...prevState,
      forms: revertedFormsList
    }));
  };

  const handleCardClick = async (formId: string, status: boolean) => {
    const updatedFormsList = appData?.forms?.map((form: any) =>
      form.form_id === formId ? { ...form, status: !form.status } : form
    );

    setAppData((prevState: any) => ({
      ...prevState,
      forms: updatedFormsList
    }));

    try {
      const response = await dispatch(
        changeformstatus({ formId, data: { status: !status } })
      );
      if (response.payload?.status) {
        dispatch({ type: 'UPDATE_APP_FORMS', payload: updatedFormsList });
      } else {
        revertFormStatus(formId);
        toast.error('Failed to change form status.');
      }
    } catch (error) {
      revertFormStatus(formId);
      toast.error('Error occurred while changing form status.');
    }
  };

  const renderSubMenu = () =>
    userType === 'organization' ? (
      <SubMenu backNavigation id={id} redirectLink="/apps" />
    ) : (
      <SubMenu backNavigation redirectLink="/apps" />
    );

  return (
    <Shell subMenu={renderSubMenu()}>
      <ContainerBox>
        <ContentBox>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 4
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip
                title={
                  getApp?.org_app_configuration
                    ? getApp?.org_app_configuration.name
                    : getApp?.name
                }
                arrow
              >
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{ mr: 2, color: '#595959' }}
                >
                  {getApp?.org_app_configuration
                    ? getApp?.org_app_configuration?.app_name
                    : getApp?.name}
                </Typography>
              </Tooltip>
              <Tooltip title="Edit this app" arrow>
                <Button
                  onClick={() => navigate(`/apps/edit-app-registration/${id}`)}
                  disableRipple
                  sx={{
                    minWidth: 'auto',
                    p: 1,
                    mb: -1,
                    color: 'text.secondary',
                    '&:hover': {
                      backgroundColor: 'transparent',
                      color: 'primary.main'
                    }
                  }}
                >
                  <EditOutlinedIcon />
                </Button>
              </Tooltip>
            </Box>
          </Box>

          <Typography variant="body1" sx={{ mb: 6, color: '#595959' }}>
            {getApp?.description}
          </Typography>

          {userType === 'super_admin' ? (
            <Box sx={{ mt: 6 }}>
              <CreateForm appId={id} appData={app} />
            </Box>
          ) : (
            <Box
              sx={{ mt: 6, backgroundColor: '#FAF9F8', borderRadius: 2, p: 2 }}
            >
              {getApp?.forms && getApp?.forms?.length > 0 ? (
                <Typography
                  variant="h5"
                  sx={{ color: '#595959', fontWeight: 500, mb: 2 }}
                >
                  Select Form Status
                </Typography>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography
                    variant="h6"
                    color="text.primary"
                    fontWeight="bold"
                  >
                    This App does not have any forms!
                  </Typography>
                  <Typography variant="body2" color="text.secondary" mt={1}>
                    You can start creating forms for the app.
                  </Typography>
                </Box>
              )}
              {getApp?.forms && getApp?.forms?.length > 0 && (
                <Box sx={{ p: 3 }}>
                  <Grid container spacing={3}>
                    {appData?.forms?.map((card: Form) => (
                      <Grid
                        item
                        xs={12}
                        sm={12}
                        md={6}
                        lg={4}
                        xl={3}
                        key={card.form_id}
                      >
                        <FormCard
                          selected={card.status}
                          onClick={() =>
                            handleCardClick(card.form_id, card.status || false)
                          }
                        >
                          <CardContent
                            sx={{
                              display: 'grid',
                              gridTemplateColumns: '1fr 10fr 1fr',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              // width: '100%',
                              py: 2 // vertical padding
                            }}
                          >
                            {/* Left Portion: Icon and Text */}
                            <Box>
                              {card?.icon ? (
                                <CardMedia
                                  component="img"
                                  image={card.icon}
                                  alt={card?.name}
                                  sx={{
                                    width: '40px',
                                    height: '40px',
                                    backgroundColor: '#F9F9F9'
                                  }}
                                />
                              ) : (
                                <AssignmentOutlinedIcon
                                  sx={{
                                    color: 'text.secondary',
                                    fontSize: 30
                                  }}
                                />
                              )}
                            </Box>
                            <Tooltip title={card.name} arrow>
                              <Typography
                                sx={{
                                  fontSize: '18px',
                                  fontWeight: 600,
                                  color: 'text.secondary',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  // width: 200
                                  paddingLeft: '8px'
                                }}
                              >
                                {card.name}
                              </Typography>
                            </Tooltip>

                            {/* Right Portion: Check Icon */}
                            <IconButton
                              sx={{
                                color: card.status
                                  ? 'primary.main'
                                  : 'grey.400',
                                // Increase click area to match the bigger icon
                                p: 1
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCardClick(
                                  card.form_id,
                                  card.status || false
                                );
                              }}
                            >
                              {card.status ? (
                                <CheckCircleOutlineIcon
                                  sx={{
                                    fontSize: '2rem' // bigger size
                                  }}
                                />
                              ) : (
                                <RadioButtonUncheckedIcon
                                  sx={{
                                    fontSize: '2rem' // match size for consistency
                                  }}
                                />
                              )}
                            </IconButton>
                          </CardContent>
                        </FormCard>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Box>
          )}
        </ContentBox>
      </ContainerBox>
    </Shell>
  );
};

export default AppDetails;
