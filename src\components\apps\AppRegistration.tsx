// Global imports
import {
  Box,
  Card,
  CardMedia,
  InputLabel,
  Typography,
  Tooltip
} from '@mui/material';
import * as Yup from 'yup';
import { Upload } from '@mui/icons-material';
import { useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local imports
import Shell from '../layout/Shell';
import {
  AppForm,
  FormInput,
  FormSelect,
  SubMenu,
  SubmitButton
} from '../form.elements';
import '../../css/app-registration-styles.scss';
import { AppDispatch, AppState } from '../../redux/app.store';
import { createapp, updateapp } from '../../redux/reducers/apps.reducer';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import LoaderUI from '../reusable/loaderUI';

const AppRegistration = ({ industryTypes, industryAppProcess }: any) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { appData, updateApp, isLoading }: any = useSelector(
    (state: AppState) => state.app
  );
  // const userType = localStorage.getItem("user_type");
  const { userType }: any = useSelector((state: AppState) => state.auth);
  const [logo, setLogo]: any = useState(null);
  // const [snackbarOpen, setSnackbarOpen] = useState(false);
  // const [snackbarMessage, setSnackbarMessage] = useState('');
  // const [snackbarSeverity, setSnackbarSeverity] = useState<
  //   'success' | 'error' | 'warning' | 'info'
  // >('success');

  const [selectedIndustryType, setSelectedIndustryType] = useState('');
  const inputLogo: any = useRef(null);
  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().strict(true).required('Name is required'),
    description: Yup.string().required('Description is required'),
    industry_type_id: id
      ? Yup.string()
      : Yup.string().required('Industry type is required'),
    industry_app_process_id: id
      ? Yup.string()
      : Yup.string().required('Industry Business process is required'),
    app_name:
      userType === 'organization'
        ? Yup.string().matches(
            /^[^\s\W][\w\s]*$/,
            'Custom App Name cannot start with a space or special characters'
          )
        : Yup.string()
  });

  const addApp = async (event: any) => {
    try {
      let createAppData;

      if (userType === 'organization') {
        const data = {
          name: event.name.replace(/\s+/g, ' ').trim(),
          description: event.description,
          logo,
          app_name: event.app_name.replace(/\s+/g, ' ').trim()
        };
        if (event?.orgAppConfiguration) {
          createAppData = await dispatch(
            updateapp({
              id: event?.orgAppConfiguration?.org_app_configuration_id,
              data
            })
          );
        } else {
          createAppData = await dispatch(createapp({ id, data }));
        }
      } else {
        const data = id
          ? {
              name: event.name.replace(/\s+/g, ' ').trim(),
              description: event.description
            }
          : {
              name: event.name.replace(/\s+/g, ' ').trim(),
              description: event.description,
              industry_type_id: event.industry_type_id,
              industry_app_process_id: event.industry_app_process_id
            };
        if (id) {
          createAppData = await dispatch(updateapp({ id, data }));
        } else {
          createAppData = await dispatch(createapp({ data }));
        }
      }

      if (createAppData?.payload?.status) {
        // toast.success(
        //   id ? 'App updated successfully!' : 'App created successfully!'
        // );

        setTimeout(() => {
          navigate(id ? `/apps/app-details/${id}` : '/apps');
        }, 1000);
      }

      if (
        (!createAppData?.payload?.status ||
          createAppData?.payload?.statusCode) &&
        createAppData?.payload.message
      ) {
        if (
          Array.isArray(createAppData?.payload?.message) &&
          createAppData?.payload?.message.length > 0
        ) {
          createAppData?.payload.message.forEach((message: string) =>
            toast.error(message)
          );
        } else {
          toast.error(createAppData?.payload.message);
        }
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    }
  };

  const uploadLogo = async () => {
    inputLogo.current.click();
  };

  const handleLogoChange = async (event: any) => {
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      setLogo(reader.result);
    };
    await dispatch(updateApp());
  };

  useMemo(() => {
    if (userType === 'organization') {
      setLogo(appData?.logo);
    }
  }, [appData, userType]);

  const getSubMenu = () => {
    return userType === 'organization' ? (
      <SubMenu
        backNavigation
        // showAppDashboardConfig
        id={id}
      />
    ) : (
      <SubMenu backNavigation redirectLink={id ? '' : '/apps'} />
    );
  };

  const handleIndustryTypeChange = (value: any) => {
    setSelectedIndustryType(value);
  };
  const initialValues = id ? appData : { name: '', description: '' };
  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <>
          <Box
            sx={{
              padding: '20px 100px'
            }}
          >
            <AppForm
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={addApp}
            >
              <Box className="AppRegstrtn-mainContainer">
                <Typography
                  className="AppRegstrtn-heading"
                  sx={{ color: '#595959' }}
                >
                  {id ? 'Update App' : 'Create App'}
                </Typography>
                <Box
                  sx={{
                    backgroundColor: '#FBF8F8',
                    padding: '30px',
                    boxShadow: '0px 0px 2px 0px rgba(0,0,0,0.24)'
                  }}
                >
                  <Box
                    sx={{
                      backgroundColor: '#FBF8F8',
                      padding: '30px'
                    }}
                  >
                    <Box>
                      <FormInput
                        name="name"
                        label="App Name"
                        required
                        disabled={userType === 'organization'}
                        containerStyles={{
                          width: '100%'
                        }}
                        style={{
                          backgroundColor: '#fff'
                        }}
                      />
                    </Box>
                    <Box>
                      <FormInput
                        name="description"
                        label="App Description"
                        required
                        disabled={userType === 'organization'}
                        containerStyles={{
                          width: '100%'
                        }}
                        style={{
                          backgroundColor: '#fff'
                        }}
                      />
                    </Box>
                    {userType === 'organization' && (
                      <>
                        <Box>
                          <Tooltip
                            title="Enter a custom name for the app"
                            arrow
                          >
                            <FormInput
                              name="app_name"
                              label="Custom App name"
                              containerStyles={{
                                width: '100%'
                              }}
                              style={{
                                backgroundColor: '#fff'
                              }}
                            />
                          </Tooltip>
                        </Box>
                        <Box>
                          <div className="spaceStyles">
                            <InputLabel
                              className="labelStyles"
                              sx={{ marginBottom: '10px' }}
                            >
                              Upload Logo
                            </InputLabel>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '10px'
                              }}
                            >
                              <Tooltip
                                title="Click to upload a logo image"
                                arrow
                              >
                                <Box
                                  onClick={uploadLogo}
                                  sx={{ cursor: 'pointer' }}
                                >
                                  <Card className="AppRegstrtn-uploadLogo">
                                    <Box
                                      sx={{
                                        backgroundColor: '#FAF9F8',
                                        borderRadius: '10px',
                                        padding: '15px'
                                      }}
                                    >
                                      <CardMedia
                                        sx={{ height: 60, width: 60 }}
                                        image={
                                          logo ||
                                          `https://ui-avatars.com/api/?name=${encodeURIComponent('upload logo')}&size=128&rounded=true`
                                        }
                                        title="Logo"
                                      />
                                      <input
                                        type="file"
                                        ref={inputLogo}
                                        style={{ display: 'none' }}
                                        onChange={handleLogoChange}
                                      />
                                    </Box>
                                    <Upload className="upload-icon" />
                                  </Card>
                                </Box>
                              </Tooltip>
                            </Box>
                          </div>
                        </Box>
                      </>
                    )}
                    {!id && (
                      <>
                        <Box>
                          <FormSelect
                            name="industry_type_id"
                            label=" Industry Type"
                            required
                            placeholder="Select Industry Type"
                            containerStyles={{
                              width: '100%'
                            }}
                            style={{
                              backgroundColor: '#fff'
                            }}
                            data={industryTypes}
                            onChange={handleIndustryTypeChange}
                          />
                        </Box>
                        <Box>
                          <FormSelect
                            name="industry_app_process_id"
                            label="Business Process"
                            required
                            placeholder="Select Business Process"
                            containerStyles={{
                              width: '100%'
                            }}
                            style={{
                              backgroundColor: '#fff'
                            }}
                            data={
                              selectedIndustryType ? industryAppProcess : []
                            }
                          />
                        </Box>
                      </>
                    )}
                  </Box>
                </Box>
                <Box sx={{ padding: '20px 0px' }}>
                  <Box
                    sx={{
                      float: 'right'
                    }}
                  >
                    <SubmitButton
                      title={
                        userType === 'organization' || id ? 'Update' : 'Create'
                      }
                      sx={{
                        color: 'white2.main',
                        padding: '10px 30px',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        '&:hover': {
                          color: 'white2.main',
                          backgroundColor: '#75E6DA'
                        }
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            </AppForm>
          </Box>
          {/* <SnackbarElement
            message={snackbarMessage}
            statusType={snackbarSeverity}
            snackbarOpen={snackbarOpen}
            setSnackbarOpen={setSnackbarOpen}
          /> */}
        </>
      )}
    </Shell>
  );
};
export default AppRegistration;
