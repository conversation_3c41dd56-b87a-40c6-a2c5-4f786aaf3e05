// Global imports
import { useState } from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Divider,
  Grid,
  IconButton,
  Tooltip,
  Typography
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

// Icon Imports
import {
  Android,
  Widgets,
  Dashboard,
  ViewModule,
  ViewComfy,
  Extension,
  Layers,
  Apps,
  Dehaze
} from '@mui/icons-material';

// Local imports
// import { SubMenu } from '../form.elements';
import Shell from '../layout/Shell';
import { AppState } from '../../redux/app.store';
import '../../css/apps-list-styles.scss';
import LoaderUI from '../reusable/loaderUI';
import { SubMenu } from '../form.elements';

interface AppListProps {
  appList: any[];
}

const iconComponents = {
  Android,
  Widgets,
  Dashboard,
  ViewModule,
  ViewComfy,
  Extension,
  Layers
};

const placeholderIconNames = [
  'Android',
  'Widgets',
  'Dashboard',
  'ViewModule',
  'ViewComfy',
  'Extension',
  'Layers'
];

const AppsList = ({ appList }: AppListProps) => {
  const navigate = useNavigate();
  const [listStyle, setListStyle] = useState<string>('grid');
  const { userType } = useSelector((state: AppState) => state.auth);
  const { isLoading } = useSelector((state: AppState) => state.app);

  const getPlaceholderIcon = (index: number) => {
    const iconName = placeholderIconNames[index % placeholderIconNames.length];
    const Icon = iconComponents[iconName as keyof typeof iconComponents];
    return <Icon fontSize="large" style={{ color: 'gray' }} />;
  };

  const getSubMenu = () => (
    <SubMenu
      buttonWithoutBg={
        userType === 'super_admin'
          ? {
              status: true,
              title: 'Create a new app',
              icon: 'AddCircleOutline',
              redirectUrl: '/apps/app-registration'
            }
          : {}
      }
      iconStyles={{ color: '#fff' }}
    />
  );

  return (
    <Shell subMenu={getSubMenu()}>
      <Box>
        {isLoading && <LoaderUI />}
        {!isLoading && (
          <Grid container>
            <Grid item xs={12}>
              <Box className="Applst-outerContainer">
                <Box className="Applst-innerContainer">
                  <Box className="Applst-iconsAlignment">
                    <Box className="Applst-appsLabelContainer">
                      <Tooltip
                        title="Available Applications"
                        arrow
                        placement="top"
                      >
                        <Box className="Applst-label-text">Apps</Box>
                      </Tooltip>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Tooltip
                        title="Switch to Grid Style"
                        arrow
                        placement="bottom"
                      >
                        <IconButton onClick={() => setListStyle('grid')}>
                          <Apps
                            color="primary"
                            sx={{ opacity: listStyle === 'grid' ? 1 : 0.4 }}
                          />
                        </IconButton>
                      </Tooltip>
                      <Divider
                        sx={{ height: 28, m: 0.5 }}
                        orientation="vertical"
                      />
                      <Tooltip
                        title="Switch to List Style"
                        arrow
                        placement="bottom"
                      >
                        <IconButton onClick={() => setListStyle('list')}>
                          <Dehaze
                            color="primary"
                            sx={{ opacity: listStyle === 'list' ? 1 : 0.4 }}
                          />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  <Box className="Applst-contentBox">
                    <Grid container spacing={4}>
                      {appList.map((data, index) => {
                        const appName =
                          data?.orgAppConfiguration?.app_name ||
                          data.name ||
                          'App';
                        const logo = data.orgAppConfiguration?.logo;
                        const placeholderIcon = getPlaceholderIcon(index);

                        return (
                          <Grid
                            item
                            key={data?.id || index}
                            xs={listStyle === 'list' ? 12 : 0}
                          >
                            <Box
                              sx={{
                                width: listStyle === 'list' ? '100%' : '180px',
                                height: listStyle === 'list' ? '60px' : '180px',
                                borderRadius:
                                  listStyle === 'list' ? '0px' : '16px',
                                background:
                                  listStyle === 'list'
                                    ? 'transparent'
                                    : '#ffffff',
                                boxShadow:
                                  listStyle === 'list'
                                    ? '0px solid'
                                    : '0px 4px 8px rgba(0,0,0,0.15)',
                                transition: '0.3s ease-in-out',
                                cursor: 'pointer',
                                display: 'flex',
                                flexDirection:
                                  listStyle === 'list' ? 'row' : 'column',
                                alignItems: 'center',
                                overflow: 'hidden',
                                '&:hover': {
                                  boxShadow:
                                    listStyle === 'list'
                                      ? '0px solid'
                                      : '0px 6px 12px rgba(0,0,0,0.2)',
                                  transform: 'translateY(-3px)'
                                }
                              }}
                              onClick={() =>
                                navigate(`/apps/app-details/${data?.app_id}`)
                              }
                            >
                              <Box
                                sx={{
                                  width: listStyle === 'list' ? '60px' : '100%',
                                  height:
                                    listStyle === 'list' ? '60px' : '120px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#e9feff'
                                }}
                              >
                                {logo ? (
                                  <Box
                                    component="img"
                                    src={logo}
                                    alt={appName}
                                    sx={{
                                      width:
                                        listStyle === 'list' ? '50px' : '80px',
                                      height:
                                        listStyle === 'list' ? '50px' : '80px',
                                      objectFit: 'contain',
                                      borderRadius: '50%',
                                      backgroundColor: '#ffffff'
                                    }}
                                  />
                                ) : (
                                  <Box
                                    sx={{
                                      width:
                                        listStyle === 'list' ? '50px' : '80px',
                                      height:
                                        listStyle === 'list' ? '50px' : '80px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      borderRadius: '50%',
                                      backgroundColor: '#ffffff'
                                    }}
                                  >
                                    {placeholderIcon}
                                  </Box>
                                )}
                              </Box>
                              <Tooltip title={appName} arrow placement="bottom">
                                <Typography
                                  noWrap
                                  sx={{
                                    maxWidth: listStyle === 'list' ? 400 : 180,
                                    color: '#595959',
                                    fontWeight: 700,
                                    textAlign:
                                      listStyle === 'list' ? 'left' : 'center',
                                    padding:
                                      listStyle === 'list' ? '0 10px' : '15px',
                                    textTransform: 'capitalize',
                                    overflow:
                                      listStyle === 'list' ? 'none' : 'hidden',
                                    textOverflow:
                                      listStyle === 'list'
                                        ? 'none'
                                        : 'ellipsis',
                                    whiteSpace:
                                      listStyle === 'list' ? 'none' : 'nowrap'
                                  }}
                                >
                                  {appName}
                                </Typography>
                              </Tooltip>
                            </Box>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        )}
      </Box>
    </Shell>
  );
};

export default AppsList;
