// Global imports
import { Box } from '@mui/material';

// Local imports
import { useSelector } from 'react-redux';
import { SubMenu } from '../form.elements';
import Shell from '../layout/Shell';
import { CreateForm } from '../reusable/CreateForm';
import LoaderUI from '../reusable/loaderUI';
import { AppState } from '../../redux/app.store';

const FormDetails = ({ appData, id }: any) => {
  const { isLoading } = useSelector((state: AppState) => state.form);
  const getSubMenu = () => {
    return (
      <SubMenu backNavigation showFormFillOrder={{ status: true, appId: id }} />
    );
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ padding: '20px 100px' }}>
          <Box
            sx={{
              padding: '60px',
              backgroundColor: '#FBF9F9'
            }}
          >
            <CreateForm appId={id} appData={appData} />
          </Box>
        </Box>
      )}
    </Shell>
  );
};

export default FormDetails;
