import { useState, useEffect } from 'react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  useSortable,
  SortableContext,
  arrayMove,
  rectSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Box,
  Typography,
  Button,
  Grid,
  Tooltip,
  Switch,
  FormControlLabel,
  CardContent,
  Card,
  CardMedia
} from '@mui/material';
import { toast } from 'react-toastify';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, AppState } from '../../redux/app.store';
import { autocreate } from '../../redux/reducers/apps.reducer';
import Shell from '../layout/Shell';
import '../../css/sub-forms-styles.scss';
import { ParamsState } from '../../types';
import LoaderUI from '../reusable/loaderUI';
import { SubMenu } from '../form.elements';
import {
  toggleformslist,
  updateformslist
} from '../../redux/reducers/form.reducer';

const SortableItem = ({ id, form, handleClick }: any) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1
  };

  return (
    <Grid
      item
      xs={12}
      sm={6}
      md={4}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      <Card
        onClick={() => handleClick(form.form_id)}
        sx={{
          borderRadius: 1, // uses theme.shape.borderRadius
          boxShadow: isDragging ? '0px 8px 16px rgba(0,0,0,0.2)' : 1, // similar to theme.shadows[1]
          p: 0,
          width: '100%',
          height: 80,
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          position: 'relative',
          backgroundColor: 'background.paper',
          border: '1px solid transparent',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            backgroundColor: 'background.default',
            border: '1px solid',
            borderColor: 'primary.main',
            transform: 'scale(1.05)',
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
          }
        }}
      >
        <CardContent
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            pt: 2,
            width: '100%'
          }}
        >
          {form?.icon ? (
            <CardMedia
              component="img"
              image={form.icon} // Use the SVG data URL here
              alt={form?.name}
              sx={{
                width: '40px',
                height: '40px',
                backgroundColor: '#F9F9F9'
              }}
            />
          ) : (
            <AssignmentOutlinedIcon
              sx={{ color: 'text.secondary', fontSize: 30 }}
            />
          )}
          <Tooltip title={form?.name} arrow>
            <Typography
              sx={{
                fontSize: '18px',
                fontWeight: 600,
                color: 'text.secondary',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                width: 200
              }}
            >
              {form?.name}
            </Typography>
          </Tooltip>
        </CardContent>
      </Card>
    </Grid>
  );
};

const SubForms = () => {
  const { appId, formId } = useParams<ParamsState>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { subForms, formData, isLoading }: any = useSelector(
    (state: AppState) => state.app
  );
  const { userType }: any = useSelector((state: AppState) => state.auth);

  const [forms, setForms] = useState<any[]>(subForms?.sub_forms || []);
  const [dragEnabled, setDragEnabled] = useState(false);

  useEffect(() => {
    if (formData?.sub_forms) {
      setForms(subForms?.sub_forms);
    }
    if (formData?.toggle_forms_fill_order_status) {
      setDragEnabled(formData?.toggle_forms_fill_order_status);
    }
  }, [formData, subForms?.sub_forms]);

  const handleClickOpen = async () => {
    const data = {
      app_id: appId,
      is_sub_form: true,
      main_form_id: formId
    };

    try {
      const response = await dispatch(autocreate(data));
      if (response.payload.status) {
        // Optionally, you can toast a success message here.
      } else {
        toast.error(
          response?.payload?.error ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const handleClick = (formID: string) => {
    if (userType === 'super_admin') {
      navigate(`/apps/form-builder/edit-form/${formID}`);
    } else if (userType === 'organization') {
      navigate(`/form-builder/edit-form/${formID}`);
    }
    if (appId) {
      localStorage.setItem('app_id', appId);
    }
  };

  const handleDragEnd = async (event: any) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    setForms((items) => {
      const oldIndex = items.findIndex(
        (item: any) => item.form_id === active.id
      );
      const newIndex = items.findIndex((item: any) => item.form_id === over.id);
      return arrayMove(items, oldIndex, newIndex);
    });

    const updatedForms = arrayMove(
      forms,
      forms.findIndex((item: any) => item.form_id === active.id),
      forms.findIndex((item: any) => item.form_id === over.id)
    );

    const data = {
      forms: updatedForms.map((form, index) => ({
        form_id: form?.form_id,
        form_index: index
      }))
    };

    try {
      await dispatch(updateformslist({ appId, data }));
    } catch {
      toast.error('Something went wrong. Try again later.');
    }
  };

  const updateToggleStatus = async () => {
    try {
      const res = await dispatch(toggleformslist({ appId, formId }));
      if (!res?.payload?.status) {
        toast.error(
          res?.payload?.message || 'Something went wrong. Try again later.'
        );
      }
    } catch {
      toast.error('Something went wrong. Try again later.');
    }
  };

  const handleToggleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDragEnabled(event.target.checked);
  };

  const getSubMenu = () => {
    return <SubMenu backNavigation subformSettings formData={formData} />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="SubFrms-outer-box">
          <Box className="SubFrms-inner-box">
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Tooltip title={formData?.name} arrow>
                  <Typography className="SubFrms-form-title">
                    Sub Forms - {formData?.name}
                  </Typography>
                </Tooltip>
                <Box sx={{ padding: '15px' }} />
              </Box>
              {userType === 'organization' && (
                <FormControlLabel
                  control={
                    <Switch
                      checked={dragEnabled}
                      onChange={(e) => {
                        handleToggleChange(e);
                        updateToggleStatus();
                      }}
                    />
                  }
                  label="Enable Drag & Drop"
                />
              )}
            </Box>

            <Box className="SubFrms-forms-list-box">
              <Box className="SubFrms-form-item">
                <Typography
                  className="SubFrms-form-list-title"
                  sx={{ color: '#595959' }}
                >
                  Forms List
                </Typography>
                <Tooltip title="Number of subforms available" arrow>
                  <Typography className="SubFrms-form-list-number">
                    {forms.length}
                  </Typography>
                </Tooltip>
              </Box>
              <Box sx={{ padding: '0px' }}>
                <Tooltip title="Create a new subform" arrow>
                  <Button
                    size="medium"
                    sx={{
                      p: '10px 40px',
                      border: '2px solid',
                      backgroundColor: '#08366B',
                      color: 'white',
                      '&:hover': { backgroundColor: '#08363B' },
                      borderRadius: '50px'
                    }}
                    onClick={handleClickOpen}
                  >
                    <AddCircleOutlineIcon fontSize="small" />
                    Create
                  </Button>
                </Tooltip>
              </Box>
            </Box>

            <Box sx={{ pt: '10px' }}>
              {userType === 'organization' && dragEnabled ? (
                <DndContext
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={forms.map((form) => form.form_id)}
                    strategy={rectSortingStrategy}
                  >
                    <Grid
                      container
                      spacing={3}
                      sx={{
                        px: '90px',
                        py: '60px',
                        maxWidth: '100%',
                        mx: 'auto'
                      }}
                    >
                      {forms.map((form) => (
                        <SortableItem
                          key={form.form_id}
                          id={form.form_id}
                          form={form}
                          handleClick={handleClick}
                        />
                      ))}
                    </Grid>
                  </SortableContext>
                </DndContext>
              ) : (
                <Grid
                  container
                  spacing={3}
                  sx={{
                    px: '90px',
                    py: '60px',
                    maxWidth: '100%',
                    mx: 'auto'
                  }}
                >
                  {forms.map((form) => (
                    <Grid item xs={12} sm={6} md={4} key={form.form_id}>
                      <Card
                        onClick={() => handleClick(form.form_id)}
                        sx={{
                          borderRadius: 1,
                          boxShadow: 1,
                          p: 0,
                          width: '100%',
                          height: 80,
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          position: 'relative',
                          backgroundColor: 'background.paper',
                          border: '1px solid transparent',
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            backgroundColor: 'background.default',
                            border: '1px solid',
                            borderColor: 'primary.main',
                            transform: 'scale(1.05)',
                            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
                          }
                        }}
                      >
                        <CardContent
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 2,
                            pt: 2,
                            width: '100%'
                          }}
                        >
                          {form?.icon ? (
                            <CardMedia
                              component="img"
                              image={form.icon}
                              alt={form?.name}
                              sx={{
                                width: '40px',
                                height: '40px',
                                backgroundColor: '#F9F9F9'
                              }}
                            />
                          ) : (
                            <AssignmentOutlinedIcon
                              sx={{ color: 'text.secondary', fontSize: 30 }}
                            />
                          )}
                          <Tooltip title={form?.name} arrow>
                            <Typography
                              sx={{
                                fontSize: '18px',
                                fontWeight: 600,
                                color: 'text.secondary',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                width: 200
                              }}
                            >
                              {form?.name}
                            </Typography>
                          </Tooltip>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                  {forms?.length === 0 && (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        width: '100%'
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: '16px',
                          fontWeight: '500',
                          color: 'text.secondary'
                        }}
                      >
                        No Sub Forms Available
                      </Typography>
                    </Box>
                  )}
                </Grid>
              )}
            </Box>
          </Box>
        </Box>
      )}
    </Shell>
  );
};

export default SubForms;
