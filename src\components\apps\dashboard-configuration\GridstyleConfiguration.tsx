// Global imports
import {
  <PERSON>,
  Typography,
  Avatar,
  IconButton,
  Card,
  Grid,
  Button
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EmailIcon from '@mui/icons-material/Email';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { PersonPinCircleRounded } from '@mui/icons-material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Local imports
import { useSelector } from 'react-redux';
import { AppState } from '../../../redux/app.store';
import '../../../css/grid-style-configuration-styles.scss';
import LoaderUI from '../../reusable/loaderUI';

const GridstyleConfiguration = () => {
  const { dashboardConfigData, isLoading }: any = useSelector(
    (state: AppState) => state.app
  );
  const cardList = [
    {
      id: '1',
      name: 'test name 1',
      address: 'test address 1',
      phonenumber: '**************'
    },
    {
      id: '2',
      name: 'test name 2',
      address: 'test address 2',
      phonenumber: '**************'
    },
    {
      id: '3',
      name: 'test name 3',
      address: 'test address 3',
      phonenumber: '**************'
    },
    {
      id: '4',
      name: 'test name 4',
      address: 'test address 4',
      phonenumber: '**************'
    }
  ];
  return (
    <Box sx={{ p: '10px' }}>
      <Box className="Gridstyl-boxContainer">
        <Grid container className="Gridstyl-helpButtonContainer">
          <Grid item>
            <IconButton>
              <HelpOutlineIcon />
              <Typography variant="body2">Help</Typography>
            </IconButton>
          </Grid>
        </Grid>
        <Grid container className="Gridstyl-clientTextAlign">
          <Grid item>
            <Typography variant="h6" fontWeight="bold">
              CLIENTS LIST
            </Typography>
          </Grid>

          <Grid item>
            <Grid container alignItems="flex-end" spacing={2}>
              <Grid item>
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<PersonPinCircleRounded />}
                  className="Gridstyl-inquiry-btnStyles"
                >
                  New Inquiry
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      <Box className="Gridstyl-mainDataBox">
        {isLoading && <LoaderUI />}
        {!isLoading && (
          <Grid container spacing={4} className="Gridstyl-mainGridContainer">
            {dashboardConfigData?.length > 0
              ? dashboardConfigData?.map((card: any, index: any) => {
                  const keyIndex = `key-card${index}-${index * 3}`;
                  return (
                    <Grid item xs={5.7} key={keyIndex} mx={1.5}>
                      <Box className="Gridstyl-card-container">
                        <Box sx={{ width: '100%' }}>
                          <Box className="Gridstyl-avatar-box">
                            <Avatar
                              src="/broken-image.jpg"
                              className="Gridstyl-card-avatar"
                            />
                          </Box>
                          <Box className="Gridstyl-actionIcon-styles">
                            <Box className="Gridstyl-actionIcon-btn">
                              <IconButton>
                                <VisibilityIcon className="Gridstyl-actionIcon-color" />
                              </IconButton>
                            </Box>
                            <Box className="Gridstyl-actionIcon-btn">
                              <IconButton>
                                <EmailIcon className="Gridstyl-actionIcon-color" />
                              </IconButton>
                            </Box>
                            <Box className="Gridstyl-uploadIcon-btn">
                              <IconButton>
                                <CloudUploadIcon className="Gridstyl-actionIcon-color" />
                              </IconButton>
                            </Box>
                          </Box>
                          <Card className="Gridstyl-data-container">
                            <Box>
                              <Typography
                                variant="h6"
                                sx={{ fontWeight: 'bold', color: '#00ACC1' }}
                              >
                                {card.firstField}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: '#616161' }}
                              >
                                {card.secondField}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: '#616161' }}
                              >
                                {card.thirdField}
                              </Typography>
                            </Box>
                            <Typography
                              variant="body2"
                              className="Gridstyl-status-styles"
                            >
                              Pending
                            </Typography>
                          </Card>
                        </Box>
                      </Box>
                    </Grid>
                  );
                })
              : cardList.map((card: any) => (
                  <Grid item xs={12} sm={6} md={4} lg={6} key={card?.id}>
                    <Box className="Gridstyl-card-container">
                      <Box sx={{ width: '100%' }}>
                        <Box className="Gridstyl-avatar-box">
                          <Avatar
                            src="/broken-image.jpg"
                            className="Gridstyl-card-avatar"
                          />
                        </Box>
                        <Box className="Gridstyl-actionIcon-styles">
                          <Box className="Gridstyl-actionIcon-btn">
                            <IconButton>
                              <VisibilityIcon className="Gridstyl-actionIcon-color" />
                            </IconButton>
                          </Box>
                          <Box className="Gridstyl-actionIcon-btn">
                            <IconButton>
                              <EmailIcon className="Gridstyl-actionIcon-color" />
                            </IconButton>
                          </Box>
                          <Box className="Gridstyl-uploadIcon-btn">
                            <IconButton>
                              <CloudUploadIcon className="Gridstyl-actionIcon-color" />
                            </IconButton>
                          </Box>
                        </Box>
                        <Card className="Gridstyl-data-container">
                          <Box>
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 'bold', color: '#00ACC1' }}
                            >
                              {card.name}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: '#616161' }}
                            >
                              {card.address}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: '#616161' }}
                            >
                              {card.phonenumber}
                            </Typography>
                          </Box>
                          <Typography
                            variant="body2"
                            className="Gridstyl-status-styles"
                          >
                            Pending
                          </Typography>
                        </Card>
                      </Box>
                    </Box>
                  </Grid>
                ))}
          </Grid>
        )}
      </Box>
    </Box>
  );
};
export default GridstyleConfiguration;
