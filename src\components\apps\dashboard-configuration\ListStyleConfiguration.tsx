// Global imports
import {
  <PERSON>,
  Typography,
  Avatar,
  IconButton,
  Card,
  Grid,
  Button
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EmailIcon from '@mui/icons-material/Email';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { PersonPinCircleRounded } from '@mui/icons-material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Local imports
import { useSelector } from 'react-redux';
import { AppState } from '../../../redux/app.store';
import '../../../css/list-style-config-styles.scss';
import LoaderUI from '../../reusable/loaderUI';

const ListStyleConfiguration = () => {
  const { dashboardConfigData, isLoading }: any = useSelector(
    (state: AppState) => state.app
  );
  const cardList = [
    {
      id: '1',
      name: 'test name 1',
      address: 'test address 1',
      phonenumber: '**************'
    },
    {
      id: '2',
      name: 'test name 2',
      address: 'test address 2',
      phonenumber: '**************'
    },
    {
      id: '3',
      name: 'test name 3',
      address: 'test address 3',
      phonenumber: '**************'
    },
    {
      id: '4',
      name: 'test name 4',
      address: 'test address 4',
      phonenumber: '**************'
    }
  ];

  return (
    <Box sx={{ p: '10px' }}>
      <Box className="ListConfig-boxContainer">
        <Grid container className="ListConfig-helpButtonContainer">
          <Grid item>
            <IconButton>
              <HelpOutlineIcon />
              <Typography variant="body2">Help</Typography>
            </IconButton>
          </Grid>
        </Grid>
        <Grid container className="ListConfig-clientTextAlign">
          <Grid item>
            <Typography variant="h6" fontWeight="bold">
              CLIENTS LIST
            </Typography>
          </Grid>

          <Grid item>
            <Grid container alignItems="flex-end" spacing={2}>
              <Grid item>
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<PersonPinCircleRounded />}
                  className="ListConfig-inquiry-btnStyles"
                >
                  New Inquiry
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="ListConfig-cardsContainer">
          <Box className="ListConfig-cardsAlign">
            {dashboardConfigData?.length > 0
              ? dashboardConfigData?.map((card: any, index: any) => {
                  const keyIndex = `key-card${index}-${index * 3}`;
                  return (
                    <Box key={keyIndex} className="ListConfig-card-box">
                      <Card className="ListConfig-card-style">
                        <Avatar
                          src="/broken-image.jpg"
                          className="ListConfig-avatar-style"
                        />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{ fontWeight: 'bold', color: '#00ACC1' }}
                          >
                            {card.firstField}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#616161' }}>
                            {card.secondField}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#616161' }}>
                            {card.thirdField}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 1
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Box className="ListConfig-action-btn">
                              <IconButton>
                                <VisibilityIcon className="ListConfig-actionIcon-color" />
                              </IconButton>
                            </Box>
                            <Box className="ListConfig-action-btn">
                              <IconButton>
                                <EmailIcon className="ListConfig-actionIcon-color" />
                              </IconButton>
                            </Box>
                            <Box className="ListConfig-action-btn">
                              <IconButton>
                                <CloudUploadIcon className="ListConfig-actionIcon-color" />
                              </IconButton>
                            </Box>
                          </Box>
                          <Typography
                            variant="body2"
                            className="ListConfig-completed-txt"
                          >
                            Completed
                          </Typography>
                        </Box>
                      </Card>
                    </Box>
                  );
                })
              : cardList.map((card) => (
                  <Box key={card.id} className="ListConfig-card-box">
                    <Card className="ListConfig-card-style">
                      <Avatar
                        src="/broken-image.jpg"
                        className="ListConfig-avatar-style"
                      />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography
                          variant="h6"
                          sx={{ fontWeight: 'bold', color: '#00ACC1' }}
                        >
                          {card.name}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#616161' }}>
                          {card.address}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#616161' }}>
                          {card.phonenumber}
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1
                        }}
                      >
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Box className="ListConfig-action-btn">
                            <IconButton>
                              <VisibilityIcon className="ListConfig-actionIcon-color" />
                            </IconButton>
                          </Box>
                          <Box className="ListConfig-action-btn">
                            <IconButton>
                              <EmailIcon className="ListConfig-actionIcon-color" />
                            </IconButton>
                          </Box>
                          <Box className="ListConfig-action-btn">
                            <IconButton>
                              <CloudUploadIcon className="ListConfig-actionIcon-color" />
                            </IconButton>
                          </Box>
                        </Box>
                        <Typography
                          variant="body2"
                          className="ListConfig-completed-txt"
                        >
                          Completed
                        </Typography>
                      </Box>
                    </Card>
                  </Box>
                ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};
export default ListStyleConfiguration;
