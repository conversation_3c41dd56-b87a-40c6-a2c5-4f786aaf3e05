// Global imports
import {
  Box,
  Avatar,
  Button,
  IconButton,
  MenuItem,
  Select,
  Typography,
  FormControl
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EmailIcon from '@mui/icons-material/Email';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { useMemo, useState } from 'react';
import { toast } from 'react-toastify';

// Local imports
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  createdashboardconfig,
  updatedashboardconfig
} from '../../../redux/reducers/apps.reducer';
import '../../../css/pop-styles.scss';
// import { SnackbarElement } from '../../reusable/SnackbarElement';
// import { SnabackBarState } from '../../../types';

const SelectInputRound = ({
  changeEvent,
  render,
  name,
  value,
  groupedOptions
}: any) => {
  return (
    <div className="custom-selectstyles">
      <select
        id={name}
        className="inputSelectRound-styles form-control override-fc"
        name={name}
        value={value}
        onChange={changeEvent}
      >
        {Object.keys(groupedOptions).map((group, index) => {
          const keyIndex = `key-card${index}-${index * 3}`;
          return (
            <optgroup key={keyIndex} label={group}>
              {render(groupedOptions[group])}
            </optgroup>
          );
        })}
      </select>
    </div>
  );
};

const Popstyles = (props: any) => {
  const { appId } = useParams<{ appId: string }>();
  const dispatch = useDispatch<AppDispatch>();
  const { primaryForm, dashboardConfig }: any = useSelector(
    (state: AppState) => state.app
  );
  const [firstField, setFirstField] = useState();
  const [secondField, setSecondField] = useState();
  const [thirdField, setThirdField] = useState();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const onChange = (event: any) => {
    const { name, value } = event.target;
    if (name === 'first_field') {
      setFirstField(value);
    } else if (name === 'second_field') {
      setSecondField(value);
    } else if (name === 'third_field') {
      setThirdField(value);
    } else {
      props?.setStyle(value);
    }
  };
  const renderOptions = (options: any) => {
    return options.map((option: any) => {
      return (
        <option key={option.field_id} value={option.field_id}>
          {option.label}
        </option>
      );
    });
  };

  const save = async () => {
    try {
      const data = {
        listing_style: props?.style,
        form_id: primaryForm?.form_id,
        listing_details: {
          firstField,
          secondField,
          thirdField
        },
        avatar_field_details: ''
      };
      if (dashboardConfig) {
        const response = await dispatch(
          updatedashboardconfig({
            id: dashboardConfig?.configuration_id,
            data
          })
        );
        if (response.payload.status) {
          props?.setOpen(false);
        }
      } else {
        const response = await dispatch(
          createdashboardconfig({ id: appId, data })
        );
        if (response.payload.status) {
          props?.setOpen(false);
        }
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const groupedOptions: any = {};
  primaryForm?.groups?.forEach((option: any) => {
    if (!groupedOptions[option.group_title])
      groupedOptions[option.group_title] = option.fields;
  });

  useMemo(() => {
    if (dashboardConfig) {
      setFirstField(dashboardConfig.listing_details.firstField);
      setSecondField(dashboardConfig.listing_details.secondField);
      setThirdField(dashboardConfig.listing_details.thirdField);
    }
  }, [dashboardConfig]);
  return (
    <>
      {props?.style === 'List' ? (
        <>
          <Box className="popuplst-main-box">
            <Avatar className="popuplst-avatar-styles" />

            <Box sx={{ mx: 2, width: '350px' }}>
              <Box className="popuplst-input-align">
                <SelectInputRound
                  changeEvent={onChange}
                  render={renderOptions}
                  name="first_field"
                  value={firstField}
                  groupedOptions={groupedOptions}
                />
              </Box>
              <Box className="popuplst-input-align">
                <SelectInputRound
                  changeEvent={onChange}
                  render={renderOptions}
                  name="second_field"
                  value={secondField}
                  groupedOptions={groupedOptions}
                />
              </Box>
              <Box className="popuplst-input-align">
                <SelectInputRound
                  changeEvent={onChange}
                  render={renderOptions}
                  name="third_field"
                  value={thirdField}
                  groupedOptions={groupedOptions}
                />
              </Box>
            </Box>

            <Box className="popuplst-icons-box">
              <Box className="popup-icon-container">
                <IconButton>
                  <VisibilityIcon className="popup-icon-color" />
                </IconButton>
              </Box>
              <Box className="popup-icon-container">
                <IconButton>
                  <EmailIcon className="popup-icon-color" />
                </IconButton>
              </Box>
              <Box className="popup-uploadicon-style">
                <IconButton>
                  <CloudUploadIcon className="popup-icon-color" />
                </IconButton>
              </Box>
            </Box>

            <Box sx={{ width: '100%', textAlign: 'right' }}>
              <Typography
                variant="body2"
                color="primary"
                sx={{ marginTop: '-122px', padding: '0px 20px' }}
              >
                Completed
              </Typography>
            </Box>
          </Box>
          <Box className="popup-bottom-controls">
            <FormControl className="popup-form-control-style">
              <Select
                displayEmpty
                defaultValue=""
                value={props?.style}
                name="style"
                onChange={onChange}
                sx={{ height: '40px' }} // Ensures the select box height matches the FormControl height
              >
                <MenuItem value="Grid">Grid</MenuItem>
                <MenuItem value="List">List</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              sx={{ mr: 1, ml: 1 }}
              onClick={() => props?.setOpen(false)}
            >
              Cancel
            </Button>
            {dashboardConfig ? (
              <Button
                variant="contained"
                color="warning"
                sx={{ mr: 1 }}
                onClick={() => save()}
              >
                Update
              </Button>
            ) : (
              <Button
                variant="contained"
                color="warning"
                onClick={() => save()}
              >
                Save
              </Button>
            )}
          </Box>
        </>
      ) : (
        <Box sx={{ paddingTop: '70px' }}>
          <Box className="popup-inner-box">
            <Avatar className="popup-avatar-style" />

            <Box sx={{ flexGrow: 1, mx: 2, mt: 2 }}>
              <Box className="popup-select-box">
                <SelectInputRound
                  changeEvent={onChange}
                  render={renderOptions}
                  name="first_field"
                  value={firstField}
                  groupedOptions={groupedOptions}
                />
              </Box>

              <Box className="popup-select-box">
                <SelectInputRound
                  changeEvent={onChange}
                  render={renderOptions}
                  name="second_field"
                  value={secondField}
                  groupedOptions={groupedOptions}
                />
              </Box>

              <Box className="popup-select-box">
                <SelectInputRound
                  changeEvent={onChange}
                  render={renderOptions}
                  name="third_field"
                  value={thirdField}
                  groupedOptions={groupedOptions}
                />
              </Box>
            </Box>

            <Box className="popup-icon-box">
              <Box className="popup-icon-container">
                <IconButton>
                  <VisibilityIcon className="popup-icon-color" />
                </IconButton>
              </Box>
              <Box className="popup-icon-container">
                <IconButton>
                  <EmailIcon className="popup-icon-color" />
                </IconButton>
              </Box>
              <Box className="popup-uploadicon-style">
                <IconButton>
                  <CloudUploadIcon className="popup-icon-color" />
                </IconButton>
              </Box>
            </Box>

            <Typography
              variant="body2"
              color="primary"
              className="popup-completed-text"
            >
              Completed
            </Typography>
          </Box>

          <Box className="popup-bottom-controls">
            <FormControl className="popup-form-control-style">
              <Select
                displayEmpty
                defaultValue=""
                value={props?.style}
                name="style"
                onChange={onChange}
                className="popup-button-heights" // Ensures the select box height matches the FormControl height
              >
                <MenuItem value="Grid">Grid</MenuItem>
                <MenuItem value="List">List</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              className="popup-button-heights"
              onClick={() => props?.setOpen(false)}
            >
              Cancel
            </Button>
            {dashboardConfig ? (
              <Button
                variant="contained"
                color="warning"
                className="popup-button-heights"
                onClick={() => save()}
              >
                Update
              </Button>
            ) : (
              <Button
                variant="contained"
                color="warning"
                className="popup-button-heights"
                onClick={() => save()}
              >
                Save
              </Button>
            )}
          </Box>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default Popstyles;
