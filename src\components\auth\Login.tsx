// Global imports
import * as Yup from 'yup';
// import { useState } from 'react';
import { Box, Card, Grid } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { FormikValues } from 'formik';
import { toast } from 'react-toastify';

// Local imports
import '../../css/login-styles.scss';
import { AppDispatch } from '../../redux/app.store';
import { postLoginForm, userValidate } from '../../redux/reducers/auth.reducer';
import {
  AppForm,
  FormInput,
  // Loader,
  SubmitButton
} from '../form.elements';
import { RootState } from '../../redux/reducers';
import Imglogo from '../../assets/fab-logo.png';
import { PasswordInput } from '../reusable/PasswordInput';
// import { SnackbarElement } from '../reusable/SnackbarElement';
// import LoaderUI from '../reusable/loaderUI';

const LoginCard = ({ type }: { type: string }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: RootState) => state.auth);

  const inputValidation = {
    EmailAddress: '',
    UserPassword: ''
  };

  const validationSchema = Yup.object().shape({
    EmailAddress: Yup.string()
      .email('Invalid email format')
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        'Please enter a valid email address'
      )
      .required('Email is required'),
    UserPassword: Yup.string().required('Password is required')
  });

  const handelSubmit = async (values: FormikValues) => {
    const loginRoute =
      type === 'super_admin' ? '/login' : '/organization/login';
    localStorage.setItem('login_route', loginRoute);

    try {
      const form = {
        username: values.EmailAddress,
        password: values.UserPassword
      };

      const { payload } = await dispatch(postLoginForm(form));

      if (payload) {
        const { statusCode, error, message } = payload;
        if (statusCode === 401 || error) {
          toast.error(message);
        } else {
          toast.success('Login successful!');
        }

        if (loginRoute === '/organization/login') {
          dispatch(userValidate(null));
        }
      }
    } catch (error: any) {
      toast.error(error?.message || 'Invalid credentials');
    }
  };

  return (
    <AppForm
      initialValues={inputValidation}
      validationSchema={validationSchema}
      onSubmit={handelSubmit}
    >
      <Box className="lgn-main-container-box">
        <Grid container spacing={2} className="lgn-grid-container">
          <Grid item xs={5}>
            {/* <Card className="formStyles"> */}
            <Card className="lgn-card-style">
              <Box>
                <Box className="lgn-form-logo">
                  <img src={Imglogo} alt="loginImg" className="lgn-logo-img" />
                </Box>
                <Box
                  sx={{
                    marginBottom: '4px'
                  }}
                >
                  <Box className="lgn-login-field">
                    <FormInput
                      id="EmailAddress"
                      name="EmailAddress"
                      label="Email Address"
                      required
                      placeholder="Enter Email Address"
                      containerStyles={{
                        width: '100%',
                        '& .MuiInputBase-formControl': {
                          marginTop: '4px'
                        }
                      }}
                      autoComplete="off"
                    />
                  </Box>
                </Box>
                <Box
                  className="lgn-login-field"
                  sx={{
                    position: 'relative'
                  }}
                >
                  <PasswordInput
                    name="UserPassword"
                    label="Password"
                    required
                  />
                </Box>
                <Box className="lgn-button-container">
                  <SubmitButton title="Login" isLoading={isLoading} />
                </Box>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </AppForm>
  );
};
export default LoginCard;
