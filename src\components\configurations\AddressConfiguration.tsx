// Global Imports
import { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio
} from '@mui/material';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import { AppForm, FormInput, SubmitButton } from '../form.elements';
import ConfigurationShell from './ConfigurationShell';
import {
  createstorageconfig,
  updatestorageconfig
} from '../../redux/reducers/addressconfig.reducer';
import { AppDispatch } from '../../redux/app.store';
import { RootState } from '../../redux/reducers';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import '../../css/configuration.scss';
// import { SnabackBarState } from '../../types';
import LoaderUI from '../reusable/loaderUI';

const AddressConfiguration = () => {
  const dispatch = useDispatch<AppDispatch>();

  const { addressConfig, addressConfigValues, isLoading }: any = useSelector(
    (state: RootState) => state.addressconfig
  );

  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });

  const [radioValue, setRadioValue] = useState(0);
  const [addressConfigurationDetails, setAddressConfigurationDetails] =
    useState<any>(addressConfigValues);

  // To change Radio type
  const handleRadioChange = (event: any) => {
    setRadioValue(Number(event.target.value));
    if (
      (addressConfig?.name === 'google' && Number(event.target.value) === 0) ||
      (addressConfig?.name === 'open_weather' &&
        Number(event.target.value) === 1)
    ) {
      setAddressConfigurationDetails({
        apiKey: addressConfig?.details.apiKey,
        hostUrl: addressConfig?.details.hostUrl
      });
    } else {
      setAddressConfigurationDetails({
        apiKey: '',
        hostUrl: ''
      });
    }
  };

  const validationSchema = Yup.object().shape({
    apiKey: Yup.string().required('API KEY is required'),
    hostUrl: Yup.string().required('HOST URL is required')
  });

  // Submit form Data to API
  const handleSubmit = async (event: any) => {
    const data = {
      configuration_id: addressConfig ? addressConfig?.configuration_id : '',
      name: radioValue === 0 ? 'google' : 'open_weather',
      type: 'address',
      details: event
    };

    if (addressConfig?.configuration_id) {
      try {
        const response = await dispatch(
          updatestorageconfig({
            id: addressConfig?.configuration_id,
            data
          })
        );
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        } else if (response.payload.status) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    } else {
      try {
        const response = await dispatch(createstorageconfig(data));

        if (response.payload.status) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message: 'Something went wrong!',
          //   type: 'error'
          // });
          toast.error('Something went wrong!');
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  };

  useEffect(() => {
    setAddressConfigurationDetails({
      apiKey: addressConfig?.details?.apiKey,
      hostUrl: addressConfig?.details?.hostUrl
    });

    setRadioValue(addressConfig?.name === 'open_weather' ? 1 : 0);
  }, [addressConfig]);

  return (
    <ConfigurationShell>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="ac-container">
          <Box sx={{ width: '100%', typography: 'body1' }}>
            <Box sx={{ padding: '0px 30px' }}>
              <Typography variant="h6" gutterBottom className="addr-config">
                ADDRESS CONFIGURATION
              </Typography>
              <RadioGroup
                aria-label="address configuration"
                name="address-config-radio-group"
                value={radioValue}
                onChange={handleRadioChange}
                row
              >
                <FormControlLabel
                  value={0}
                  control={<Radio />}
                  label="Google"
                />
                <FormControlLabel
                  value={1}
                  control={<Radio />}
                  label="Open Weather"
                />
              </RadioGroup>
            </Box>
            <AppForm
              key={radioValue}
              initialValues={addressConfigurationDetails}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              <Box sx={{ p: 3 }}>
                <Box sx={{ marginBottom: 2 }}>
                  <Box className="form-box">
                    <Box className="field-input">
                      <FormInput
                        name="apiKey"
                        label="API KEY"
                        required
                        placeholder=" "
                        containerStyles={{
                          width: {
                            xs: '100%'
                          }
                        }}
                      />
                    </Box>
                    <Box style={{ width: '100%' }}>
                      <Box className="field-input">
                        <FormInput
                          name="hostUrl"
                          label="HOST URL"
                          required
                          placeholder=" "
                          type="text"
                          containerStyles={{
                            width: {
                              xs: '100%'
                            }
                          }}
                        />
                      </Box>
                    </Box>
                    <Box>
                      <SubmitButton
                        title={
                          addressConfig?.configuration_id ? 'UPDATE' : 'SAVE'
                        }
                        sx={{
                          backgroundColor: 'primaryBlue.main',
                          color: 'white2.main',
                          padding: '10px 30px',
                          boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                          '&:hover': {
                            color: 'white2.main',
                            backgroundColor: 'primaryBlue.main'
                          }
                        }}
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>
            </AppForm>
          </Box>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen?.message}
        statusType={snackbarOpen?.type || 'success'}
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </ConfigurationShell>
  );
};

export default AddressConfiguration;
