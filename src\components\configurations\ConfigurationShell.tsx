import { Box } from '@mui/material';

import { useSelector } from 'react-redux';
import { SubMenu } from '../form.elements';
import Sidebar from '../reusable/Sidebar';
import Shell from '../layout/Shell';
import { AppState } from '../../redux/app.store';
import LoaderUI from '../reusable/loaderUI';

const ConfigurationShell = ({ children }: any) => {
  const { isLoading }: any = useSelector(
    (state: AppState) => state.addressconfig
  );
  const menuItems = [
    {
      name: 'Address Configuration',
      url: '/configurations/address-configuration'
    },
    {
      name: 'Storage Configuration',
      url: '/configurations/storage-configuration'
    },
    { name: 'Email Configuration', url: '/configurations/email-configuration' },
    { name: 'Pdf Configuration', url: '/configurations/pdf-configuration' }
  ];
  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };
  const getDrawer = () => {
    return (
      <Sidebar
        menuItems={menuItems}
        // eslint-disable-next-line react/no-children-prop
        children={undefined}
        userType={undefined}
      />
    );
  };
  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ background: '#FAF9F8' }}>
          <Box sx={{ display: 'flex', background: '#FAF9F8' }}>{children}</Box>
        </Box>
      )}
    </Shell>
  );
};
export default ConfigurationShell;
