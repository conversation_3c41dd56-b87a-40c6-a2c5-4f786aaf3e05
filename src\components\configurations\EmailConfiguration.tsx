// Global Imports
import {
  Box,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography
} from '@mui/material';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import ConfigurationShell from './ConfigurationShell';
import { AppForm, FormInput } from '../form.elements';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  createstorageconfig,
  getemailconfig,
  updatestorageconfig
} from '../../redux/reducers/addressconfig.reducer';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import '../../css/configuration.scss';
// import { SnabackBarState } from '../../types';
import LoaderUI from '../reusable/loaderUI';

const validationSchema = Yup.object({
  apiKey: Yup.string().required('API Key is required'),
  fromEmail: Yup.string()
    .email('Invalid email')
    .required('From Email is required')
});

const EmailConfiguration = () => {
  const [selectedStorage, setSelectedStorage] = useState('Send Grid');
  const dispatch = useDispatch<AppDispatch>();
  const { emailConfig, emailConfigValues, isLoading }: any = useSelector(
    (state: AppState) => state.addressconfig
  );

  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });

  const handleStorageChange = (event: any) => {
    setSelectedStorage(event.target.value);
  };

  const getEmailDetails = async () => {
    try {
      const response = await dispatch(getemailconfig(null));
      if (response.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.error,
        //   type: 'error'
        // });
        toast.error(response.payload?.error);
      }
      //  else if (response.payload.status) {
      //   // setSnackbarOpen({
      //   //   status: true,
      //   //   message: response.payload?.message,
      //   //   type: 'success'
      //   // });
      // toast.success(response.payload?.message);
      // }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message: error?.message,
      //   type: 'error'
      // });
      toast.error(error?.message);
    }
  };

  const handleSubmit = async (event: any) => {
    const data = {
      configuration_id: emailConfig ? emailConfig?.configuration_id : '',
      name: 'send-grid',
      type: 'email',
      details: event
    };

    if (emailConfig?.configuration_id) {
      try {
        const response = await dispatch(
          updatestorageconfig({
            id: emailConfig?.configuration_id,
            data
          })
        );
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        } else if (response.payload.status) {
          getEmailDetails();
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    } else {
      try {
        const response = await dispatch(createstorageconfig(data));
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        } else if (response.payload.status) {
          getEmailDetails();
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  };

  useEffect(() => {
    getEmailDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ConfigurationShell>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="sc-conatiner">
          <Typography sx={{ fontWeight: '600', fontSize: '24px' }}>
            EMAIL CONFIGURATION
          </Typography>
          <RadioGroup
            value={selectedStorage}
            onChange={handleStorageChange}
            sx={{ display: 'flex', padding: '10px' }}
          >
            <Box sx={{ padding: '5px 0px' }}>
              <FormControlLabel
                value="Send Grid"
                control={<Radio />}
                label="Send Grid"
              />
            </Box>
          </RadioGroup>

          <Box className="form-box">
            {selectedStorage === 'Send Grid' && (
              <AppForm
                initialValues={emailConfigValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
              >
                <>
                  <FormInput
                    name="apiKey"
                    label="API KEY"
                    required
                    placeholder=" "
                    type="text"
                    containerStyles={{
                      width: {
                        xs: '100%'
                      }
                    }}
                  />

                  <FormInput
                    name="fromEmail"
                    label="From Email"
                    required
                    placeholder=" "
                    type="text"
                    containerStyles={{
                      width: {
                        xs: '100%'
                      }
                    }}
                  />

                  <LoadingButton
                    type="submit"
                    title={emailConfig?.configuration_id ? 'Update' : 'Save'}
                    sx={{
                      backgroundColor: 'primaryBlue.main',
                      color: 'white2.main',
                      padding: '10px 30px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      '&:hover': {
                        color: 'white2.main',
                        backgroundColor: 'primaryBlue.main'
                      },
                      alignItems: 'flex-end'
                    }}
                  >
                    {emailConfig?.configuration_id ? 'Update' : 'Save'}
                  </LoadingButton>
                </>
              </AppForm>
            )}
          </Box>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen?.message}
        statusType={snackbarOpen?.type || 'success'}
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </ConfigurationShell>
  );
};

export default EmailConfiguration;
