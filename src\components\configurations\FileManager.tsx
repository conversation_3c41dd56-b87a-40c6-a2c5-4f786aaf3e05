// Global Imports
import { useMemo, useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  Grid,
  Tooltip,
  Typography
} from '@mui/material';
import SelectedFolderIcon from '@mui/icons-material/FolderOpen';
import UnselectedFolderIcon from '@mui/icons-material/Folder';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import { ActiveSelectionBorderSvg } from '../form.elements';
import { AppDispatch, AppState } from '../../redux/app.store';
import { selectonedrivefolder } from '../../redux/reducers/addressconfig.reducer';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import '../../css/configuration.scss';
// import { SnabackBarState } from '../../types';
import LoaderUI from '../reusable/loaderUI';

const FileManager = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    oneDriveFolders,
    oneDriveConfigValues,
    storageConfig,
    googleConfigValues,
    isLoading
  }: any = useSelector((state: AppState) => state.addressconfig);

  const [folders, setFolders] = useState([]);
  const [errMessage, setErrMessage] = useState('');

  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });

  const handleSelect = (name: string) => {
    setFolders((prevFolders: any) =>
      prevFolders.map((folder: any) =>
        folder?.name === name || folder?.folder_name === name
          ? { ...folder, isSelected: true }
          : { ...folder, isSelected: false }
      )
    );
  };

  // Handle save action
  const handleSave = async () => {
    const selectedFolder: any = folders.find(
      (folder: any) => folder.isSelected
    );

    if (selectedFolder) {
      const data =
        storageConfig?.name === 'one_drive'
          ? {
              name: 'one_drive',
              type: 'storage',
              details: {
                clientId: oneDriveConfigValues.clientId,
                tenentId: oneDriveConfigValues.tenentId,
                secretValue: oneDriveConfigValues.secretValue,
                backendRedirectUrl: oneDriveConfigValues.backendRedirectUrl,
                folder_name: selectedFolder.folder_name,
                folder_id: selectedFolder.item_id,
                drive_id: selectedFolder.drive_id
              }
            }
          : {
              name: 'google_drive',
              type: 'storage',
              details: {
                clientId: googleConfigValues.clientId,
                secretKey: googleConfigValues.secretKey,
                backendRedirectUrl: googleConfigValues.backendRedirectUrl,
                folderName: selectedFolder.name,
                folderId: selectedFolder.id
              }
            };
      try {
        const response = await dispatch(
          selectonedrivefolder({
            configuration_id: storageConfig.configuration_id,
            data
          })
        );
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        } else if (response.payload.status) {
          window.close();
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    } else {
      setErrMessage('Please select folder.');
    }
  };

  useMemo(() => {
    if (oneDriveFolders && storageConfig?.details) {
      const folderId =
        storageConfig.details.folder_id || storageConfig.details.folderId;

      setFolders(
        oneDriveFolders.map((folder: any) => {
          const folderItemId = folder.item_id || folder.id;
          return folderItemId === folderId
            ? { ...folder, isSelected: true }
            : { ...folder, isSelected: false };
        })
      );
    } else {
      setFolders(
        oneDriveFolders.map((folder: any) => ({ ...folder, isSelected: false }))
      );
    }
  }, [oneDriveFolders, storageConfig]);

  return (
    <Box sx={{ padding: '10px 120px' }}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="fm-container">
          <Box>
            <Box className="choose-driver-box">
              <Typography variant="h6" className="typography">
                Choose Drive Folder
              </Typography>
            </Box>
            <Box>
              <Divider sx={{ width: '100%', height: '1px' }} />
            </Box>
          </Box>

          <Box className="main-container">
            <Box className="folder-list">
              <Grid
                container
                spacing={2}
                sx={{ height: '420px', overflow: 'auto' }}
              >
                {folders?.map((folder: any, index: any) => {
                  const key = `folders-${folder.folder_name}-key-${index}`;
                  return (
                    <Grid item xs={2.2} key={key} sx={{ marginBottom: 4 }}>
                      {folder.isSelected ? (
                        <ActiveSelectionBorderSvg
                          title={
                            folder.folder_name
                              ? folder.folder_name
                              : folder.name
                          }
                          icon="Folder"
                        />
                      ) : (
                        <Tooltip
                          title={
                            folder.folder_name
                              ? folder.folder_name
                              : folder.name
                          }
                          arrow
                        >
                          <Card
                            onClick={() =>
                              handleSelect(
                                folder.folder_name
                                  ? folder.folder_name
                                  : folder.name
                              )
                            }
                            className="card"
                          >
                            <CardContent>
                              <Box
                                className="cardContentStyles"
                                style={{
                                  textAlign: 'center',
                                  transition:
                                    'transform 0.3s ease, color 0.3s ease'
                                }}
                              >
                                {folder.isSelected ? (
                                  <SelectedFolderIcon className="selected-folder-icon" />
                                ) : (
                                  <UnselectedFolderIcon className="unselected-folder-icon" />
                                )}
                                <Typography
                                  className="folder-name-typography"
                                  sx={{
                                    color: folder.isSelected
                                      ? '#3f51b5'
                                      : '#616161'
                                  }}
                                >
                                  {folder.folder_name
                                    ? folder.folder_name
                                    : folder.name}
                                </Typography>
                              </Box>
                            </CardContent>
                          </Card>
                        </Tooltip>
                      )}
                    </Grid>
                  );
                })}
              </Grid>
            </Box>
            <p color="danger">{errMessage}</p>
            <Box className="button-box">
              <Button variant="contained" color="primary" onClick={handleSave}>
                Save
              </Button>
            </Box>
          </Box>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen.message}
        statusType={snackbarOpen.type || 'success'}
        snackbarOpen={snackbarOpen.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Box>
  );
};
export default FileManager;
