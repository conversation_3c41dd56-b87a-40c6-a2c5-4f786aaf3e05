// Global Imports
import { useEffect, useState } from 'react';
import {
  Box,
  Button,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography
} from '@mui/material';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import ConfigurationShell from './ConfigurationShell';
import { AppForm, FormInput, SubmitButton } from '../form.elements';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  createstorageconfig,
  getlinkdata,
  getstorageconfig,
  updatestorageconfig
} from '../../redux/reducers/addressconfig.reducer';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import '../../css/configuration.scss';
// import { SnabackBarState } from '../../types';
import LoaderUI from '../reusable/loaderUI';

const StorageConfiguration = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    storageConfig,
    googleConfigValues,
    oneDriveConfigValues,
    isLoading
  }: any = useSelector((state: AppState) => state.addressconfig);

  const [selectedStorage, setSelectedStorage] = useState('Google Drive');

  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });
  const googleValidationSchema = Yup.object({
    clientId: Yup.string().required('Client Id Is Required'),
    secretKey: Yup.string().required('Secret Key Is Required')
  });

  const oneDriveValidationSchema = Yup.object({
    clientId: Yup.string().required('Client Id Is Required'),
    tenentId: Yup.string().required('Tenent Id Is Required'),
    secretValue: Yup.string().required('Secret Value Is Required')
  });

  // Get Storage Details
  const getStorageDetails = async () => {
    try {
      const response = await dispatch(getstorageconfig(null));
      if (response.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.error,
        //   type: 'error'
        // });
        toast.error(response.payload?.error);
      }
      // else if (response.payload.status) {
      //   // setSnackbarOpen({
      //   //   status: true,
      //   //   message: response.payload?.message,
      //   //   type: 'success'
      //   // });
      //   toast.success(response.payload?.message);
      // }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  // handle save
  const handleSubmit = async (event: any) => {
    const data = {
      configuration_id: storageConfig ? storageConfig?.configuration_id : '',
      name: selectedStorage === 'Google Drive' ? 'google_drive' : 'one_drive',
      type: 'storage',
      details:
        selectedStorage === 'Google Drive'
          ? {
              backendRedirectUrl: `${window.location.protocol}//${window.location.host}/google-drive/callback`,
              clientId: event.clientId,
              secretKey: event.secretKey
            }
          : {
              backendRedirectUrl: `${window.location.protocol}//${window.location.host}/one-drive/redirect`,
              clientId: event.clientId,
              secretValue: event.secretValue,
              tenentId: event.tenentId
            }
    };

    try {
      if (storageConfig?.configuration_id) {
        const response = await dispatch(
          updatestorageconfig({
            id: storageConfig?.configuration_id,
            data
          })
        );
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        } else if (response.payload.status) {
          getStorageDetails();
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        }
      } else {
        const response = await dispatch(createstorageconfig(data));
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        } else if (response.payload.status) {
          getStorageDetails();
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          // toast.success(response.payload?.message);
        }
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const handleStorageChange = (event: any) => {
    setSelectedStorage(event.target.value);
  };

  // get link data
  const getLink = async () => {
    try {
      const response = await dispatch(getlinkdata(null));
      if (response.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.error,
        //   type: 'error'
        // });
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        window.close();
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.message,
        //   type: 'success'
        // });
        // toast.success(response.payload?.message);
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  useEffect(() => {
    getStorageDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setSelectedStorage(
      storageConfig?.name === 'one_drive' ? 'One Drive' : 'Google Drive'
    );
  }, [storageConfig]);

  return (
    <ConfigurationShell>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="sc-conatiner" sx={{}}>
          <Box>
            <Typography className="storage-typography">
              STORAGE CONFIGURATION
            </Typography>
            <RadioGroup
              value={selectedStorage}
              onChange={handleStorageChange}
              sx={{ display: 'flex', padding: '10px' }}
            >
              <Box sx={{ display: 'flex', padding: '5px 0px' }}>
                <FormControlLabel
                  value="Google Drive"
                  control={<Radio />}
                  label="Google Drive"
                />
                <FormControlLabel
                  value="One Drive"
                  control={<Radio />}
                  label="One Drive"
                />
              </Box>
            </RadioGroup>
            <Box className="form-box">
              {selectedStorage === 'Google Drive' && (
                <AppForm
                  key={selectedStorage}
                  initialValues={googleConfigValues}
                  validationSchema={googleValidationSchema}
                  onSubmit={handleSubmit}
                >
                  <>
                    <FormInput
                      name="clientId"
                      label=" Client Id"
                      required
                      placeholder=""
                      type="text"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                    <FormInput
                      name="secretKey"
                      label=" Secret Key"
                      required
                      placeholder=" "
                      type="text"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                    <Box sx={{ display: 'flex' }}>
                      <Box sx={{ padding: '10px' }}>
                        <Button
                          sx={{
                            background: '#D6DADE',
                            padding: '10px',
                            fontSize: '14px'
                          }}
                          disabled={!storageConfig}
                          onClick={getLink}
                        >
                          Get Link
                        </Button>
                      </Box>
                      <Box sx={{ padding: '10px' }}>
                        <Box>
                          <SubmitButton
                            title={
                              storageConfig?.configuration_id
                                ? 'Update'
                                : 'Save'
                            }
                            sx={{
                              backgroundColor: 'primaryBlue.main',
                              color: 'white2.main',
                              padding: '10px 30px',
                              boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                              '&:hover': {
                                color: 'white2.main',
                                backgroundColor: 'primaryBlue.main'
                              }
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  </>
                </AppForm>
              )}

              {selectedStorage === 'One Drive' && (
                <AppForm
                  key={selectedStorage}
                  initialValues={oneDriveConfigValues}
                  validationSchema={oneDriveValidationSchema}
                  onSubmit={handleSubmit}
                >
                  <>
                    <FormInput
                      name="tenentId"
                      label="Tenent Id"
                      required
                      placeholder=" "
                      type="text"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                    <FormInput
                      name="clientId"
                      label="Client Id"
                      required
                      placeholder=" "
                      type="text"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                    <FormInput
                      name="secretValue"
                      label="Secret Value"
                      required
                      placeholder=" "
                      type="text"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                    <Box sx={{ display: 'flex' }}>
                      <Box sx={{ padding: '10px' }}>
                        <Button
                          sx={{
                            background: '#D6DADE',
                            padding: '10px',
                            fontSize: '14px'
                          }}
                          disabled={!storageConfig}
                          onClick={getLink}
                        >
                          Get Link
                        </Button>
                      </Box>
                      <Box sx={{ padding: '10px' }}>
                        <Box>
                          <SubmitButton
                            title={
                              storageConfig?.configuration_id
                                ? 'Update'
                                : 'Save'
                            }
                            sx={{
                              backgroundColor: 'primaryBlue.main',
                              color: 'white2.main',
                              padding: '10px 30px',
                              boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                              '&:hover': {
                                color: 'white2.main',
                                backgroundColor: 'primaryBlue.main'
                              }
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  </>
                </AppForm>
              )}
            </Box>
          </Box>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen?.message}
        statusType={snackbarOpen?.type || 'success'}
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </ConfigurationShell>
  );
};
export default StorageConfiguration;
