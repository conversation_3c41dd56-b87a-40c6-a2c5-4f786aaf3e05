// Global Imports
import { Box, Typography, Tooltip } from '@mui/material';

// Local Imports
import '../../css/dashboard/activeUsers.scss';

const ActiveUsers: React.FC = () => {
  return (
    <Box className="ActvUsr-container">
      <Box className="ActvUsr-header">
        <Typography className="ActvUsr-title">Active Users</Typography>
      </Box>
      <Box className="ActvUsr-content">
        <Box className="ActvUsr-imageBox">
          <Tooltip title="Click for more information" placement="top" arrow>
            <Box className="ActvUsr-imageInnerBox" />
          </Tooltip>
        </Box>
      </Box>
      <Box className="ActvUsr-userIcons">
        <Tooltip title="User 1" placement="top" arrow>
          <Box className="ActvUsr-user">
            <Box className="ActvUsr-userIconWrapper">
              <Box className="ActvUsr-userIcon" />
            </Box>
            <Box>
              <Typography className="ActvUsr-userText">Text</Typography>
            </Box>
          </Box>
        </Tooltip>
        <Tooltip title="User 2" placement="top" arrow>
          <Box className="ActvUsr-user">
            <Box className="ActvUsr-userIconWrapper">
              <Box className="ActvUsr-userIcon" />
            </Box>
            <Box>
              <Typography className="ActvUsr-userText">Text</Typography>
            </Box>
          </Box>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default ActiveUsers;
