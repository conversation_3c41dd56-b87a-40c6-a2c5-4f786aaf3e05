// Global Imports
import {
  <PERSON>,
  Typography,
  Grid,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { useSelector } from 'react-redux';
import { useState, useEffect } from 'react';
import ArrowCircleRightIcon from '@mui/icons-material/ArrowCircleRight';
import { useNavigate } from 'react-router-dom';

// Local Imports
import { RootState } from '../../redux/reducers';
import { Icon } from '../form.elements';
import '../../css/dashboard/appList.scss';

const AppList: React.FC = () => {
  const navigate = useNavigate();
  const { apps } = useSelector((state: RootState) => state.dashBoard);
  const { userType } = useSelector((state: RootState) => state.auth);
  const [loading, setLoading] = useState<boolean>(true);
  const [showMore] = useState<boolean>(false);

  const appsPerRow = 5;
  const rowsToShow = 2;
  const totalAppsToShow = appsPerRow * rowsToShow;

  const appIcons = ['Widgets', 'Dashboard', 'ViewModule', 'ViewComfy'] as const;

  type AppIconName = (typeof appIcons)[number];

  const iconColors = [
    '#9E9E9E' // Grey
  ];

  useEffect(() => {
    if (apps) {
      setLoading(false);
    }
  }, [apps]);

  const renderApps = () => {
    let appsToDisplay = [];

    if (apps && apps.length > 0) {
      appsToDisplay = showMore ? apps : apps?.slice(0, totalAppsToShow);
    }

    if (!appsToDisplay || appsToDisplay.length === 0) {
      return (
        <Typography sx={{ textAlign: 'center', marginTop: '1rem' }}>
          No apps available.
        </Typography>
      );
    }
    return appsToDisplay.map((app: any, index: number) => {
      const logoSrc = app?.orgAppConfiguration?.logo || null;
      const iconName: AppIconName = appIcons[index % appIcons.length];
      const iconColor = iconColors[index % iconColors.length];

      return (
        <Grid item xs={12} sm={6} md={4} lg={2.4} key={app?.id}>
          <Grid item key={app?.id} xs={3}>
            <Box
              sx={{
                width: '170px',
                height: '170px',
                borderRadius: '16px',
                background: '#fff',
                boxShadow: '0px 4px 8px rgba(0,0,0,0.15)',
                transition: '0.3s ease-in-out',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                overflow: 'hidden'
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  height: '120px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#e9feff'
                }}
              >
                {userType?.toLowerCase() !== 'super_admin' && logoSrc ? (
                  <Box
                    component="img"
                    src={logoSrc}
                    alt={app.name}
                    sx={{
                      width: '80px',
                      height: '80px',
                      objectFit: 'contain',
                      borderRadius: '50%',
                      backgroundColor: '#ffffff'
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      width: '80px',
                      height: '80px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: '50%',
                      backgroundColor: '#ffffff'
                    }}
                  >
                    <Icon
                      name={iconName}
                      sx={{ fontSize: '3rem', color: iconColor }}
                    />
                  </Box>
                )}
              </Box>
              <Tooltip
                title={app?.orgAppConfiguration?.app_name || app.name}
                placement="bottom"
                arrow
                PopperProps={{
                  sx: {
                    marginBottom: '4px'
                  }
                }}
              >
                <Typography
                  sx={{
                    fontWeight: 700,
                    textAlign: 'center',
                    padding: '15px',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textTransform: 'capitalize',
                    color: '#595959'
                  }}
                >
                  {app?.orgAppConfiguration?.app_name || app.name}
                </Typography>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      );
    });
  };

  return (
    <>
      <Box className="AppList-header">
        <Box className="AppList-label">Apps :</Box>
        <Box className="AppList-count">
          <Box className="AppList-number">{apps?.length || 0}</Box>
          <Box className="AppList-text">Nos.</Box>
        </Box>
      </Box>

      <Box className="AppList-container">
        {loading ? (
          <Box className="AppList-loading">
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Grid container spacing={2}>
              {renderApps()}
            </Grid>

            {apps.length > totalAppsToShow && (
              <Box className="AppList-show-more">
                <Box>
                  <Typography className="AppList-show-more-text">
                    View More
                  </Typography>
                </Box>
                <Box className="AppList-show-more-icon">
                  <ArrowCircleRightIcon
                    fontSize="large"
                    onClick={() => {
                      navigate(`/apps/`);
                    }}
                  />
                </Box>
              </Box>
            )}
          </>
        )}
      </Box>
    </>
  );
};

export default AppList;
