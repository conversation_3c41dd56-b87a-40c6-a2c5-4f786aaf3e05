// Global Import
import { Box, Typography, Tooltip } from '@mui/material';

// Local Import
import '../../css/dashboard/clientGraph.scss';

const ClientGraph: React.FC = () => {
  return (
    <Box className="clientGrph-container">
      <Box className="clientGrph-header">
        <Typography className="clientGrph-title">Client Graph</Typography>
      </Box>
      <Box className="clientGrph-graph-container">
        <Tooltip title="Click to interact with the graph" arrow>
          <Box className="clientGrph-graph-box">
            <Box className="clientGrph-graph" />
          </Box>
        </Tooltip>
      </Box>
      <Box className="clientGrph-footer">
        <Tooltip title="Text description for first item" arrow>
          <Box className="clientGrph-footer-item">
            <Box className="clientGrph-footer-dot" />
            <Box>
              <Typography className="clientGrph-footer-text">Text</Typography>
            </Box>
          </Box>
        </Tooltip>
        <Tooltip title="Text description for second item" arrow>
          <Box className="clientGrph-footer-item">
            <Box className="clientGrph-footer-dot" />
            <Box>
              <Typography className="clientGrph-footer-text">Text</Typography>
            </Box>
          </Box>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default ClientGraph;
