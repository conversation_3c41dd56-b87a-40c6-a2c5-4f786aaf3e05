// Global Imports
import { useState, DragEvent, useEffect } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip,
  Typography
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import { toast } from 'react-toastify';

// Local Imports
import { useSelector } from 'react-redux';
import AppList from './AppList';
import ClientGraph from './ClientGraph';
import ActiveUsers from './ActiveUsers';
import { Icon, SubMenu } from '../form.elements';
import MetricsList from './metricsList';
import Shell from '../layout/Shell';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import { DashboardPanelComponentState } from '../../types';
import LoaderUI from '../reusable/loaderUI';
import { AppState } from '../../redux/app.store';

const DashboardPanel: React.FC = () => {
  const { isLoading } = useSelector((state: AppState) => state.dashBoard);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [components, setComponents] = useState<DashboardPanelComponentState[]>(
    []
  );
  const [nextId, setNextId] = useState(0);
  useEffect(() => {
    setComponents([
      { id: nextId, type: 'AppList', width: '100%' }
      // { id: nextId + 1, type: 'ClientGraph', width: '49%' },
      // { id: nextId + 2, type: 'ActiveUsers', width: '49%' },
      // { id: nextId + 3, type: 'Metrics', width: '49%' }
    ]);
    setNextId(nextId + 3);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  // const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  // const [snackbarSeverity, setSnackbarSeverity] = useState<
  //   'success' | 'error' | 'warning' | 'info'
  // >('success');
  const [draggedItemId, setDraggedItemId] = useState<number | null>(null);

  const addComponent = (type: string) => () => {
    if (components.some((component) => component.type === type)) {
      // setSnackbarMessage(`The ${type} Widget is already added.`);
      // setSnackbarSeverity('info');
      // setOpenSnackbar(true);
      toast.info(`The ${type} Widget is already added.`);
      return;
    }
    const componentWidth = type === 'AppList' ? '100%' : '49%';

    setComponents([...components, { id: nextId, type, width: componentWidth }]);
    setNextId(nextId + 1);
    // setSnackbarMessage(`${type} Widget added.`);
    // setSnackbarSeverity('info');
    // setOpenSnackbar(true);
    toast.success(`${type} Widget added.`);
  };

  const deleteComponent = (id: number) => () => {
    document.getElementById(`component-${id}`)?.classList.add('fade-out');
    setTimeout(() => {
      setComponents(components.filter((component) => component.id !== id));
      // setSnackbarMessage('Widget deleted.');
      // setSnackbarSeverity('success');
      // setOpenSnackbar(true);
      toast.success('Widget deleted.');
    }, 300);
  };

  const adjustWidth = (type: string, width: string) => () => {
    setComponents(
      components.map((component) =>
        component.type === type ? { ...component, width } : component
      )
    );
  };

  const handleDragStart = (id: number) => (event: DragEvent) => {
    setDraggedItemId(id);
    const transferEvent = event.dataTransfer;
    if (transferEvent) {
      transferEvent.effectAllowed = 'move';
    }
  };

  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (id: number) => (event: DragEvent) => {
    event.preventDefault();
    if (draggedItemId === null || draggedItemId === id) return;
    const draggedIndex = components.findIndex(
      (component) => component.id === draggedItemId
    );
    const droppedIndex = components.findIndex(
      (component) => component.id === id
    );
    if (draggedIndex === -1 || droppedIndex === -1) return;

    const updatedComponents = [...components];
    const [draggedItem] = updatedComponents.splice(draggedIndex, 1);
    updatedComponents.splice(droppedIndex, 0, draggedItem);

    setComponents(updatedComponents);
    setDraggedItemId(null);
    // setSnackbarMessage('Component reordered.');
    // setSnackbarSeverity('info');
    // setOpenSnackbar(true);
    toast.info('Component reordered.');
  };

  const handleDragEnd = () => {
    setDraggedItemId(null);
  };

  const getMetricsComponent = (componentsData: any) => {
    // Find the index of the component with type 'Metrics'
    const index = componentsData.findIndex(
      (component: any) => component.type === 'Metrics'
    );
    // Return the found component or a default value
    return index !== -1 ? components[index] : components[3];
  };

  const renderComponent = (type: string) => {
    switch (type) {
      case 'AppList':
        return <AppList />;
      case 'Metrics':
        return <MetricsList components={getMetricsComponent(components)} />;
      case 'ClientGraph':
        return <ClientGraph />;
      case 'ActiveUsers':
        return <ActiveUsers />;
      default:
        return null;
    }
  };

  const isComponentAdded = (type: string) =>
    components.some((component) => component.type === type);

  const getSubMenu = () => {
    return (
      <SubMenu
        showMenuIcon
        onMenuClick={() => setDrawerOpen(!drawerOpen)}
        pageName="Dashboard"
      />
    );
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ background: '#ffffff', minHeight: '100%' }}>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ flex: '1 1 auto' }}>
              <>
                <Box sx={{ display: 'flex', position: 'relative' }}>
                  <Drawer
                    variant="persistent"
                    anchor="left"
                    open={drawerOpen}
                    sx={{
                      width: drawerOpen ? '250px' : '0px',
                      flexShrink: 0,
                      '& .MuiDrawer-paper': {
                        width: '250px',
                        position: 'relative',
                        top: 0,
                        left: 0,
                        zIndex: 1100,
                        borderRight: '1px solid rgba(0, 0, 0, 0.12)',
                        backgroundColor: '#fafafa',
                        transition: 'width 0.3s ease',
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column'
                      }
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: '10px',
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        backgroundColor: '#e0e0e0',
                        alignItems: 'center'
                      }}
                    >
                      <Box>
                        <Typography
                          sx={{ fontSize: '18px', fontWeight: '600' }}
                        >
                          Widgets
                        </Typography>
                      </Box>
                      <Box>
                        <Tooltip title="Close menu" arrow>
                          <IconButton
                            aria-label="close"
                            onClick={() => setDrawerOpen(false)}
                            sx={{
                              transition: 'transform 0.3s ease',
                              color: '#e57373'
                            }}
                          >
                            <ChevronLeftIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                    <ListItem
                      button
                      onClick={addComponent('AppList')}
                      className={
                        isComponentAdded('AppList') ? 'faded-text' : ''
                      }
                    >
                      <ListItemText primary="AppList" />
                      {isComponentAdded('AppList') && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(76, 175, 80, 0.2)',
                            borderRadius: '50%',
                            width: '24px',
                            height: '24px',
                            marginLeft: 'auto'
                          }}
                        >
                          <Icon
                            name="Check"
                            fontSize="small"
                            sx={{ color: '#4caf50' }}
                          />
                        </Box>
                      )}
                    </ListItem>

                    <List sx={{ flexGrow: 1, paddingTop: '0' }}>
                      <ListItem
                        button
                        onClick={addComponent('ActiveUsers')}
                        className={
                          isComponentAdded('ActiveUsers') ? 'faded-text' : ''
                        }
                      >
                        <ListItemText primary="ActiveUsers" />
                        {isComponentAdded('ActiveUsers') && (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: 'rgba(76, 175, 80, 0.2)',
                              borderRadius: '50%',
                              width: '24px',
                              height: '24px',
                              marginLeft: 'auto'
                            }}
                          >
                            <Icon
                              name="Check"
                              fontSize="small"
                              sx={{ color: '#4caf50' }}
                            />
                          </Box>
                        )}
                      </ListItem>
                      <ListItem
                        button
                        onClick={addComponent('ClientGraph')}
                        className={
                          isComponentAdded('ClientGraph') ? 'faded-text' : ''
                        }
                      >
                        <ListItemText primary="ClientGraph" />
                        {isComponentAdded('ClientGraph') && (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: 'rgba(76, 175, 80, 0.2)',
                              borderRadius: '50%',
                              width: '24px',
                              height: '24px',
                              marginLeft: 'auto'
                            }}
                          >
                            <Icon
                              name="Check"
                              fontSize="small"
                              sx={{ color: '#4caf50' }}
                            />
                          </Box>
                        )}
                      </ListItem>
                      <ListItem
                        button
                        onClick={addComponent('Metrics')}
                        className={
                          isComponentAdded('Metrics') ? 'faded-text' : ''
                        }
                      >
                        <ListItemText primary="Metrics" />
                        {isComponentAdded('Metrics') && (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: 'rgba(76, 175, 80, 0.2)',
                              borderRadius: '50%',
                              width: '24px',
                              height: '24px',
                              marginLeft: 'auto'
                            }}
                          >
                            <Icon
                              name="Check"
                              fontSize="small"
                              sx={{ color: '#4caf50' }}
                            />
                          </Box>
                        )}
                      </ListItem>
                    </List>
                  </Drawer>

                  <Box
                    sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      justifyContent: 'flex-start',
                      alignItems: 'flex-start',
                      flexGrow: 1,
                      gap: '16px',
                      padding: '0px 100px',
                      '& > *': {
                        flexBasis: 'calc(50% - 16px)', // Always 50% minus gap
                        maxWidth: '100%',
                        minWidth: '300px'
                      }
                    }}
                  >
                    {components.map((component) => (
                      <Box
                        key={component.id}
                        id={`component-${component.id}`}
                        sx={{
                          flex:
                            component.width === '100%' ? '0 0 100%' : '0 0 49%',
                          padding: '10px',
                          borderRadius: '8px',
                          backgroundColor: '#fff',
                          position: 'relative',
                          transition: 'opacity 0.3s ease',
                          '&:hover .icon-container': {
                            display: 'flex'
                          }
                        }}
                        draggable
                        onDragStart={handleDragStart(component.id)}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop(component.id)}
                        onDragEnd={handleDragEnd}
                      >
                        {renderComponent(component.type)}

                        <Box
                          className="icon-container"
                          sx={{
                            display: 'none', // Default display is none
                            position: 'absolute',
                            top: '10px',
                            right: '10px',
                            gap: '8px' // Adds space between icons
                          }}
                        >
                          <Tooltip title="Delete component" arrow>
                            <IconButton
                              aria-label="delete"
                              onClick={deleteComponent(component.id)}
                              sx={{
                                color: '#e57373',
                                '&:hover': {
                                  backgroundColor: 'rgba(229, 115, 115, 0.1)'
                                }
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                          {component.type !== 'AppList' && (
                            <>
                              {component.width === '49%' && (
                                <Tooltip title="Expand to full width" arrow>
                                  <IconButton
                                    aria-label="expand"
                                    onClick={adjustWidth(
                                      component.type,
                                      '100%'
                                    )}
                                    sx={{
                                      color: '#64b5f6',
                                      '&:hover': {
                                        backgroundColor:
                                          'rgba(100, 181, 246, 0.1)'
                                      }
                                    }}
                                  >
                                    <FullscreenIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                              {component.width === '100%' && (
                                <Tooltip title="Shrink to half width" arrow>
                                  <IconButton
                                    aria-label="shrink"
                                    onClick={adjustWidth(component.type, '49%')}
                                    sx={{
                                      color: '#64b5f6',
                                      '&:hover': {
                                        backgroundColor:
                                          'rgba(100, 181, 246, 0.1)'
                                      }
                                    }}
                                  >
                                    <FullscreenExitIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </>
                          )}
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>

                {/* <SnackbarElement
                  message={snackbarMessage}
                  statusType={snackbarSeverity}
                  snackbarOpen={openSnackbar}
                  setSnackbarOpen={setOpenSnackbar}
                /> */}
              </>
            </Box>
          </Box>
        </Box>
      )}
    </Shell>
  );
};

export default DashboardPanel;
