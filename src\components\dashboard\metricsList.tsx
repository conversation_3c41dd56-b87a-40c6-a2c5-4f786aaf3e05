// Global Imports
import { Box, Grid, Typography, Tooltip } from '@mui/material';
import { useSelector } from 'react-redux';

// Local Imports
import { RootState } from '../../redux/reducers';
import { Icon } from '../reusable/Icon';
import '../../css/dashboard/metrics.scss';
import { MetricsListComponentState, OrgAppListState } from '../../types';

interface MetricsListProps {
  components: MetricsListComponentState;
}

const MetricsList: React.FC<MetricsListProps> = ({ components }) => {
  const { clientList } = useSelector((state: RootState) => state.clients);
  const { apps } = useSelector((state: RootState) => state.dashBoard);
  const { users } = useSelector((state: RootState) => state.user);

  const organizedAppsList: OrgAppListState[] = [
    {
      name: 'Clients',
      icon: 'AccountCircleOutlined',
      values: clientList.length
    },
    { name: 'Total Apps', icon: 'WidgetsOutlined', values: apps.length },
    { name: 'Messages', icon: 'MessageOutlined', values: '25' },
    {
      name: 'Total Users',
      icon: 'SupervisedUserCircleOutlined',
      values: users.length
    },
    { name: 'Active Users', icon: 'AccessibilityNewOutlined', values: '5K' },
    { name: 'Subscriptions', icon: 'RecommendOutlined', values: '1000' }
  ];
  return (
    <Box className="MetricsLst-container">
      <Box className="MetricsLst-header">
        <Typography className="MetricsLst-title">Metrics</Typography>
      </Box>
      <Box className="MetricsLst-content">
        <Grid container rowSpacing={4.6} columnSpacing={4.6}>
          {organizedAppsList.map((app: OrgAppListState) => (
            <Grid
              item
              xs={
                components.width === '100%' && components.type === 'Metrics'
                  ? 2
                  : 4
              }
              key={app?.name}
            >
              <Tooltip title={`Total ${app.name}`} arrow>
                <Box className="MetricsLst-app-box">
                  <Typography className="MetricsLst-app-value">
                    {app.values}
                  </Typography>
                </Box>
              </Tooltip>
              <Box className="MetricsLst-app-info">
                <Box className="MetricsLst-app-icon">
                  <Icon name={app.icon} color="primary" />
                </Box>
                <Typography className="MetricsLst-app-name">
                  {app.name}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default MetricsList;
