// Package Imports
import { useState } from 'react';
import {
  Box,
  Divider,
  Grid,
  IconButton,
  Tooltip,
  Typography
} from '@mui/material';
import { A<PERSON>, Dehaze, Widgets } from '@mui/icons-material';

// Locale Imports
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Shell from '../layout/Shell';
// import { SubMenu } from '../form.elements';
import '../../css/index.scss';
import '../../css/apps-list-styles.scss';
import { RootState } from '../../redux/reducers';
import LoaderUI from '../reusable/loaderUI';

interface AppListProps {
  appsList: any[];
}
export const AppsList = ({ appsList }: AppListProps) => {
  const { isLoading } = useSelector((state: RootState) => state.form);
  const navigate = useNavigate();

  const [listStyle, setListStyle] = useState<string>('grid');

  return (
    <Shell
    // subMenu={
    //   <SubMenu
    //   // search
    //   // searchStyles={{
    //   //   width: '50%',
    //   //   height: '45px'
    //   // }}
    //   />
    // }
    >
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ padding: '0px 100px' }}>
          <Box sx={{ padding: '20px 0px' }}>
            {' '}
            <Box
              className="justify-content-between d-flex align-items-center"
              paddingBottom="20px"
            >
              <Box className="d-flex align-items-center gap-10">
                <Tooltip
                  title="Total Apps"
                  arrow
                  placement="top"
                  enterDelay={200}
                >
                  <Box className="font-weight-400" fontSize="28px">
                    Apps
                  </Box>
                </Tooltip>
              </Box>
              <Box className="d-flex">
                <Tooltip
                  title="Grid Style View"
                  arrow
                  placement="top"
                  enterDelay={200}
                >
                  <IconButton onClick={() => setListStyle('grid')}>
                    <Apps
                      opacity={listStyle === 'grid' ? 1 : 0.4}
                      sx={{
                        color: 'b3.main'
                      }}
                    />
                  </IconButton>
                </Tooltip>
                <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
                <Tooltip
                  title="List Style View"
                  arrow
                  placement="top"
                  enterDelay={200}
                >
                  <IconButton onClick={() => setListStyle('list')}>
                    <Dehaze
                      sx={{
                        color: 'b3.main'
                      }}
                      opacity={listStyle === 'list' ? 1 : 0.4}
                    />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            <Box
              sx={{
                background: '#faf9f8',
                borderTop: '0.125rem solid #0483ba',
                padding: '1.875rem 2.5rem'
              }}
              padding="40px"
              boxShadow="0px 0px 2px 0px rgba(0,0,0,0.24)"
              className="h-full"
            >
              {isLoading && <LoaderUI />}
              {!isLoading && (
                <Grid container columnSpacing={4} rowSpacing={4}>
                  {appsList.map((data: any, index: any) => {
                    const key = data?.id || index;
                    const appName =
                      data?.orgAppConfiguration?.app_name || data?.name;
                    const logo = data?.orgAppConfiguration?.logo;
                    const placeholderIcon = (
                      <Widgets style={{ color: 'gray' }} />
                    );

                    return (
                      <Grid item key={key} xs={listStyle === 'list' ? 12 : 0}>
                        <Box
                          sx={{
                            width: listStyle === 'list' ? '100%' : '180px',
                            height: listStyle === 'list' ? '60px' : '180px',
                            borderRadius: listStyle === 'list' ? '0px' : '16px',
                            background:
                              listStyle === 'list' ? 'transparent' : '#ffffff',
                            boxShadow:
                              listStyle === 'list'
                                ? '0px solid'
                                : '0px 4px 8px rgba(0,0,0,0.15)',
                            transition: '0.3s ease-in-out',
                            cursor: 'pointer',
                            display: 'flex',
                            flexDirection:
                              listStyle === 'list' ? 'row' : 'column',
                            alignItems: 'center',
                            overflow: 'hidden',
                            '&:hover': {
                              boxShadow:
                                listStyle === 'list'
                                  ? '0px solid'
                                  : '0px 6px 12px rgba(0,0,0,0.2)',
                              transform: 'translateY(-3px)'
                            }
                          }}
                          onClick={() =>
                            navigate(
                              `/form-builder/form-details/${data?.app_id}`
                            )
                          }
                        >
                          <Box
                            sx={{
                              width: listStyle === 'list' ? '60px' : '100%',
                              height: listStyle === 'list' ? '60px' : '120px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: '#e9feff'
                            }}
                          >
                            {logo ? (
                              <Box
                                component="img"
                                src={logo}
                                alt={appName}
                                sx={{
                                  width: listStyle === 'list' ? '50px' : '80px',
                                  height:
                                    listStyle === 'list' ? '50px' : '80px',
                                  objectFit: 'contain',
                                  borderRadius: '50%',
                                  backgroundColor: '#ffffff'
                                }}
                              />
                            ) : (
                              <Box
                                sx={{
                                  width: listStyle === 'list' ? '50px' : '80px',
                                  height:
                                    listStyle === 'list' ? '50px' : '80px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  borderRadius: '50%',
                                  backgroundColor: '#ffffff'
                                }}
                              >
                                {placeholderIcon}
                              </Box>
                            )}
                          </Box>
                          <Tooltip
                            title={appName}
                            arrow
                            placement={
                              listStyle === 'list' ? 'right' : 'bottom'
                            }
                            enterDelay={200}
                          >
                            <Typography
                              noWrap
                              sx={{
                                color: '#595959',
                                maxWidth: 180,
                                fontWeight: 700,
                                textAlign:
                                  listStyle === 'list' ? 'left' : 'center',
                                padding:
                                  listStyle === 'list' ? '0 10px' : '15px',
                                textTransform: 'capitalize',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {appName}
                            </Typography>
                          </Tooltip>
                        </Box>
                      </Grid>
                    );
                  })}
                </Grid>
              )}
            </Box>
          </Box>
        </Box>
      )}
    </Shell>
  );
};

export default AppsList;
