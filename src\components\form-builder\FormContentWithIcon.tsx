import React from 'react';
import { useFormikContext } from 'formik';
import {
  Box,
  Card,
  CardMedia,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  Typography
} from '@mui/material';
import { Delete, Upload } from '@mui/icons-material';
import { FormInput } from '../form.elements';
import { RenderGroup } from '../reusable/CreateForm.elements';
import LoaderUI from '../reusable/loaderUI';
import { useDebounceCallback } from '../../utils/customHooks/useDebounce';
import { updateform, updateSnackbar } from '../../redux/reducers/form.reducer';

interface FormContentWithIconProps {
  formData: any;
  formId: string;
  isLoading: boolean;
  handleInputChange: (e: any, values?: any) => void;
  openIconDialog: boolean;
  handleClose: () => void;
  handleIconDialogOpen: () => void;
  fullScreen: boolean;
  formSvgIcons: any[];
  setOpenIconDialog: (open: boolean) => void;
  setHandleFormIconLoading: (loading: boolean) => void;
  dispatch: any;
}

const FormContentWithIcon: React.FC<FormContentWithIconProps> = ({
  formData,
  formId,
  isLoading,
  handleInputChange,
  openIconDialog,
  handleClose,
  handleIconDialogOpen,
  fullScreen,
  formSvgIcons,
  setOpenIconDialog,
  setHandleFormIconLoading,
  dispatch
}) => {
  const { setFieldValue, values } = useFormikContext<any>();

  // Debounced function for icon updates with optimistic updates
  const handleFormIconChangeWithFormik = useDebounceCallback(
    async (value: string) => {
      try {
        // Optimistically update the icon in Formik state immediately
        setFieldValue('icon', value);
        setHandleFormIconLoading(true);

        const response = await dispatch(
          updateform({ value, values: formData, name: 'icon', formId })
        );
        setHandleFormIconLoading(false);

        if (response.payload.status) {
          setOpenIconDialog(false);
          // Icon is already updated optimistically, no need to sync back
        } else {
          // Revert the optimistic update on failure
          setFieldValue('icon', formData.icon);
          dispatch(
            updateSnackbar({
              open: true,
              message:
                response?.payload?.message || 'Failed to update form icon',
              severity: 'error'
            })
          );
        }
      } catch (error) {
        setHandleFormIconLoading(false);
        // Revert the optimistic update on error
        setFieldValue('icon', formData.icon);
        dispatch(
          updateSnackbar({
            open: true,
            message: 'Something Went Wrong, Please Try Again Later.',
            severity: 'error'
          })
        );
      }
    },
    800
  );

  return (
    <Box className="form">
      <Box className="form-title-card d-flex flex-column mb-3 pt-5 align-items-center">
        <Grid container spacing={2}>
          <Grid item xs={4} sm={3} md={2}>
            <Box className="org-registn-upload-logo-box">
              <Box className="d-flex">
                <Card
                  className="org-registn-upload-logo-card"
                  onClick={handleIconDialogOpen}
                >
                  <Box className="org-registn-logo-box">
                    <CardMedia
                      className="org-registn-logo-media p-4"
                      image={
                        values?.icon ||
                        `https://ui-avatars.com/api/?name=${encodeURIComponent(
                          'upload logo'
                        )}&size=128&rounded=true`
                      }
                      title="Logo"
                    />
                  </Box>
                  <Upload className="org-registn-upload-icon" />
                </Card>
                <Box>
                  {values?.icon && (
                    <Delete
                      onClick={() => handleFormIconChangeWithFormik('')}
                      sx={{
                        color: 'red2.light',
                        cursor: 'pointer'
                      }}
                    />
                  )}
                </Box>
              </Box>
            </Box>

            <Dialog
              fullScreen={fullScreen}
              open={openIconDialog}
              onClose={handleClose}
              aria-labelledby="customized-dialog-title"
              sx={{
                '& .MuiDialog-paper': {
                  borderRadius: '20px',
                  padding: '20px',
                  backgroundColor: '#ece9e6',
                  textTransform: 'capitalize',
                  maxHeight: '90vh',
                  overflow: 'hidden'
                }
              }}
              PaperProps={{
                component: 'form',
                onSubmit: async (event: React.FormEvent<HTMLFormElement>) => {
                  event.preventDefault();
                }
              }}
            >
              <DialogTitle
                id="customized-dialog-title"
                sx={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  color: '#08366B',
                  marginBottom: '0px'
                }}
              >
                Select Form Icon
              </DialogTitle>

              <DialogContent
                dividers
                sx={{
                  maxHeight: '70vh',
                  overflowY: 'auto',
                  paddingRight: '10px',
                  paddingTop: '0px'
                }}
              >
                {formSvgIcons.map(
                  (
                    iconsByCategory: { name: string; images: any },
                    index: number
                  ) => (
                    <Box
                      key={`${index + 1}`}
                      className="d-flex flex-column"
                      sx={{
                        gap: '10px',
                        justifyContent: 'center'
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: '16px',
                          fontWeight: 'bold',
                          color: '#08366B',
                          marginBottom: '10px',
                          position: 'sticky',
                          top: 0,
                          backgroundColor: '#ece9e6',
                          zIndex: 1,
                          padding: '5px 0',
                          borderBottom: '1px solid #dcdcdc'
                        }}
                      >
                        {iconsByCategory.name}
                      </Typography>

                      <Box className="d-flex flex-wrap">
                        {iconsByCategory.images.map(
                          (icon: any, iconIndex: number) => {
                            const dataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(icon.svg)}`;
                            return (
                              <Box
                                key={`${iconIndex + 1}`}
                                sx={{
                                  margin: '7px',
                                  padding: '2px',
                                  backgroundColor: 'white',
                                  border:
                                    values.icon === dataUrl
                                      ? '2px solid #36C0ED'
                                      : '1px solid transparent',
                                  cursor: 'pointer'
                                }}
                              >
                                <CardMedia
                                  component="img"
                                  image={dataUrl}
                                  alt={icon.name}
                                  sx={{
                                    height: 80,
                                    width: 80,
                                    margin: 'auto'
                                  }}
                                  onClick={() =>
                                    handleFormIconChangeWithFormik(dataUrl)
                                  }
                                />
                              </Box>
                            );
                          }
                        )}
                      </Box>
                    </Box>
                  )
                )}
              </DialogContent>
            </Dialog>
          </Grid>
          <Grid item xs={8} sm={9} md={10}>
            <Box className="d-flex flex-column">
              <FormInput
                name="name"
                className="form-title-field"
                label=""
                autoComplete="off"
                placeholder="Form Title"
                isCreateForm
                fullWidth
                handleInputChange={handleInputChange}
                containerStyles={{
                  marginBottom: '0px'
                }}
              />
              <FormInput
                name="description"
                label=""
                placeholder="Description"
                autoComplete="off"
                className="description-title-field"
                isCreateForm
                handleInputChange={handleInputChange}
                containerStyles={{
                  marginBottom: '0px'
                }}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="w-100">
          {formData &&
            formData.groups
              .slice()
              .sort(
                (prev: any, next: any) =>
                  parseInt(prev.group_index, 10) -
                  parseInt(next.group_index, 10)
              )
              .map((group: any, index: number) => {
                const groupKey = `groups[${index}].group_key`;
                const groupTitleName = `groups[${index}].group_title`;
                const groupDescriptionName = `groups[${index}].group_description`;
                const groupIteration = `groups[${index}].is_iterative_or_not`;
                const groupMaxIterationLength = `groups[${index}].iteration_max_length`;
                const key = `${index}-${index * 2}-group-key`;
                return (
                  <RenderGroup
                    formData={formData}
                    group={group}
                    groupKey={groupKey}
                    groupsCount={formData.groups.length}
                    groupTitle={groupTitleName}
                    groupDescription={groupDescriptionName}
                    groupIteration={groupIteration}
                    groupMaxIterationLength={groupMaxIterationLength}
                    handleInputChange={handleInputChange}
                    key={key + 1}
                    secIndex={index}
                  />
                );
              })}
        </Box>
      )}

      <Box
        sx={{
          height: '100px',
          width: '100%'
        }}
      />
    </Box>
  );
};

export default FormContentWithIcon;
