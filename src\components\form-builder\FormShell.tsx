// Package Imports
import { Box } from '@mui/material';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Locale Imports
import FormHeader from './features/Header';
import '../../css/add-form-styles.scss';
import SectionTemplates from './features/SectionTemplates';
import Suggestions from './features/Suggestions';
import FormContainer from './FormContainer';
import { AppDispatch } from '../../redux/app.store';
import {
  getsectiontemplate,
  loadTemplateSpinner
} from '../../redux/reducers/form.reducer';
import { RootState } from '../../redux/reducers';
import Shell from '../layout/Shell';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import LoaderUI from '../reusable/loaderUI';

const FormShell: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    form,
    // snackbar
    isLoading
  } = useSelector((state: RootState) => state.form);
  const userType = localStorage.getItem('user_type');
  const [showTemplate, setShowTemplate] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<any>();

  const handleSelection = async (id: string) => {
    try {
      dispatch(loadTemplateSpinner(true));
      const response = await dispatch(getsectiontemplate(id));
      if (response.payload.status) {
        setSelectedTemplate(response.payload.data);
      } else {
        toast.error('Something went wrong');
      }
    } catch (error) {
      toast.error('Something went wrong');
    }
  };

  // const handleCloseSnackbar = () => {
  //   setSnackbarOpen(false);
  // };
  const getSubMenu = () => {
    return (
      <FormHeader
        type="form-builder"
        redirectLink={
          userType === 'organization' ? `` : `/apps/app-details/${form?.app_id}`
        }
      />
    );
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="w-full h-full bg-grayF0 d-flex flex-column gap-0">
          <Box className="w-full overflow-hidden position-relative flex gap-10">
            {/* Side Templates */}
            {showTemplate === 'suggestion' ? (
              <Suggestions
                setShowTemplate={() => setShowTemplate('')}
                handleSelection={handleSelection}
              />
            ) : (
              showTemplate === 'sectionTemplate' && (
                <SectionTemplates
                  setShowTemplate={() => setShowTemplate('')}
                  handleSelection={handleSelection}
                />
              )
            )}

            {/* Form Container */}
            <FormContainer
              showTemplate={showTemplate}
              setShowTemplate={setShowTemplate}
              selectedTemplate={selectedTemplate}
            />
          </Box>

          {/* <Snackbar
          open={snackbarOpen}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
          message="Template selected successfully"
          action={
            <IconButton
              size="small"
              color="inherit"
              onClick={handleCloseSnackbar}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          }
        /> */}
        </Box>
      )}
      {/* <SnackbarElement
        message={
          snackbar?.snackbarMessage ||
          'Something Went Wrong, Please Try Again Later.'
        }
        statusType={snackbar?.snackbarSeverity}
        snackbarOpen={snackbarOpen}
        setSnackbarOpen={() => setSnackbarOpen(false)}
      /> */}
      {/* {snackbar?.snackbarMessage && <SnackbarElement message={snackbar?.snackbarMessage} statusType={snackbar?.snackbarSeverity} snackbarOpen={snackbarOpen} setSnackbarOpen={()=>setSnackbarOpen(false)} />} */}
    </Shell>
  );
};
export default FormShell;
