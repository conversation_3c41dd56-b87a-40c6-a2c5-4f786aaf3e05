// Package Imports
import { Box } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local Imports
import { AppForm, FormInput, SubMenu } from '../../form.elements';
import { AppDispatch } from '../../../redux/app.store';
import '../../../css/index.scss';
import {
  createtemplate,
  // gettemplatecountrieslist,
  gettemplateform,
  gettemplateformFields
} from '../../../redux/reducers/template.reducer';
// eslint-disable-next-line import/no-duplicates
import '../../../css/add-form-styles.scss';
import { RootState } from '../../../redux/reducers';
import TemplateActiveField from './TemplateActiveField';
import TemplateInactiveField from './TemplateInactiveField';
import LoaderUI from '../../reusable/loaderUI';
import { getcountrieslist } from '../../../redux/reducers/form.reducer';

const AddTemplate: React.FC = () => {
  const { id } = useParams<string>();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const {
    formFields,
    countriesList = [],
    isLoading
  } = useSelector((state: RootState) => state.template);
  const { formId } = useSelector((state: RootState) => state.form);

  const [formData, setFormData] = useState<any>({
    name: '',
    description: '',
    fields: []
  });

  const [formProps, setFormProps] = useState({
    columnIndex: 0,
    previousColumnIndex: 0,
    textType: null
  });
  const [countries, setCountries] = useState<any>([]);
  const [elements, setElements] = useState({
    radioElements: null,
    checkboxElements: null,
    selectElements: null,
    toggleElements: null
  });

  // const [snackbarOpen, setSnackbarOpen] = useState(false);
  // const [snackbarMessage, setSnackbarMessage] = useState('');
  // const [snackbarSeverity, setSnackbarSeverity] = useState<
  //   'success' | 'error' | 'warning' | 'info'
  // >('success');

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Please enter Template name')
  });

  const submitForm = async () => {
    if (id) {
      const res = await dispatch(
        createtemplate({ formId: id, data: formData })
      );
      if (res.payload.status) {
        // const snackbarmsg = {
        //   snackbarMessage: 'Template Updated successfully',
        //   snackbarSeverity: 'success',
        //   snackbarOpen: true,
        //   setSnackbarOpen: null
        // };
        // dispatch(updateSnackbar(snackbarmsg));
        // setTimeout(() => {
        //   snackbarmsg.snackbarMessage = '';
        //   snackbarmsg.snackbarSeverity = 'success';
        //   snackbarmsg.snackbarOpen = false;
        //   dispatch(updateSnackbar(snackbarmsg));
        // }, 3000);
        // toast.success('Template Updated successfully');
        navigate(`/apps/form-builder/edit-form/${formId}`);
      } else if (res.payload.error) {
        // const snackbarmsg = {
        //   snackbarMessage:
        //     res.payload.message.length > 0
        //       ? res.payload.message[0]
        //       : res.payload.message,
        //   snackbarSeverity: 'error',
        //   snackbarOpen: true,
        //   setSnackbarOpen: null
        // };
        // dispatch(updateSnackbar(snackbarmsg));
        // setTimeout(() => {
        //   snackbarmsg.snackbarMessage = '';
        //   snackbarmsg.snackbarSeverity = 'error';
        //   snackbarmsg.snackbarOpen = false;
        //   dispatch(updateSnackbar(snackbarmsg));
        // }, 3000);
        toast.error(
          res.payload.message.length > 0
            ? res.payload.message[0]
            : res.payload.message
        );
      }
    } else {
      const res = await dispatch(
        createtemplate({ formId: id || '', data: formData })
      );

      if (res.payload.status) {
        navigate(`/edit-section-template/${res?.payload?.data?.section_id}`);
        // toast.success(res.payload.message);
      } else {
        const errorMessage = res.payload.message;
        if (errorMessage.length > 0) {
          errorMessage.forEach((element: any) => {
            toast.warning(element);
          });
        } else {
          toast.error(res.payload.message);
        }
      }
    }
  };

  // useEffect(() => {
  //   if (templateForm?.section_id) {
  //     const data = {
  //       name: templateForm?.name,
  //       description: templateForm?.description,
  //       fields: templateForm?.fields
  //     };
  //     setFormData(data);
  //     setFormProps((prevData: any) => ({
  //       ...prevData,
  //       columnIndex: templateForm?.fields ? templateForm.fields.length - 1 : 0
  //     }));
  //   }
  // }, [templateForm]);

  const getData = async () => {
    const { payload } = await dispatch(gettemplateform(id));

    if (payload.status) {
      const data = {
        name: payload?.data?.name,
        description: payload?.data?.description,
        fields: payload?.data?.fields
      };
      const index = payload?.data?.fields ? payload.data.fields.length - 1 : 0;
      const textType = formFields?.find(
        (s: any) =>
          s.field_id === payload?.data?.fields[index]?.original_field_id
      );
      if (textType?.skelton?.input_type === 'radio') {
        setElements({
          ...elements,
          radioElements: payload?.data?.fields[index]?.options
        });
      }
      if (textType?.skelton?.input_type === 'checkbox') {
        setElements({
          ...elements,
          checkboxElements: payload?.data?.fields[index]?.options
        });
      }
      if (textType?.skelton?.type === 'select') {
        setElements({
          ...elements,
          selectElements: payload?.data?.fields[index]?.options
        });
      }
      if (textType?.skelton?.type === 'toggle') {
        setElements({
          ...elements,
          toggleElements: payload?.data?.fields[index]?.options
        });
      }
      setFormData(data);
      setFormProps((prevData: any) => ({
        ...prevData,
        columnIndex: index,
        textType
      }));
    }
  };

  const getFormFieldsData = async () => {
    await dispatch(gettemplateformFields(null));
    // await dispatch(gettemplatecountrieslist(null));
    await dispatch(getcountrieslist(null));
  };

  const handleInputChange = (e: any, values: any) => {
    e.preventDefault();
    const { name, value } = e.target;
    const keys = name.split(/[[\].]+/).filter(Boolean);

    const jsonString: string = JSON.stringify(values);
    const inputValues = JSON.parse(jsonString);
    let obj = JSON.parse(jsonString);

    keys.forEach((key: any, index: number) => {
      if (index === keys.length - 1) {
        obj[key] = value;
      } else {
        obj = obj[key];
      }
    });
    if (name === 'name') {
      inputValues.name = value;
      setFormData(values);
    }
    if (name === 'description') {
      inputValues.description = value;
      setFormData(values);
    }
    if (name === 'state') {
      inputValues.state = value;
      setFormData(values);
    }

    if (keys.length > 2) {
      if (keys[2] === 'label') {
        inputValues.fields[parseInt(keys[1], 10)].label = value;
      } else if (keys[2] === 'original_field_id' && values.fields) {
        const typeText = formFields?.find((s: any) => s.field_id === value);
        inputValues.fields[parseInt(keys[1], 10)].original_field_id = value;
        inputValues.fields[parseInt(keys[1], 10)].type = typeText.skelton.type;
        inputValues.fields[parseInt(keys[1], 10)].input_type =
          typeText.skelton.input_type;
        inputValues.fields[parseInt(keys[1], 10)].options =
          typeText?.skelton?.options;
        if (typeText?.skelton?.input_type === 'radio') {
          setElements({
            ...elements,
            radioElements: typeText?.skelton?.options
          });
        }
        if (typeText?.skelton?.input_type === 'checkbox') {
          setElements({
            ...elements,
            checkboxElements: typeText?.skelton?.options
          });
        }
        if (typeText?.skelton?.type === 'select') {
          setElements({
            ...elements,
            selectElements: typeText?.skelton?.options
          });
        }
        if (typeText?.skelton?.type === 'toggle') {
          setElements({
            ...elements,
            toggleElements: typeText?.skelton?.options
          });
        }
      } else if (keys[2] === 'description') {
        inputValues.fields[parseInt(keys[1], 10)].description_status =
          value.length > 0;
        inputValues.fields[parseInt(keys[1], 10)].description = value;
      } else if (keys[2] === 'is_iterative_or_not') {
        inputValues.fields[parseInt(keys[1], 10)].is_iterative_or_not =
          e.target.checked;
      } else if (keys[2] === 'iteration_max_length') {
        inputValues.fields[parseInt(keys[1], 10)].iteration_max_length =
          parseInt(value);
      } else if (keys[2] === 'validation_schema') {
        inputValues.fields[parseInt(keys[1], 10)].validation_schema.required =
          e.target.checked;
      }
      setFormData(inputValues);
    }
  };
  useEffect(() => {
    if (id) {
      getData();
    } else {
      setFormData((prevData: any) => ({
        ...prevData,
        name: 'Form Title'
      }));
    }

    if (countriesList) {
      const data = countriesList?.map((country: any) => {
        return {
          value: country?.isoCode,
          label: country?.isoCode
        };
      });
      setCountries(data);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, countriesList]);
  useEffect(() => {
    getFormFieldsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const updateColIndex = (index: any) => {
    setFormProps((prevData: any) => ({
      ...prevData,
      columnIndex: index
    }));
  };

  interface Element {
    label: string;
    value: string;
  }

  const changeElement = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: 'radio' | 'checkbox' | 'select' | 'toggle',
    i: number,
    elementId: string
  ) => {
    const { value } = e.target;
    if (!value) return;

    setElements((prevElements: any) => {
      const elementKey = `${type}Elements`; // radioElements, checkboxElements, etc.
      const updatedElements = prevElements[elementKey].map(
        (element: Element, index: number) =>
          index === i ? { ...element, label: value, value } : element
      );

      setFormData((prevFormData: any) => {
        const fieldIndex = prevFormData.fields.findIndex(
          (field: any) => field?.field_id === elementId
        );

        const updatedFields = prevFormData.fields.map(
          (field: any, index: number) =>
            index === fieldIndex
              ? { ...field, options: updatedElements }
              : field
        );

        return {
          ...prevFormData,
          fields: updatedFields
        };
      });

      return {
        ...prevElements,
        [elementKey]: updatedElements
      };
    });
  };

  const handleSelectField = (field: any) => {
    const textType = formFields?.find(
      (s: any) => s?.field_id === field?.original_field_id
    );
    if (textType?.skelton?.input_type === 'radio') {
      setElements({
        ...elements,
        radioElements: field?.options
      });
    }
    if (textType?.skelton?.input_type === 'checkbox') {
      setElements({
        ...elements,
        checkboxElements: field?.options
      });
    }
    if (textType?.skelton?.type === 'select') {
      setElements({
        ...elements,
        selectElements: field?.options
      });
    }
    if (textType?.skelton?.type === 'toggle') {
      setElements({
        ...elements,
        toggleElements: field?.options
      });
    }
  };

  const handleAddOption = (type: string, optId: string) => {
    const elementMap: Record<string, string> = {
      radio: 'radioElements',
      checkbox: 'checkboxElements',
      select: 'selectElements'
    };

    // Check if the provided type is valid
    if (!elementMap[type]) return;

    const elementKey = elementMap[type];

    setElements((prevElements: any) => {
      // Create a new option
      const updatedElements = [
        ...prevElements[elementKey],
        {
          value: `Option${prevElements[elementKey].length + 1}`,
          label: `Option${prevElements[elementKey].length + 1}`
        }
      ];

      // Update form data with the new option
      setFormData((prevFormData: any) => {
        const fieldIndex = prevFormData.fields.findIndex(
          (field: any) => field?.field_id === optId
        );

        const updatedFields = prevFormData.fields.map(
          (field: any, index: number) =>
            index === fieldIndex
              ? { ...field, options: updatedElements }
              : field
        );

        return {
          ...prevFormData,
          fields: updatedFields
        };
      });

      // Update the state with the new elements
      return {
        ...prevElements,
        [elementKey]: updatedElements
      };
    });
  };

  const updateParagraphText = (value: string, textId: string) => {
    setFormData((prevFormData: any) => {
      const fieldIndex = prevFormData.fields.findIndex(
        (fd: any) => fd?.field_id === textId
      );

      const updatedFields = prevFormData.fields.map(
        (field: any, index: number) =>
          index === fieldIndex ? { ...field, description: value } : field
      );

      return {
        ...prevFormData,
        fields: updatedFields
      };
    });
  };

  return (
    <Box className="d-flex-column">
      <Box
        sx={{
          height: 82
        }}
      >
        <SubMenu
          backNavigation
          redirectLink={`/apps/form-builder/edit-form/${formId}`}
        />
      </Box>
      {isLoading ? (
        <LoaderUI />
      ) : (
        <Box className="form-container d-flex flex-column overflow-auto overflow-hidden flex-1">
          <Box className="flex-1 form">
            <AppForm
              initialValues={formData}
              validationSchema={validationSchema}
              onSubmit={submitForm}
            >
              <>
                <Box className="form-title-card d-flex flex-column mb-3 pt-5">
                  <FormInput
                    name="name"
                    className="form-title-field"
                    label=""
                    autoComplete="off"
                    placeholder="Form Title"
                    isCreateForm
                    handleInputChange={handleInputChange}
                    containerStyles={{
                      marginBottom: '0px'
                    }}
                  />
                  <FormInput
                    name="description"
                    label=""
                    placeholder="Description"
                    autoComplete="off"
                    className="description-title-field"
                    isCreateForm
                    handleInputChange={handleInputChange}
                    containerStyles={{
                      marginBottom: '0px'
                    }}
                  />
                </Box>

                {!id && formData.fields.length === 0 ? (
                  <Box className="w-100">
                    <TemplateActiveField
                      field="new"
                      formData={formData}
                      setFormData={setFormData}
                      colIndex={0}
                      formFields={formFields}
                      formProps={formProps}
                      setFormProps={setFormProps}
                      elements={elements}
                      changeElement={changeElement}
                      handleAddOption={handleAddOption}
                      updateParagraphText={updateParagraphText}
                      handleInputChange={handleInputChange}
                    />
                  </Box>
                ) : (
                  <Box className="w-100">
                    {formData?.fields?.map((field: any, index: number) => {
                      return formProps.columnIndex === index ? (
                        <TemplateActiveField
                          key={`${index + 1}`}
                          field={field}
                          formData={formData}
                          setFormData={setFormData}
                          colIndex={index}
                          formFields={formFields}
                          formProps={formProps}
                          setFormProps={setFormProps}
                          elements={elements}
                          changeElement={changeElement}
                          handleAddOption={handleAddOption}
                          updateParagraphText={updateParagraphText}
                          handleInputChange={handleInputChange}
                        />
                      ) : (
                        <TemplateInactiveField
                          key={`${index + 1}`}
                          colIndex={index}
                          formData={formData}
                          field={field}
                          countries={countries}
                          updateColIndex={updateColIndex}
                          handleSelectField={handleSelectField}
                        />
                      );
                    })}
                  </Box>
                )}
                <br />
              </>
            </AppForm>
          </Box>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarMessage}
        statusType={snackbarSeverity}
        snackbarOpen={snackbarOpen}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Box>
  );
};

export default AddTemplate;
