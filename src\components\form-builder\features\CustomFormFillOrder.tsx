import React, { useEffect, useState, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Switch,
  Tooltip,
  Typography,
  Card,
  CardContent,
  CardMedia
} from '@mui/material';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  rectSortingStrategy,
  arrayMove
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { toast } from 'react-toastify';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import { AppDispatch } from '../../../redux/app.store';
import { getapp } from '../../../redux/reducers/apps.reducer';
import {
  toggleformslist,
  updateformslist
} from '../../../redux/reducers/form.reducer';
import { SubMenu } from '../../form.elements';
import Shell from '../../layout/Shell';
import '../../../css/index.scss';
import '../../../css/custom-fill-order.scss';

/**
 * Unified card component that accepts a flag "isDraggable".
 * When isDraggable is true, we conditionally apply a hover effect (scale and boxShadow).
 */
interface CardComponentProps {
  card: any;
  onCardClick: (cardId: string) => void;
  isDraggable?: boolean;
}
export const CardComponent: React.FC<CardComponentProps> = ({
  card,
  onCardClick,
  isDraggable = false
}) => {
  return (
    <Card
      onClick={() => onCardClick(card.id)}
      sx={{
        borderRadius: 1,
        boxShadow: 1,
        p: 0,
        width: '100%',
        height: 80,
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: 'background.paper',
        border: '1px solid transparent',
        transition: 'all 0.3s ease-in-out',
        ...(isDraggable && {
          '&:hover': {
            backgroundColor: 'background.default',
            border: '1px solid',
            borderColor: 'primary.main',
            transform: 'scale(1.05)',
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
          }
        })
      }}
    >
      <CardContent
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          pt: 2,
          width: '100%'
        }}
      >
        {card?.icon ? (
          <CardMedia
            component="img"
            image={card.icon}
            alt={card?.name}
            sx={{ width: '40px', height: '40px', backgroundColor: '#F9F9F9' }}
          />
        ) : (
          <AssignmentOutlinedIcon
            sx={{ color: 'text.secondary', fontSize: 30 }}
          />
        )}
        <Tooltip title={card?.name} arrow>
          <Typography
            sx={{
              fontSize: '18px',
              fontWeight: 600,
              color: 'text.secondary',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              width: 200
            }}
          >
            {card?.name}
          </Typography>
        </Tooltip>
      </CardContent>
    </Card>
  );
};

/**
 * Sortable card wraps the unified CardComponent with DnD Kit hooks.
 * This version is rendered only when drag-and-drop is enabled.
 */
interface SortableFormCardProps {
  card: any;
  onCardClick: (cardId: string) => void;
}
export const SortableFormCard: React.FC<SortableFormCardProps> = ({
  card,
  onCardClick
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({
    id: card.id
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1
  };

  return (
    <Grid
      item
      xs={12}
      sm={6}
      md={4}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      <CardComponent card={card} onCardClick={onCardClick} isDraggable />
    </Grid>
  );
};

/**
 * Main component that renders form cards.
 * Uses drag-and-drop version (SortableFormCard) if the toggle is enabled,
 * otherwise uses the regular CardComponent.
 */
const CustomFormFillOrder: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [formsList, setFormsList] = useState<any[]>([]);
  const [isToggled, setIsToggled] = useState(false);

  const getSubMenu = useCallback(() => <SubMenu backNavigation />, []);

  const formsRepository = useCallback(async () => {
    try {
      const res = await dispatch(getapp(id));
      if (res?.payload?.status) {
        const responseData = res.payload.data;
        const data = responseData?.forms?.map((form: any) => ({
          ...form,
          id: form?.form_id
        }));
        setFormsList(data);
        setIsToggled(responseData?.toggle_forms_fill_order_status);
      } else {
        toast.error(
          res?.payload?.message || 'Something went wrong. Try again later.'
        );
      }
    } catch {
      toast.error('Something went wrong. Try again later.');
    }
  }, [dispatch, id]);

  useEffect(() => {
    formsRepository();
  }, [formsRepository]);

  const handleCardClick = (cardId: string) => {
    navigate(`/form-builder/edit-form/${cardId}`);
    if (id) {
      localStorage.setItem('app_id', id);
    }
  };

  const handleToggleChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsToggled(event.target.checked);
    try {
      const res = await dispatch(toggleformslist({ appId: id }));
      if (!res?.payload?.status) {
        toast.error(
          res?.payload?.message || 'Something went wrong. Try again later.'
        );
      }
    } catch {
      toast.error('Something went wrong. Try again later.');
    }
  };

  const handleDragEnd = async (event: any) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    setFormsList((currentForms) => {
      const oldIndex = currentForms.findIndex((form) => form.id === active.id);
      const newIndex = currentForms.findIndex((form) => form.id === over.id);
      return arrayMove(currentForms, oldIndex, newIndex);
    });

    const updatedForms = arrayMove(
      formsList,
      formsList.findIndex((form) => form.id === active.id),
      formsList.findIndex((form) => form.id === over.id)
    );

    const data = {
      forms: updatedForms.map((form, index) => ({
        form_id: form.id,
        form_index: index
      }))
    };

    try {
      await dispatch(updateformslist({ appId: id, data }));
    } catch {
      toast.error('Something went wrong. Try again later.');
    }
  };

  return (
    <Shell subMenu={getSubMenu()}>
      <Box className="SubFrms-outer-box">
        <Box className="SubFrms-inner-box">
          {/* Header */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              margin: '30px 110px -20px 110px'
            }}
          >
            <Typography variant="h5">Custom Form Order</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="body1"
                sx={{ mr: 1, color: 'text.secondary' }}
              >
                Enable Drag & Drop
              </Typography>
              <Switch
                onChange={handleToggleChange}
                checked={isToggled}
                color="primary"
              />
            </Box>
          </Box>

          {/* Forms list */}
          <Box sx={{ pt: 4 }}>
            {isToggled ? (
              <DndContext
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={formsList.map((form) => form.id)}
                  strategy={rectSortingStrategy}
                >
                  <Grid
                    container
                    spacing={3}
                    sx={{
                      px: '90px',
                      py: '60px',
                      maxWidth: '100%',
                      mx: 'auto'
                    }}
                  >
                    {formsList.map((card) => (
                      <SortableFormCard
                        key={card.id}
                        card={card}
                        onCardClick={handleCardClick}
                      />
                    ))}
                  </Grid>
                </SortableContext>
              </DndContext>
            ) : (
              <Grid
                container
                spacing={3}
                sx={{ px: '90px', py: '60px', maxWidth: '100%', mx: 'auto' }}
              >
                {formsList.length > 0 ? (
                  formsList.map((card) => (
                    <Grid item xs={12} sm={6} md={4} key={card.id}>
                      <CardComponent
                        card={card}
                        onCardClick={handleCardClick}
                        isDraggable={false}
                      />
                    </Grid>
                  ))
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '100%'
                    }}
                  >
                    <Typography variant="body1">No Forms Available</Typography>
                  </Box>
                )}
              </Grid>
            )}
          </Box>
        </Box>
      </Box>
    </Shell>
  );
};

export default CustomFormFillOrder;
