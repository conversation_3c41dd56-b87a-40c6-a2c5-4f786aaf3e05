import { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
  SelectChangeEvent
} from '@mui/material';

export default function Header() {
  const [titleFont, setTitleFont] = useState<string>('Montserrat');
  const [copyTextFont, setCopyTextFont] = useState<string>('Roboto');

  const handleFontChange = (event: SelectChangeEvent, type: string) => {
    if (type === 'title') setTitleFont(event.target.value as string);
    else if (type === 'copytext') setCopyTextFont(event.target.value as string);
  };

  const [bgColor, setBgColor] = useState<string>('#D3E0E6');
  const [cardColor, setCardColor] = useState<string>('#FFFFFF');
  const [primaryColor, setPrimaryColor] = useState<string>('#0E397C');
  const [secondaryColor, setSecondaryColor] = useState<string>('#F69E68');
  const [labelTextColor, setLabelTextColor] = useState<string>('#333333');
  const [selectionColor, setSelectionColor] = useState<string>('#0E397C');
  const [textColor, setTextColor] = useState<string>('#333333');
  const [linkColor, setLinkColor] = useState<string>('#2DC8F4');

  return (
    <Box
      sx={{
        maxWidth: 400,
        bgcolor: '#f8f8f8',
        borderRadius: '8px',
        boxShadow: '0 0 8px rgba(0, 0, 0, 0.1)'
      }}
    >
      <Box>
        <Typography
          sx={{
            color: '#ffffff',
            background: '#0E397C',
            fontSize: '20px',
            padding: '20px'
          }}
        >
          THEME BUILDER
        </Typography>
      </Box>
      <Box sx={{ padding: '30px' }}>
        <Box>
          <Typography variant="subtitle1">Theme</Typography>
          <ToggleButtonGroup
            exclusive
            aria-label="Theme"
            sx={{
              borderRadius: '6px',
              boxShadow: 'inset 0 0 2px rgba(0,0,0,0.2)',
              width: '100%'
            }}
          >
            <ToggleButton
              value="Light"
              sx={{
                flex: 1,
                border: '1px solid #0E397C',
                marginRight: '3px',
                '&.Mui-selected': {
                  bgcolor: '#f5f5f5',
                  color: '#0E397C',
                  border: '2px solid #0E397C'
                }
              }}
            >
              Light
            </ToggleButton>
            <ToggleButton
              value="Dark"
              sx={{
                flex: 1,
                border: '1px solid #0E397C',
                color: '#ffffff',
                background: '#242424',
                '&.Mui-selected': {
                  bgcolor: '#333',
                  color: '#fff',
                  border: '2px solid #0E397C'
                }
              }}
            >
              Dark
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Theme Name
          </Typography>
          <TextField fullWidth variant="outlined" />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Color Styles
          </Typography>
          <Box display="grid" gridTemplateColumns="1fr 1fr" gap={1}>
            <Typography>Bg. Color</Typography>
            <TextField
              type="color"
              sx={{
                padding: '0px',
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={bgColor}
              onChange={(e) => setBgColor(e.target.value)}
            />
            <Typography>Card Color</Typography>
            <TextField
              type="color"
              sx={{
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={cardColor}
              onChange={(e) => setCardColor(e.target.value)}
            />
            <Typography>Primary Color</Typography>
            <TextField
              type="color"
              sx={{
                '& input': {
                  marging: 'none',
                  padding: 'none'
                },
                '& div': {
                  width: '70px',
                  height: '70px',
                  marging: 'none',
                  padding: 'none'
                },
                '& fieldset': {
                  border: 'none',
                  width: 'none',
                  height: 'none',
                  bottom: 'none',
                  right: 'none',
                  top: 'none',
                  left: 'none',
                  margin: 'none'
                }
              }}
              value={primaryColor}
              onChange={(e) => setPrimaryColor(e.target.value)}
            />
            <Typography>Secondary Color</Typography>
            <TextField
              type="color"
              sx={{
                padding: '0px',
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={secondaryColor}
              onChange={(e) => setSecondaryColor(e.target.value)}
            />
            <Typography>Label Text Color</Typography>
            <TextField
              type="color"
              sx={{
                padding: '0px',
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={labelTextColor}
              onChange={(e) => setLabelTextColor(e.target.value)}
            />
            <Typography>Selection Color</Typography>
            <TextField
              type="color"
              sx={{
                padding: '0px',
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={selectionColor}
              onChange={(e) => setSelectionColor(e.target.value)}
            />
            <Typography>Text Color</Typography>
            <TextField
              type="color"
              sx={{
                padding: '0px',
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
            />
            <Typography>Link Color</Typography>
            <TextField
              type="color"
              sx={{
                padding: '0px',
                '& fieldset': {
                  border: 'none'
                }
              }}
              value={linkColor}
              onChange={(e) => setLinkColor(e.target.value)}
            />
          </Box>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Fonts
          </Typography>
          <FormControl fullWidth>
            <Typography variant="body2">Title Font</Typography>
            <Select
              value={titleFont}
              onChange={(e) => handleFontChange(e, 'title')}
              sx={{
                bgcolor: '#fff',
                borderRadius: '6px',
                boxShadow: '0 0 2px rgba(0,0,0,0.1)'
              }}
            >
              <MenuItem value="Montserrat">Montserrat</MenuItem>
              <MenuItem value="Roboto">Roboto</MenuItem>
              <MenuItem value="Arial">Arial</MenuItem>
            </Select>
          </FormControl>

          <FormControl fullWidth sx={{ mt: 2 }}>
            <Typography variant="body2">Copytext Font</Typography>
            <Select
              value={copyTextFont}
              onChange={(e) => handleFontChange(e, 'copytext')}
              sx={{
                bgcolor: '#fff',
                borderRadius: '6px',
                boxShadow: '0 0 2px rgba(0,0,0,0.1)'
              }}
            >
              <MenuItem value="Roboto">Roboto</MenuItem>
              <MenuItem value="Montserrat">Montserrat</MenuItem>
              <MenuItem value="Arial">Arial</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Buttons
          </Typography>
          <Box display="grid" gridTemplateColumns="repeat(3, 1fr)" gap={1}>
            <Button variant="contained" sx={{ bgcolor: '#800080' }}>
              Primary
            </Button>
            <Button variant="contained" sx={{ bgcolor: '#29ABE2' }}>
              Secondary
            </Button>
            <Button variant="contained" sx={{ bgcolor: '#3EB37F' }}>
              Success
            </Button>
            <Button variant="contained" sx={{ bgcolor: '#F24B4B' }}>
              Danger
            </Button>
            <Button
              variant="contained"
              sx={{ bgcolor: '#333333', color: '#fff' }}
            >
              Black
            </Button>
            <Button
              variant="outlined"
              sx={{ color: '#000', borderColor: '#000' }}
            >
              White
            </Button>
          </Box>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Button Style
          </Typography>

          <Button
            value="Sharp Edges"
            sx={{
              flex: 1,
              color: '#ffffff',
              bgcolor: '#2B2B2B',
              border: '1px solid #E0E0E0',
              '&.Mui-selected': {
                bgcolor: '#f5f5f5',
                border: '2px solid #0E397C'
              }
            }}
          >
            Sharp Edges
          </Button>
          <Button
            value="Rounded"
            sx={{
              flex: 1,
              bgcolor: '#2B2B2B',
              color: '#ffffff',
              borderRadius: '50px',
              margin: '5px',
              border: '1px solid #E0E0E0',
              '&.Mui-selected': {
                bgcolor: '#2B2B2B80',
                border: '2px solid #0E397C'
              }
            }}
          >
            Rounded
          </Button>
          <Button
            value="outlined"
            sx={{
              flex: 1,
              bgcolor: '#ffffff',
              color: '#2B2B2B',
              border: '1px solid #2B2B2B',
              '&.Mui-selected': {
                bgcolor: '#2B2B2B80',
                border: '2px solid #0E397C'
              }
            }}
          >
            Outlined
          </Button>
        </Box>
      </Box>
      <Box sx={{ background: '#0483BA', padding: '30px' }}>
        <Box sx={{ padding: '0px 50px' }}>
          <Button
            variant="contained"
            sx={{
              width: '220px',
              height: '50px',
              mt: 1,
              bgcolor: '#ffffff',
              color: '#2b2b2b'
            }}
          >
            PREVIEW
          </Button>
          <Button
            variant="contained"
            sx={{
              width: '220px',
              height: '50px',
              mt: 1,
              bgcolor: '#ffffff',
              color: '#2b2b2b'
            }}
          >
            APPLY STYLE
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
