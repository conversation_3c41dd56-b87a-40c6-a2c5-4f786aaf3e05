// Package Imports
import { useEffect, useMemo, useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  DialogContent,
  Button,
  Dialog,
  DialogActions,
  TextField,
  DialogTitle
} from '@mui/material';
import { useLocation, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import '../../../css/header-settings-styles.scss';
import '../../../css/index.scss';
import TagsInput from 'react-tagsinput';

// Local Imports
import { Icon, SubMenu } from '../../form.elements';
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  getformresponseasemail,
  saveformresponseasemail,
  updateformresponseasemail,
  updateformstatus
} from '../../../redux/reducers/form.reducer';
import { ThemeBuilder } from './ThemeBuilder';
import 'react-tagsinput/react-tagsinput.css';
import { RootState } from '../../../redux/reducers';

const FormHeader: React.FC<{
  type?: any;
  clickPreviewType?: any;
  displayStylePanel?: any;
  redirectLink?: any;
}> = ({ type, clickPreviewType, displayStylePanel, redirectLink }) => {
  const [activeSelection, setActiveSelection] = useState('mode');

  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { userType }: any = useSelector((state: AppState) => state.auth);
  const { form } = useSelector((state: RootState) => state.form);
  // const { activeTheme, themesData }: any = useSelector((state: AppState) => state.form);
  const location = useLocation();

  // Don not remove this code
  // New Theme Builder
  // const StylePanel = () => {
  //   const [titleFont, setTitleFont] = useState<string>('Montserrat');
  //   const [copyTextFont, setCopyTextFont] = useState<string>('Roboto');

  //   const handleFontChange = (event: SelectChangeEvent, type: string) => {
  //     if (type === 'title') setTitleFont(event.target.value as string);
  //     else if (type === 'copytext') setCopyTextFont(event.target.value as string);
  //   };

  //   const [themeElements, setThemeElements] = useState({});

  //   useEffect(() => {
  //     if (activeTheme && themesData?.length > 0) {
  //       setThemeElements(activeTheme);
  //       console.log("Active Theme: ", activeTheme)
  //     } else {
  //       const defaultTheme = {
  //         name: '',
  //         theme: {
  //           backgroundColor: '#ffffff',
  //           buttonTextColor: '#ffffff',
  //           buttonColor: '#F47B20',
  //           buttonStyle: 'standard',
  //           bodyFont: 'roboto',
  //           titleFont: 'roboto',
  //           linkColor: '#26BBFA',
  //           textColor: '#000000',
  //           navigationColor: '#d4d4d4',
  //           navigationTextColor: '#000000',
  //           sectionViewType: 'plain',
  //           mobileLayout: 'singleColumn',
  //           tabletLayout: 'singleColumn',
  //           headerImage: '',
  //         }
  //       };
  //       setThemeElements(defaultTheme);
  //       console.log("Default Theme: ", defaultTheme);
  //     }
  //     console.log("Themes Data: ", themesData, themeElements);
  //   }, [activeTheme]);

  //   const [bgColor, setBgColor] = useState<string>('#D3E0E6');
  //   const [cardColor, setCardColor] = useState<string>('#FFFFFF');
  //   const [primaryColor, setPrimaryColor] = useState<string>('#0E397C');
  //   const [secondaryColor, setSecondaryColor] = useState<string>('#F69E68');
  //   const [labelTextColor, setLabelTextColor] = useState<string>('#333333');
  //   const [selectionColor, setSelectionColor] = useState<string>('#0E397C');
  //   const [textColor, setTextColor] = useState<string>('#333333');
  //   const [linkColor, setLinkColor] = useState<string>('#2DC8F4');
  //   return (
  //     <Box
  //       sx={{
  //         maxWidth: 400,
  //         bgcolor: '#f8f8f8',
  //         borderRadius: '8px',
  //         boxShadow: '0 0 8px rgba(0, 0, 0, 0.1)',
  //       }}
  //     >
  //       <Box>
  //         <Box sx={{ background: "#0E397C", display: "flex", alignItems: 'center', justifyContent: "space-between", padding: "14px" }}>
  //           <Typography
  //             sx={{ color: '#ffffff', fontSize: "20px", }}
  //           >
  //             THEME BUILDER
  //           </Typography>
  //           <IconButton onClick={() => setActiveSelection("mode")}>
  //             <CloseIcon sx={{ color: 'white2.main' }} />
  //           </IconButton>
  //         </Box>

  //       </Box>

  //       <Box sx={{
  //         padding: '30px',
  //         height: 320,
  //         overflow: 'auto',
  //       }}>
  //         <Box>
  //           <Typography variant="subtitle1" sx={{ fontSize: "18px", color: "#2B2B2B", fontWeight: "500" }} >
  //             Theme
  //           </Typography>
  //           <ToggleButtonGroup
  //             exclusive
  //             aria-label="Theme"
  //             sx={{
  //               borderRadius: '6px',
  //               boxShadow: 'inset 0 0 2px rgba(0,0,0,0.2)',
  //               width: '100%',
  //             }}
  //           >
  //             <ToggleButton
  //               value="Light"
  //               sx={{
  //                 flex: 1,
  //                 border: '1px solid #0E397C',
  //                 marginRight: '3px',
  //                 '&.Mui-selected': {
  //                   bgcolor: '#f5f5f5',
  //                   color: '#0E397C',
  //                   border: '2px solid #0E397C',
  //                 },
  //               }}
  //             >
  //               Light
  //             </ToggleButton>
  //             <ToggleButton
  //               value="Dark"
  //               sx={{
  //                 flex: 1,
  //                 border: '1px solid #0E397C',
  //                 color: '#ffffff',
  //                 background: "#242424",
  //                 '&.Mui-selected': {
  //                   bgcolor: '#333',
  //                   color: '#fff',
  //                   border: '2px solid #0E397C',
  //                 },
  //               }}
  //             >
  //               Dark
  //             </ToggleButton>
  //           </ToggleButtonGroup>
  //         </Box>

  //         <Box sx={{ mb: 3 }}>
  //           <Typography variant="subtitle1" gutterBottom sx={{ fontSize: "18px", color: "#2B2B2B", fontWeight: "500" }}>
  //             Theme Name
  //           </Typography>
  //           <TextField fullWidth variant="outlined" />
  //         </Box>

  //         <Box sx={{ mb: 3 }}>
  //           <Typography variant="subtitle1" gutterBottom sx={{ fontSize: "18px", color: "#2B2B2B", fontWeight: "500" }} >
  //             Color Styles
  //           </Typography>
  //           <Box >
  //             <Box >
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }}>
  //                 <Box><Typography>Bg. Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={bgColor}
  //                     onChange={(e) => setBgColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }} >
  //                 <Box><Typography>Card Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={cardColor}
  //                     onChange={(e) => setCardColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }}>
  //                 <Box><Typography>Primary Color</Typography></Box>

  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={primaryColor}
  //                     onChange={(e) => setPrimaryColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }} >
  //                 <Box><Typography>Secondary Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={secondaryColor}
  //                     onChange={(e) => setSecondaryColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }} >
  //                 <Box> <Typography>Label Text Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={labelTextColor}
  //                     onChange={(e) => setLabelTextColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }} >
  //                 <Box><Typography>Selection Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={selectionColor}
  //                     onChange={(e) => setSelectionColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }} >
  //                 <Box> <Typography>Text Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={textColor}
  //                     onChange={(e) => setTextColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //               <Box sx={{ display: "flex", justifyContent: 'space-between' }} >
  //                 <Box> <Typography>Link Color</Typography></Box>
  //                 <Box>
  //                   <TextField
  //                     type="color"
  //                     sx={{
  //                       "& input": {
  //                         marging: 'none',
  //                         padding: "0px",
  //                         width: "30px",
  //                         height: "30px",
  //                         display: "flex",
  //                         alignSelf: "right"
  //                       },
  //                       "& fieldset": {
  //                         border: "none",
  //                         width: "none",
  //                         height: "none",
  //                         bottom: "none",
  //                         right: "none",
  //                         top: "none",
  //                         left: "none",
  //                         margin: "none",

  //                       }
  //                     }}
  //                     value={linkColor}
  //                     onChange={(e) => setLinkColor(e.target.value)}
  //                   />
  //                 </Box>
  //               </Box>
  //             </Box>
  //           </Box>
  //         </Box>

  //         <Box sx={{ mb: 3 }}>
  //           <Typography variant="subtitle1" gutterBottom sx={{ fontSize: "18px", color: "#2B2B2B", fontWeight: "500" }} >
  //             Fonts
  //           </Typography>
  //           <FormControl fullWidth>
  //             <Typography variant="body2">Title Font</Typography>
  //             <Select
  //               value={titleFont}
  //               onChange={(e) => handleFontChange(e, 'title')}
  //               sx={{
  //                 bgcolor: '#fff',
  //                 borderRadius: '6px',
  //                 boxShadow: '0 0 2px rgba(0,0,0,0.1)',
  //               }}
  //             >
  //               <MenuItem value="Montserrat">Montserrat</MenuItem>
  //               <MenuItem value="Roboto">Roboto</MenuItem>
  //               <MenuItem value="Arial">Arial</MenuItem>
  //             </Select>
  //           </FormControl>

  //           <FormControl fullWidth sx={{ mt: 2 }}>
  //             <Typography variant="body2">Copytext Font</Typography>
  //             <Select
  //               value={copyTextFont}
  //               onChange={(e) => handleFontChange(e, 'copytext')}
  //               sx={{
  //                 bgcolor: '#fff',
  //                 borderRadius: '6px',
  //                 boxShadow: '0 0 2px rgba(0,0,0,0.1)',
  //               }}
  //             >
  //               <MenuItem value="Roboto">Roboto</MenuItem>
  //               <MenuItem value="Montserrat">Montserrat</MenuItem>
  //               <MenuItem value="Arial">Arial</MenuItem>
  //             </Select>
  //           </FormControl>
  //         </Box>

  //         <Box sx={{ mb: 3 }}>
  //           <Typography variant="subtitle1" gutterBottom sx={{ fontSize: "18px", color: "#2B2B2B", fontWeight: "500" }} >
  //             Buttons
  //           </Typography>
  //           <Box display="grid" gridTemplateColumns="repeat(3, 1fr)" gap={1}>
  //             <Button variant="contained" sx={{ bgcolor: '#800080' }}>
  //               Primary
  //             </Button>
  //             <Button variant="contained" sx={{ bgcolor: '#29ABE2' }}>
  //               Secondary
  //             </Button>
  //             <Button variant="contained" sx={{ bgcolor: '#3EB37F' }}>
  //               Success
  //             </Button>
  //             <Button variant="contained" sx={{ bgcolor: '#F24B4B' }}>
  //               Danger
  //             </Button>
  //             <Button variant="contained" sx={{ bgcolor: '#333333', color: '#fff' }}>
  //               Black
  //             </Button>
  //             <Button variant="outlined" sx={{ color: '#000', borderColor: '#000' }}>
  //               White
  //             </Button>
  //           </Box>
  //         </Box>

  //         <Box sx={{ mb: 3 }}>
  //           <Typography variant="subtitle1" gutterBottom sx={{ fontSize: "18px", color: "#2B2B2B", fontWeight: "500" }} >
  //             Button Style
  //           </Typography>

  //           <Button
  //             value="Sharp Edges"
  //             sx={{
  //               flex: 1,
  //               color: "#ffffff",
  //               bgcolor: '#2B2B2B',
  //               border: '1px solid #E0E0E0',
  //               '&.Mui-selected': {
  //                 bgcolor: '#f5f5f5',
  //                 border: '2px solid #0E397C',
  //               },
  //             }}
  //           >
  //             Sharp Edges
  //           </Button>
  //           <Button
  //             value="Rounded"
  //             sx={{
  //               flex: 1,
  //               bgcolor: '#2B2B2B',
  //               color: "#ffffff",
  //               borderRadius: "50px",
  //               margin: "5px",
  //               border: '1px solid #E0E0E0',
  //               '&.Mui-selected': {
  //                 bgcolor: '#2B2B2B80',
  //                 border: '2px solid #0E397C',
  //               },
  //             }}
  //           >
  //             Rounded
  //           </Button>
  //           <Button
  //             value="outlined"
  //             sx={{
  //               flex: 1,
  //               bgcolor: '#ffffff',
  //               color: "#2B2B2B",
  //               border: '1px solid #2B2B2B',
  //               '&.Mui-selected': {
  //                 bgcolor: '#2B2B2B80',
  //                 border: '2px solid #0E397C',
  //               },
  //             }}
  //           >
  //             Outlined
  //           </Button>

  //         </Box>

  //       </Box>
  //       <Box sx={{ background: "#0483BA", padding: "30px" }}>
  //         <Box sx={{ padding: "0px 50px" }}>
  //           <Button
  //             variant="contained"
  //             sx={{
  //               width: '220px',
  //               height: "50px",
  //               mt: 1,
  //               bgcolor: '#ffffff',
  //               color: "#2b2b2b",

  //             }} >
  //             PREVIEW
  //           </Button>
  //           <Button
  //             variant="contained"
  //             sx={{
  //               width: '220px',
  //               height: "50px",
  //               mt: 1,
  //               bgcolor: '#ffffff',
  //               color: "#2b2b2b",
  //             }} >
  //             APPLY STYLE
  //           </Button>
  //         </Box>
  //       </Box>
  //     </Box>
  //   );
  // }

  const closeSettings = () => {
    setActiveSelection('');
  };

  const [open, setOpen] = useState(false);
  const [tags, setTags] = useState<any>([]);
  const [handleEmailInput, setHandleEmailInput] = useState(false);
  const [emailInput, setEmailInput]: any = useState();
  const [emailData, setEmailData]: any = useState();

  const [settingOptions, setSettingOptions] = useState([
    {
      name: 'Editable',
      status: true,
      disabled: true
    },
    {
      name: 'Template',
      status: false,
      disabled: true
    },
    {
      name: 'Secure',
      status: false,
      disabled: true
    },
    {
      name: 'Data Tables',
      status: false,
      disabled: true
    },
    {
      name: 'Printing',
      status: false,
      disabled: true
    },
    {
      name: 'Publish',
      status: false
    },
    {
      name: 'Send form responses as email',
      status: false
    },
    {
      name: 'Allow Multiple Responses',
      status: false
    },
    {
      name: 'Disable Form Responses',
      status: false
    }
  ]);

  const getData = async () => {
    const response = await dispatch(getformresponseasemail(id));
    if (response.payload) {
      setEmailData(response.payload);
      setHandleEmailInput(response.payload.sent_to_user);
      setEmailInput(response.payload.sent_to_user_or_other);
      setTags(response.payload.cc_emails);
    }
  };

  useMemo(() => {
    const searchParams = new URLSearchParams(location.search);

    const styles = searchParams.get('styles');

    if (styles) {
      setActiveSelection('styles');
    }
    if (id && userType === 'organization') {
      getData();
    }
  }, [id, userType]);

  useEffect(() => {
    setSettingOptions((prevOptions) =>
      prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
        mapSettingOptionIndex === 6
          ? {
              ...mapSettingOption,
              status: form?.form_response_as_email?.status || false
            }
          : mapSettingOption
      )
    );

    if (form?.status) {
      setSettingOptions((prevOptions) =>
        prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
          mapSettingOptionIndex === 5
            ? { ...mapSettingOption, status: form?.status }
            : mapSettingOption
        )
      );
    }

    if (form?.has_multiple_form_responses) {
      setSettingOptions((prevOptions) =>
        prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
          mapSettingOptionIndex === 7
            ? { ...mapSettingOption, status: form?.has_multiple_form_responses }
            : mapSettingOption
        )
      );
    }

    if (form?.is_disable_form_response) {
      setSettingOptions((prevOptions) =>
        prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
          mapSettingOptionIndex === 8
            ? { ...mapSettingOption, status: form?.is_disable_form_response }
            : mapSettingOption
        )
      );
    }
  }, [form]);

  const handleFormSettings = async (
    checkedStatus: boolean,
    optionIndex: number
  ) => {
    if (optionIndex === 6) {
      if (checkedStatus) {
        if (!emailData) {
          setOpen(true);
        } else {
          setSettingOptions((prevOptions) =>
            prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
              mapSettingOptionIndex === optionIndex
                ? { ...mapSettingOption, status: true }
                : mapSettingOption
            )
          );

          if (id) {
            await dispatch(
              updateformresponseasemail({
                id: emailData.form_res_as_email_id,
                data: {
                  ...emailData,
                  form_id: id,
                  status: true
                }
              })
            );
          }
        }
      } else {
        setSettingOptions((prevOptions) =>
          prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
            mapSettingOptionIndex === optionIndex
              ? { ...mapSettingOption, status: false }
              : mapSettingOption
          )
        );

        if (id) {
          await dispatch(
            updateformresponseasemail({
              id: emailData.form_res_as_email_id,
              data: {
                ...emailData,
                form_id: id,
                status: false
              }
            })
          );
        }
      }
      return;
    }

    let data: any = {};

    switch (optionIndex) {
      case 5:
        data = {
          status: checkedStatus
        };
        break;
      case 7:
        data = {
          has_multiple_form_responses: checkedStatus
        };
        break;
      case 8:
        data = {
          is_disable_form_response: checkedStatus
        };
        break;
      default:
        data = {};
    }

    setSettingOptions((prevOptions) =>
      prevOptions.map((mapSettingOption, mapSettingOptionIndex) =>
        mapSettingOptionIndex === optionIndex
          ? { ...mapSettingOption, status: checkedStatus }
          : mapSettingOption
      )
    );

    if (id) {
      await dispatch(updateformstatus({ id, data }));
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const handleTagsChange = (newTags: any) => {
    if (isValidEmail(newTags[newTags.length - 1])) {
      setTags(newTags);
    }
    if (newTags.length === 0 && tags.length > 0) {
      setTags([]);
    }
  };

  return (
    <Box className="position-relative w-100">
      <SubMenu
        backNavigation
        redirectLink={redirectLink}
        formActionButtons={type === 'form-builder'}
        activeSelection={activeSelection}
        setActiveSelection={setActiveSelection}
        previewActionButtons={type === 'preview-form'}
        clickPreviewType={clickPreviewType}
        displayForm={type === 'preview-form'}
        displayStylePanel={userType === 'organization' && displayStylePanel}
        id={id}
      />

      {activeSelection === 'styles' && (
        <DialogContent
          className="d-flex position-absolute justify-content-end"
          sx={{
            top: 62,
            right: '0',
            zIndex: '20'
          }}
        >
          {/* <StylePanel /> */}
          <ThemeBuilder type={type} setActiveSelection={setActiveSelection} />
        </DialogContent>
      )}

      {activeSelection === 'settings' && (
        <Box
          className="d-flex position-absolute justify-content-end"
          sx={{
            right: '0',
            zIndex: '20'
          }}
        >
          <Box className="d-flex justify-content-right bg-white overflow-auto p-30">
            <Box
              sx={{
                width: '260px',
                maxHeight: '80vh'
              }}
            >
              <Box className="d-flex justify-content-end">
                <Icon
                  name="Close"
                  fontSize="large"
                  sx={{ cursor: 'pointer' }}
                  onClick={closeSettings}
                />
              </Box>
              <Box>
                {settingOptions.map((item: any, index: number) => {
                  const key = `${index}-${index * 2}-item-key`;
                  return index === 6 && userType === 'super_admin' ? (
                    ''
                  ) : (
                    <Box
                      key={key}
                      className="d-flex-center"
                      sx={{
                        gap: 1.5,
                        height: '40px',
                        justifyContent:
                          index === 6 ? 'space-between' : 'flex-start',
                        width: '100%'
                      }}
                    >
                      <Box className="d-flex-center" sx={{ gap: 1.5 }}>
                        <input
                          type="checkbox"
                          name=""
                          id=""
                          checked={item?.status}
                          onChange={(e) =>
                            handleFormSettings(e.target.checked, index)
                          }
                          className="custom-checkbox"
                          style={{
                            width: '18px',
                            height: '18px',
                            margin: '0'
                          }}
                          disabled={item?.disabled}
                        />
                      </Box>
                      <Typography>{item?.name}</Typography>

                      {/* Add edit button for "Send form responses as email" option when it's enabled */}
                      {index === 6 && item.status && (
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => setOpen(true)}
                          sx={{
                            fontSize: '0.7rem',
                            padding: '2px 8px',
                            minWidth: '60px'
                          }}
                        >
                          Edit
                        </Button>
                      )}
                    </Box>
                  );
                })}
              </Box>
            </Box>
          </Box>
        </Box>
      )}
      <Box sx={{ padding: '20px' }}>
        <Dialog
          open={open}
          onClose={handleClose}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            component: 'form',
            onSubmit: async (event: React.FormEvent<HTMLFormElement>) => {
              event.preventDefault();
              const formData = new FormData(event.currentTarget);
              const formJson = Object.fromEntries((formData as any).entries());

              const data = {
                form_id: id,
                sent_to_user: formJson.registered === 'on',
                sent_to_user_or_other: formJson.email ? formJson.email : '',
                cc_emails: tags
              };

              try {
                if (emailData) {
                  // Update existing email configuration
                  const response = await dispatch(
                    updateformresponseasemail({
                      id: emailData.form_res_as_email_id,
                      data
                    })
                  );

                  if (response.payload) {
                    // Update the email data state with the new configuration
                    setEmailData(response.payload);

                    // Make sure the checkbox is checked
                    setSettingOptions((prevOptions) =>
                      prevOptions.map((option, index) =>
                        index === 6 ? { ...option, status: true } : option
                      )
                    );

                    handleClose();
                  }
                } else {
                  // Create new email configuration
                  const response = await dispatch(
                    saveformresponseasemail(data)
                  );

                  if (response.payload) {
                    // Update the email data state with the new configuration
                    setEmailData(response.payload);

                    // Make sure the checkbox is checked
                    setSettingOptions((prevOptions) =>
                      prevOptions.map((option, index) =>
                        index === 6 ? { ...option, status: true } : option
                      )
                    );

                    handleClose();
                  }
                }
              } catch (error) {
                console.error('Error saving email configuration:', error);
              }
            }
          }}
        >
          <DialogTitle
            sx={{
              color: '#000000'
            }}
          >
            Configure Form Response Email
          </DialogTitle>
          <DialogContent sx={{ paddingLeft: 7, paddingRight: 7 }}>
            <>
              <Typography sx={{ fontWeight: 700, marginBottom: 1 }}>
                To Mail
              </Typography>

              <Box
                className="d-flex-center"
                sx={{
                  gap: 1.5,
                  height: '40px',
                  marginBottom: 1
                }}
              >
                <input
                  type="checkbox"
                  name="registered"
                  id="registered"
                  checked={handleEmailInput}
                  onChange={() => {
                    setHandleEmailInput(!handleEmailInput);
                    setEmailInput('');
                  }}
                  className="custom-checkbox"
                  style={{
                    width: '18px',
                    height: '18px',
                    margin: '0'
                  }}
                />

                <Typography>Registered User Email</Typography>
              </Box>
              <Typography sx={{ fontWeight: 700, marginBottom: 1 }}>
                (OR)
              </Typography>

              <TextField
                autoFocus
                margin="dense"
                id="email"
                name="email"
                label="Custom Email"
                placeholder="Enter Email"
                type="email"
                value={emailInput}
                onChange={(e: any) => setEmailInput(e.target.value)}
                fullWidth
                variant="standard"
                InputLabelProps={{
                  shrink: true,
                  style: { color: 'black', fontSize: '25px' }
                }}
                InputProps={{
                  style: { marginTop: '40px', marginBottom: '10px' }
                }}
                required={!handleEmailInput}
                disabled={handleEmailInput}
              />
              <Typography sx={{ fontWeight: 700, marginBottom: 1 }}>
                CC Mails
              </Typography>

              <TagsInput value={tags} onChange={handleTagsChange} />
            </>
          </DialogContent>
          <DialogActions
            sx={{ paddingBottom: 5, paddingLeft: 6, paddingRight: 5 }}
          >
            <Button onClick={handleClose}>Cancel</Button>
            <Button type="submit" variant="contained" color="primary">
              {emailData ? 'Update Configuration' : 'Save Configuration'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Box>
  );
};

export default FormHeader;
