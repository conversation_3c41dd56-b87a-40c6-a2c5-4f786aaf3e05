// Package Imports
import {
  Box,
  IconButton,
  List,
  Divider,
  Paper,
  InputBase,
  Tooltip,
  Typography
} from '@mui/material';
import { useState, useMemo } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import SubtitlesIcon from '@mui/icons-material/Subtitles';
import CloseIcon from '@mui/icons-material/Close';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import { useDispatch, useSelector } from 'react-redux';
import '../../../css/add-section-template-styles.scss';
import '../../../css/index.scss';
import { toast } from 'react-toastify';

// Local Imports
import { useNavigate } from 'react-router-dom';
import { AppDispatch, AppState } from '../../../redux/app.store';
import { getformsections } from '../../../redux/reducers/form.reducer';
import { Icon } from '../../reusable/Icon';
// import { SnackbarElement } from '../../reusable/SnackbarElement';
import LoaderUI from '../../reusable/loaderUI';

const SectionTemplates: React.FC<{
  setShowTemplate: any;
  handleSelection: any;
}> = ({ setShowTemplate, handleSelection }) => {
  // const [snackbarOpen, setSnackbarOpen] = useState<{
  //   status: boolean;
  //   message: string;
  // }>({
  //   status: false,
  //   message: ''
  // });
  const dispatch = useDispatch<AppDispatch>();
  const { sectionTemplates, userType, templateSpinner, isLoading }: any =
    useSelector((state: AppState) => state.form);
  const navigate = useNavigate();

  const [sectionTemplatesList, setSectionTemplatesList] =
    useState(sectionTemplates);

  const fetchSectionTemplates = async () => {
    try {
      const response = await dispatch(getformsections(null));
      if (response.payload.status) {
        const filteredData =
          response.payload.data?.filter((form: any) => !form?.deleted_at) || [];
        setSectionTemplatesList(filteredData);
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     response?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (err) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const search = (event: any) => {
    if (event.target.value !== '') {
      const filteredData = sectionTemplates?.filter((form: any) =>
        form?.name.match(event.target.value)
      );
      setSectionTemplatesList(filteredData);
    } else {
      setSectionTemplatesList(sectionTemplates);
    }
  };

  useMemo(() => {
    fetchSectionTemplates();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Box className="h-full w-300 bg-FAF9F8">
        <Box className="bg-white flex-space-betweeen position-relative p-0-10 h-60">
          <Box className="d-flex align-items-center">
            <Box
              sx={{
                paddingRight: '10px',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <SubtitlesIcon className="color-0483BA" />
            </Box>
            <Typography className="color-616161">Section Templates</Typography>
          </Box>
          <Box className="d-flex align-items-center">
            {userType === 'super_admin' && (
              <Tooltip title="Create A New Section Template" arrow>
                <IconButton
                  sx={{
                    color: 'primary.main'
                  }}
                  onClick={() => navigate('/add-section-template')}
                >
                  <Icon name="AddCircleOutline" />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Close Section Template" arrow>
              <IconButton onClick={setShowTemplate}>
                <CloseIcon
                  sx={{
                    color: 'primary.main'
                    // color: "#EBEBEB"
                  }}
                />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Divider sx={{ borderColor: '#747878', border: '1px solid' }} />
        <List
          component="nav"
          aria-label="search"
          sx={{ pr: '10px', pl: '10px' }}
        >
          <Paper
            component="form"
            className="d-flex align-items-center"
            sx={{
              p: '2px 4px',
              borderRadius: '20px'
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1 }}
              placeholder="Search"
              inputProps={{ 'aria-label': 'search' }}
              onChange={search}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon sx={{ color: '#0483BA' }} />
            </IconButton>
          </Paper>
        </List>
        {isLoading && <LoaderUI />}
        {!isLoading && (
          <Box
            sx={{
              overflowY: 'auto',
              height: 'calc(100% - 120px)',
              width: '100%'
            }}
          >
            {sectionTemplatesList?.map((template: any, index: number) => {
              const key = `${index}-${index * 2}-template-key`;
              return (
                <Box
                  key={key}
                  className="flex-space-betweeen bg-white p-0-10"
                  sx={{
                    border: '1px solid #F0F0F0',
                    height: '62px',
                    cursor: 'pointer'
                  }}
                >
                  <Tooltip title={template?.name} arrow>
                    <Box
                      className="d-flex align-items-center"
                      sx={{
                        width: '230px'
                      }}
                      onClick={() =>
                        !templateSpinner &&
                        handleSelection(template?.section_id)
                      }
                    >
                      <IconButton>
                        <InsertDriveFileOutlinedIcon
                          sx={{ color: '#0483BA' }}
                        />
                      </IconButton>
                      <Typography
                        className="color-616161 overflow-hidden font-16 font-weight-400 w-200"
                        sx={{
                          // display: "flex",
                          // flexGrow: 1,
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          textTransform: 'capitalize'
                        }}
                      >
                        {template?.name}
                      </Typography>
                    </Box>
                  </Tooltip>

                  {userType === 'super_admin' && (
                    <Tooltip title="Edit Section Template" arrow>
                      <IconButton
                        onClick={() =>
                          navigate(
                            `/edit-section-template/${template?.section_id}`
                          )
                        }
                      >
                        <Icon
                          name="Edit"
                          sx={{
                            color: '#0483BA',
                            opacity: 0.7
                          }}
                        />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              );
            })}
          </Box>
        )}
      </Box>
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default SectionTemplates;
