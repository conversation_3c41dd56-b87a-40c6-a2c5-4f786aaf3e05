// Package Imports
import {
  <PERSON>,
  Divider,
  IconButton,
  InputBase,
  List,
  Paper,
  Tooltip,
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import CollectionsBookmarkOutlinedIcon from '@mui/icons-material/CollectionsBookmarkOutlined';
import ClassOutlinedIcon from '@mui/icons-material/ClassOutlined';
import '../../../css/index.scss';

const Suggestions: React.FC<{ setShowTemplate: any, handleSelection: any }> = ({
  setShowTemplate
}) => {
  // const { userType }: any = useSelector((state: AppState) => state.form);
  const suggetion = [
    { name: 'ADL', index: 1 },
    { name: 'Suggestion-2', index: 2 },
    { name: 'ADL', index: 1 },
    { name: 'Suggestion-2', index: 3 },
    { name: 'AD<PERSON>', index: 1 },
    { name: 'Suggestion-1', index: 4 },
    { name: 'Suggestion-2', index: 3 },
    { name: 'ADL', index: 1 },
    { name: 'Suggestion-1', index: 4 },
    { name: 'Suggestion-2', index: 3 },
    { name: 'ADL', index: 1 },
    { name: 'Suggestion-1', index: 4 },
    { name: 'Suggestion-2', index: 3 },
    { name: 'ADL', index: 1 },
    { name: 'Suggestion-1', index: 4 }
  ];

  return (
    <Box className="w-300 h-full bg-FAF9F8">
      <Box className="bg-white position-relative flex-space-betweeen h-60 p-0-10">
        <Box className="d-flex align-items-center">
          <IconButton>
            <CollectionsBookmarkOutlinedIcon className="color-0483BA" />
          </IconButton>
          <Typography className="color-616161">Suggestions</Typography>
        </Box>
        <Box className="d-flex align-items-center">
          {/* {userType == "super_admin" && (
            <Tooltip title={"Create A New Suggestion"}>
              <IconButton sx={{
                color: "primary.main",
              }}>
                <Icon
                  name="AddCircleOutline"
                />
              </IconButton>
            </Tooltip>
          )} */}
          <Tooltip title="Close Suggestions" arrow>
            <IconButton onClick={setShowTemplate}>
              <CloseIcon
                sx={{
                  color: 'primary.main'
                  // color: "#EBEBEB"
                }}
              />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Divider sx={{ borderColor: '#747878', border: '1px solid' }} />
      <List component="nav" aria-label="search" className="pr-10 pl-10">
        <Paper
          component="form"
          className="d-flex align-items-center"
          sx={{
            p: '2px 4px',
            borderRadius: '20px'
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search"
            inputProps={{ 'aria-label': 'search' }}
          />
          <IconButton type="button" className="p-10" aria-label="search">
            <SearchIcon className="color-0483BA" />
          </IconButton>
        </Paper>
      </List>

      <Box
        sx={{ overflowY: 'auto', height: 'calc(100% - 120px)', width: '100%' }}
      >
        {suggetion.map((sg: any, index: number) => {
          const key = `${index}-${index * 2}-sg-key`;
          return (
            <Box
              key={key}
              className="bg-white flex-space-betweeen p-0-10"
              sx={{
                border: '1px solid #F0F0F0',
                height: '62px',
                cursor: 'pointer'
              }}
            >
              <IconButton>
                <ClassOutlinedIcon className="color-0483BA" />
              </IconButton>
              <Tooltip title={sg?.name} arrow>
                <Typography
                  className="color-616161 font-16 font-weight-400 w-200 overflow-hidden textTransform-capitalize"
                  sx={{
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {sg?.name}
                </Typography>
              </Tooltip>
              {/* {userType == "super_admin" && (
                <Tooltip title={"Edit Section Template"}>
                  <IconButton>
                    <Icon
                      name="Edit"
                      sx={{
                        color: "#0483BA",
                        opacity: 0.7,
                      }}
                    />
                  </IconButton>
                </Tooltip>
              )} */}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};
export default Suggestions;
