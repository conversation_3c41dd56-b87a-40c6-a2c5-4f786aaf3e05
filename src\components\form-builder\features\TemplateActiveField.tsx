// Package Imports
import {
  Box,
  Button,
  Di<PERSON>r,
  FormControlLabel,
  IconButton,
  InputAdornment,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Select,
  Switch,
  TextField,
  Tooltip,
  Typography
} from '@mui/material';
import { useEffect, useState } from 'react';
import { ToWords } from 'to-words';
import ReactQuill from 'react-quill';

// Local Imports
import { Icon } from '../../reusable/Icon';
import { FormInput, FormSelect, SubmitButton } from '../../form.elements';
import { toPascalCase } from '../../../utils/functions';
import 'react-quill/dist/quill.snow.css';
import '../../../css/index.scss';
import { RootState } from '../../../redux/reducers';
import { useDispatch, useSelector } from 'react-redux';
import {
  getcountrieslist,
  getstateslist,
  updateSelectedCountry,
  updateSelectedState
} from '../../../redux/reducers/form.reducer';
import { AppDispatch } from '../../../redux/app.store';
import { useFormikContext } from 'formik';
import AntSwitch from '../../reusable/AntSwitch';

const toWords = new ToWords();

const TemplateActiveField: React.FC<{
  field: any;
  colIndex: any;
  formFields: any;
  formProps: any;
  setFormProps: any;
  formData: any;
  setFormData: any;
  handleInputChange: any;
  elements: any;
  changeElement: any;
  handleAddOption: any;
  updateParagraphText: any;
}> = ({
  field,
  colIndex,
  formFields,
  formProps,
  setFormProps,
  formData,
  setFormData,
  handleInputChange,
  elements,
  changeElement,
  handleAddOption,
  updateParagraphText
}) => {
  const {
    countriesList,
    statesList,
    citiesList,
    selectedCountry,
    selectedState
  }: any = useSelector((state: RootState) => state.form);
  const dispatch = useDispatch<AppDispatch>();
  const { values } = useFormikContext<any>();

  const [showDescription, setShowDescription] = useState<boolean>(false);
  const [showRepeatField, setShowRepeatField] = useState<any>(
    field?.is_iterative_or_not || false
  );
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [showFormControl, setShowFormControl] = useState<boolean>(false);
  const fieldLabelId = `fields[${formProps?.columnIndex}].label`;
  const fieldOriginalId = `fields[${formProps?.columnIndex}].original_field_id`;
  const fielddescription = `fields[${formProps?.columnIndex}].description`;
  const fieldIteration = `fields[${formProps?.columnIndex}].is_iterative_or_not`;
  const fieldMaxIterationLength = `fields[${formProps?.columnIndex}].iteration_max_length`;
  const fieldrequired = `fields[${formProps?.columnIndex}].validation_schema.required`;
  const [paragraphText, setParagraphText] = useState(field?.description || '');
  const [countriesData, setCountriesData] = useState([]);
  const [statesData, setStatesData] = useState([]);
  const [citiesData, setCitiesData] = useState([]);
  const [currency, setCurrency] = useState('');

  const small: any = {
    zero: 0,
    one: 1,
    two: 2,
    three: 3,
    four: 4,
    five: 5,
    six: 6,
    seven: 7,
    eight: 8,
    nine: 9,
    ten: 10,
    eleven: 11,
    twelve: 12,
    thirteen: 13,
    fourteen: 14,
    fifteen: 15,
    sixteen: 16,
    seventeen: 17,
    eighteen: 18,
    nineteen: 19,
    twenty: 20,
    twentyone: 21,
    twentytwo: 22,
    twentythree: 23,
    twentyfour: 24,
    twentyfive: 25,
    twentysix: 26,
    twentyseven: 27,
    twentyeight: 28,
    twentynine: 29,
    thirty: 30,
    forty: 40,
    fifty: 50,
    sixty: 60,
    seventy: 70,
    eighty: 80,
    ninety: 90
  };

  const addField = async () => {
    setFormProps((prevData: any) => ({
      ...prevData,
      previousColumnIndex: formProps?.columnIndex
    }));

    const textType = formFields?.find(
      (s: any) => s.skelton.input_type === 'text'
    );

    const city = formFields?.find((s: any) => s.skelton.input_type === 'city');

    const column = {
      name: `field_${toWords.convert(
        small[
          formData?.fields[formData.fields.length - 1]?.name
            .split('_')[1]
            .toLowerCase() || 'zero'
        ] + 1
      )}`,
      field_index: '',
      field_id: '',
      original_field_id: textType?.field_id,
      type: textType?.skelton?.type,
      input_type: textType?.skelton?.input_type,
      label: 'Text',
      placeHolder: '',
      description_status: false,
      description: '',
      validation_schema: {
        required: false
      },
      options: '',
      is_iterative_or_not: false,
      iteration_min_length: 1,
      iteration_max_length: 2,
      default_country: city ? city?.skelton?.default_country : '',
      default_state: city ? city?.skelton?.default_state : ''
    };

    await setFormData((prevFormData: any) => {
      const insertPosition = formProps.columnIndex + 1;

      const newFields = [
        ...prevFormData.fields.slice(0, insertPosition),
        column,
        ...prevFormData.fields.slice(insertPosition)
      ];

      return {
        ...prevFormData,
        fields: newFields
      };
    });

    setFormProps((prevData: any) => ({
      ...prevData,
      columnIndex: formProps.columnIndex + 1
    }));
  };

  const addDuplicateField = async (duplicateField: any) => {
    setFormProps((prevData: any) => ({
      ...prevData,
      previousColumnIndex: formProps?.columnIndex
    }));

    const column = {
      name: `field_${toWords.convert(
        small[
          formData?.fields[formData.fields.length - 1].name
            .split('_')[1]
            .toLowerCase()
        ] + 1
      )}`,
      field_index: '',
      field_id: '',
      original_field_id: duplicateField?.original_field_id,
      type: duplicateField?.type,
      input_type: duplicateField?.input_type,
      label: duplicateField?.label,
      placeHolder: duplicateField?.placeHolder,
      description_status: duplicateField?.description_status,
      description: duplicateField?.description,
      validation_schema: {
        required: duplicateField?.validation_schema?.required
      },
      options: duplicateField?.options,
      is_iterative_or_not: duplicateField?.is_iterative_or_not,
      iteration_min_length: duplicateField?.iteration_min_length,
      iteration_max_length: duplicateField?.iteration_max_length,
      default_country: duplicateField?.default_country,
      default_state: duplicateField?.default_state
    };

    await setFormData((prevFormData: any) => {
      const selectedIndex = prevFormData.fields.findIndex(
        (fld: any) => fld?.field_id === field?.field_id
      );
      const newFields = [
        ...prevFormData.fields.slice(0, selectedIndex + 1),
        column,
        ...prevFormData.fields.slice(selectedIndex + 1)
      ];

      return {
        ...prevFormData,
        fields: newFields
      };
    });
    setFormProps((prevData: any) => ({
      ...prevData,
      columnIndex: formProps.columnIndex + 1
    }));
  };

  const formFieldsData = formFields?.map((s: any) => {
    return {
      label: s.name,
      value: s.field_id,
      icon: s?.skelton?.icon ? toPascalCase(s?.skelton?.icon) : null
    };
  });

  const deleteField = async (name: any) => {
    const fieldIndex = formData.fields.findIndex(
      (dltField: any) => dltField.name === name
    );

    const data = formData.fields.filter(
      (formField: any) => formField.name !== name
    );

    setFormData((prevFormData: any) => ({
      ...prevFormData,
      fields: data
    }));

    setFormProps((prevData: any) => {
      let newColumnIndex = prevData.columnIndex;

      if (fieldIndex <= prevData.columnIndex) {
        newColumnIndex = Math.max(prevData.columnIndex - 1, 0);
      }

      return {
        ...prevData,
        columnIndex: newColumnIndex
      };
    });
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleConditionalInputClick = () => {
    setShowFormControl((prev) => !prev);
    handleClose();
  };

  const isStateFieldInSection = () => {
    return formData?.fields?.some(
      (fieldData: any) => fieldData.input_type === 'state'
    );
  };
  const isCountryFieldInSection = () => {
    return formData?.fields?.some(
      (fieldData: any) => fieldData.input_type === 'country'
    );
  };
  const getStatesData = async (countryCode: string) => {
    const res: any = await dispatch(getstateslist(countryCode));
    if (res?.payload) {
      const states = res.payload?.map((state: any) => ({
        value: state?.isoCode,
        label: state?.name
      }));
      setStatesData(states);
    }
  };
  const handleCountrySelect = async (e: any) => {
    const countryCode = e.target.value;
    await dispatch(updateSelectedCountry(countryCode));
    if (field?.input_type === 'city') {
      getStatesData(countryCode);
    }
    setFormData((prevFormData: any) => {
      const updatedFields = prevFormData.fields.map((upfield: any) =>
        upfield.name === field.name
          ? { ...field, default_country: countryCode }
          : upfield
      );

      return {
        ...prevFormData,
        fields: updatedFields
      };
    });
  };
  const handleStateSelect = async (e: any) => {
    const stateCode = e.target.value;
    dispatch(updateSelectedState(stateCode));
    setFormData((prevFormData: any) => {
      const updatedFields = prevFormData.fields.map((upfield: any) =>
        upfield.name === field.name
          ? { ...field, default_state: stateCode }
          : upfield
      );

      return {
        ...prevFormData,
        fields: updatedFields
      };
    });
  };
  const handleCurrency = async (e: any) => {
    const currencyCode = e.target.value;
    setCurrency(currencyCode);
    setFormData((prevFormData: any) => {
      const updatedFields = prevFormData.fields.map((upfield: any) =>
        upfield.name === field.name
          ? { ...field, value: currencyCode }
          : upfield
      );

      return {
        ...prevFormData,
        fields: updatedFields
      };
    });
  };

  useEffect(() => {
    if (field === 'new' && formFields) {
      addField();
    }
    if (
      !countriesList &&
      (field?.input_type === 'state' || field?.input_type === 'city')
    ) {
      (async () => {
        await dispatch(getcountrieslist(null));
      })();
    }

    if (countriesList && countriesData?.length === 0) {
      const countries = countriesList?.map((country: any) => ({
        value: country?.isoCode,
        label: country?.name
      }));
      setCountriesData(countries);
    }
    if (statesList && statesData?.length === 0) {
      const states = statesList?.map((state: any) => ({
        value: state?.isoCode,
        label: state?.name
      }));
      setStatesData(states);
    }
    if (citiesList && citiesData?.length === 0) {
      const cities = citiesList?.map((city: any) => ({
        value: city?.isoCode,
        label: city?.name
      }));
      setCitiesData(cities);
    }
    if (field?.default_country) {
      dispatch(updateSelectedCountry(field?.default_country));
      if (field?.input_type === 'city') {
        getStatesData(field?.default_country);
      }
    } else {
      dispatch(updateSelectedCountry(''));
    }
    if (field?.default_state) {
      dispatch(updateSelectedState(field?.default_state));
    } else {
      dispatch(updateSelectedState(''));
    }
    if (field?.value) {
      setCurrency(field?.value);
    }
    field.description_status
      ? setShowDescription(true)
      : setShowDescription(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [field, formFields, countriesList, statesList, citiesList]);

  const handlePragraphText = (value: any) => {
    setParagraphText(value);
    updateParagraphText(value, field?.field_id);
  };

  return (
    <Box>
      <Box
        sx={{
          backgroundColor: '#fff',
          borderRadius: '4px',
          padding: '30px 30px 10px 30px',
          boxShadow: '0px 1px 0px 2px #24242410',
          display: 'flex',
          flexDirection: 'column',
          gap: '10px',
          marginTop: '10px',
          borderLeft: '4px solid #36C0ED'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer'
          }}
        >
          <Icon
            name="OpenWith"
            sx={{
              fontSize: '18px'
            }}
          />
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            backgroundColor: '#fff',
            gap: '10px',
            paddingTop: '10px'
          }}
        >
          <Box sx={{ display: 'flex', flexGrow: 1, height: '40px' }}>
            <FormInput
              className="section-options"
              name={fieldLabelId}
              label=""
              type={field?.type}
              placeholder={field?.placeHolder ? field?.placeHolder : 'Options'}
              handleInputChange={handleInputChange}
              containerStyles={{
                width: '100%',
                height: '45px',
                margin: '0px',
                '& .section-options': {
                  marginTop: '0px',
                  height: '45px',
                  '& input': {
                    height: '45px',
                    padding: '0px 10px'
                  }
                }
              }}
            />
          </Box>

          <Box
            sx={{
              width: '300px',
              height: '45px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            {colIndex === formProps.columnIndex && (
              <FormSelect
                name={fieldOriginalId}
                data={formFieldsData}
                onChange={handleInputChange}
                containerStyles={{
                  width: '100%',
                  height: '45px',
                  marginBottom: '0px',
                  position: 'relative',
                  top: '0px',
                  padding: '0px',
                  '& .MuiInputBase-root': {
                    height: '45px',
                    margin: '0px',
                    paddingTop: '0px',
                    paddingBottom: '0px',
                    '& .MuiSelect-select': {
                      height: '45px',
                      margin: '0px',
                      paddingTop: '0px',
                      paddingBottom: '0px'
                    }
                  }
                }}
                iconStyles={{
                  height: '45px',
                  marginRight: '10px',
                  fontSize: '24px'
                }}
                menuStyles={{
                  height: '45px',
                  fontSize: '16px'
                }}
              />
            )}
          </Box>
        </Box>

        {showDescription && (
          <Box>
            <FormInput
              name={fielddescription}
              label=""
              handleInputChange={handleInputChange}
              placeholder="Enter Field Description"
              containerStyles={{
                width: '100%',
                height: '45px',
                margin: '0px',
                '& .MuiInputBase-root': {
                  marginTop: '0px',
                  height: '45px',
                  '& input': {
                    height: '45px',
                    padding: '0px 10px'
                  }
                }
              }}
            />
          </Box>
        )}

        {field?.original_field_id ===
          'ad014e76-af87-4aca-8514-46b278cff9f0' && (
          <Box>
            <ReactQuill
              theme="snow"
              value={paragraphText}
              onChange={handlePragraphText}
            />
          </Box>
        )}
        {elements?.radioElements && field?.input_type === 'radio' && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '20px 0px '
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                paddingRight: '10px'
              }}
            >
              {elements?.radioElements?.map((radio: any, index: number) => {
                const key = `${index}-${index * 2}-radio-key`;
                return (
                  <Box key={key}>
                    <TextField
                      id="input-with-icon-textfield"
                      // label="TextField"
                      onChange={(e) =>
                        changeElement(e, 'radio', index, field?.field_id)
                      }
                      defaultValue={radio?.label}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon
                              name="CircleOutlined"
                              sx={{
                                fontSize: '22px'
                              }}
                            />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="start">
                            <Icon name="CloseOutlined" />
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      sx={{
                        width: '100%',
                        height: '50px'
                      }}
                    />
                  </Box>
                );
              })}
              <Box>
                <TextField
                  id="input-with-icon-textfield"
                  placeholder="Add Option"
                  onClick={() => handleAddOption('radio', field?.field_id)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon
                          name="CircleOutlined"
                          sx={{
                            fontSize: '22px'
                          }}
                        />
                      </InputAdornment>
                    )
                  }}
                  variant="standard"
                  sx={{
                    width: '100%',
                    height: '50px'
                  }}
                />
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                width: '300px'
              }}
            >
              <Icon name="ReportGmailerrorredOutlined" color="primary" />
              <Typography>
                If you want to show multiple choice and user have to select only
                one choice.
              </Typography>
            </Box>
          </Box>
        )}
        {elements?.checkboxElements && field?.input_type === 'checkbox' && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '20px 0px '
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                paddingRight: '10px'
              }}
            >
              {elements?.checkboxElements?.map(
                (checkbox: any, index: number) => {
                  const key = `${index}-${index * 3}-checkbox-key`;
                  return (
                    <Box key={key}>
                      <TextField
                        id="input-with-icon-textfield"
                        onChange={(e) =>
                          changeElement(e, 'checkbox', index, field?.field_id)
                        }
                        defaultValue={checkbox?.label}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Icon
                                name="CheckBoxOutlineBlankOutlined"
                                sx={{
                                  fontSize: '22px'
                                }}
                              />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="start">
                              <Icon name="CloseOutlined" />
                            </InputAdornment>
                          )
                        }}
                        variant="standard"
                        sx={{
                          width: '100%',
                          height: '50px'
                        }}
                      />
                    </Box>
                  );
                }
              )}
              <Box>
                <TextField
                  id="input-with-icon-textfield"
                  placeholder="Add Option"
                  onClick={() => handleAddOption('checkbox', field?.field_id)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon
                          name="CheckBoxOutlineBlankOutlined"
                          sx={{
                            fontSize: '22px'
                          }}
                        />
                      </InputAdornment>
                    )
                  }}
                  variant="standard"
                  sx={{
                    width: '100%',
                    height: '50px'
                  }}
                />
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                width: '300px'
              }}
            >
              <Icon name="ReportGmailerrorredOutlined" color="primary" />
              <Typography>We can make multiple choices.</Typography>
            </Box>
          </Box>
        )}
        {elements?.selectElements && field?.type === 'select' && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '20px 0px '
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                paddingRight: '10px'
              }}
            >
              {elements?.selectElements?.map((select: any, index: number) => {
                const key = `${index}-${index * 3}-select-key`;
                return (
                  <Box key={key}>
                    <TextField
                      id="input-with-icon-textfield"
                      onChange={(e) =>
                        changeElement(e, 'select', index, field?.field_id)
                      }
                      defaultValue={select?.label}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon
                              name="CircleOutlined"
                              sx={{
                                fontSize: '22px'
                              }}
                            />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="start">
                            <Icon name="CloseOutlined" />
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      sx={{
                        width: '100%',
                        height: '50px'
                      }}
                    />
                  </Box>
                );
              })}
              <Box>
                <TextField
                  id="input-with-icon-textfield"
                  placeholder="Add Option"
                  onClick={() => handleAddOption('select', field?.field_id)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon
                          name="CircleOutlined"
                          sx={{
                            fontSize: '22px'
                          }}
                        />
                      </InputAdornment>
                    )
                  }}
                  variant="standard"
                  sx={{
                    width: '100%',
                    height: '50px'
                  }}
                />
              </Box>
            </Box>
          </Box>
        )}
        {elements?.toggleElements && field?.type === 'toggle' && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '20px 0px '
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                paddingRight: '10px'
              }}
            >
              {elements?.toggleElements?.map((toggle: any, index: number) => {
                const key = `${index}-${index * 3}-toggle-key`;
                return (
                  <Box key={key}>
                    <TextField
                      id="input-with-icon-textfield"
                      onChange={(e) =>
                        changeElement(e, 'toggle', index, field?.field_id)
                      }
                      defaultValue={toggle?.label}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon
                              name="CircleOutlined"
                              sx={{
                                fontSize: '22px'
                              }}
                            />
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      sx={{
                        width: '100%',
                        height: '50px'
                      }}
                    />
                  </Box>
                );
              })}
            </Box>
          </Box>
        )}

        {field?.input_type === 'state' && (
          <Box>
            {!isCountryFieldInSection() && (
              <Select
                value={selectedCountry || 'default'}
                onChange={handleCountrySelect}
                placeholder="Select Country"
                className="w-full"
              >
                <MenuItem value="default">Select Default Country</MenuItem>
                {countriesData.map((country: any) => (
                  <MenuItem key={country?.value} value={country?.value}>
                    {country?.label}
                  </MenuItem>
                ))}
              </Select>
            )}
          </Box>
        )}
        {field?.input_type === 'city' && (
          <Box className="d-flex flex-column gap-10">
            {!isCountryFieldInSection() && !isStateFieldInSection() && (
              <Select
                value={selectedCountry || 'default'}
                onChange={handleCountrySelect}
                placeholder="Select Country"
                className="w-full"
              >
                <MenuItem value="default">Select Default Country</MenuItem>
                {countriesData.map((country: any) => (
                  <MenuItem key={country?.value} value={country?.value}>
                    {country?.label}
                  </MenuItem>
                ))}
              </Select>
            )}
            {!isStateFieldInSection() && (
              <Select
                value={selectedState || 'default'}
                onChange={handleStateSelect}
                placeholder="Select State"
                className="w-full"
              >
                <MenuItem value="default">Select Default State</MenuItem>
                {statesData?.map((state: any) => (
                  <MenuItem key={state?.value} value={state?.value}>
                    {state?.label}
                  </MenuItem>
                ))}
              </Select>
            )}
          </Box>
        )}

        {field?.input_type === 'currency' && (
          <div>
            Select Currency Field
            <Select
              value={currency}
              onChange={handleCurrency}
              placeholder="Choose a currency"
              className="w-full"
            >
              <MenuItem value="usd">US Dollar</MenuItem>
              <MenuItem value="eur">Euro</MenuItem>
              <MenuItem value="inr">Indian Rupee</MenuItem>
              <MenuItem value="gbp">British Pound</MenuItem>
              <MenuItem value="aud">Australian Dollar</MenuItem>
            </Select>
          </div>
        )}

        <Divider sx={{ padding: '5px' }} />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'end',
            alignItems: 'center'
          }}
        >
          {showRepeatField && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              {field?.is_iterative_or_not && (
                <Box
                  sx={{
                    width: '100px'
                  }}
                >
                  <FormInput
                    name={fieldMaxIterationLength}
                    label=""
                    type="number"
                    inputMode="numeric"
                    containerStyles={{
                      margin: '0px',
                      height: '45px',
                      '& .MuiInputBase-formControl': {
                        marginTop: '0px'
                      },
                      '& input': {
                        marginTop: '0px',
                        height: '45px'
                      }
                    }}
                    onChange={(e: any) => handleInputChange(e, values)}
                  />
                </Box>
              )}
              <FormControlLabel
                name={fieldIteration}
                control={<Switch defaultChecked={field?.is_iterative_or_not} />}
                label="Repeat Field"
                labelPlacement="start"
                onChange={(e: any) => handleInputChange(e, values)}
              />
            </Box>
          )}
          <Tooltip title="Copy/Duplicate Field" arrow>
            <IconButton onClick={() => addDuplicateField(field)}>
              <Icon name="FileCopy" color="primary" fontSize="medium" />
            </IconButton>
          </Tooltip>

          {formData?.fields.length > 1 && (
            <Tooltip title="Delete Field" arrow>
              <IconButton onClick={() => deleteField(field?.name)}>
                <Icon name="DeleteOutline" color="primary" fontSize="medium" />
              </IconButton>
            </Tooltip>
          )}
          <Divider
            orientation="vertical"
            variant="middle"
            flexItem
            sx={{ padding: '15px 4px' }}
          />

          {field?.type !== 'toggle' && field?.type !== 'paragraph' && (
            <Box>
              <FormControlLabel
                name={fieldrequired}
                control={
                  <AntSwitch
                    defaultChecked={field?.validation_schema?.required}
                    inputProps={{ 'aria-label': 'ant design' }}
                    sx={{ marginLeft: '6px' }}
                    onChange={(e) => handleInputChange(e, values)}
                  />
                }
                label="Required"
                labelPlacement="start"
              />
            </Box>
          )}
          <Box sx={{ paddingLeft: '10px' }}>
            <Icon
              name="MoreVert"
              sx={{ cursor: 'pointer' }}
              onClick={handleClick}
            />
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              {field?.type != 'paragraph' && (
                <>
                  <MenuItem
                    onClick={() => {
                      setShowDescription(!showDescription);
                      handleClose();
                    }}
                  >
                    <ListItemText primary="Description" />

                    <ListItemIcon>
                      {showDescription && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(76, 175, 80, 0.2)',
                            borderRadius: '50%',
                            width: '24px',
                            height: '24px',
                            marginLeft: 'auto'
                          }}
                        >
                          <Icon
                            name="Check"
                            fontSize="small"
                            sx={{ color: '#4caf50' }}
                          />
                        </Box>
                      )}
                    </ListItemIcon>
                  </MenuItem>
                  <MenuItem onClick={handleConditionalInputClick}>
                    <ListItemText primary="Conditional Input" />

                    <ListItemIcon>
                      {showFormControl && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(76, 175, 80, 0.2)',
                            borderRadius: '50%',
                            width: '24px',
                            height: '24px',
                            marginLeft: 'auto'
                          }}
                        >
                          <Icon
                            name="Check"
                            fontSize="small"
                            sx={{ color: '#4caf50' }}
                          />
                        </Box>
                      )}
                    </ListItemIcon>
                  </MenuItem>
                  <MenuItem>
                    <ListItemText primary="Custom Validations" />
                  </MenuItem>
                </>
              )}
              <MenuItem
                onClick={() => {
                  setShowRepeatField(!showRepeatField);
                  handleClose();
                }}
              >
                <ListItemText primary="Repeat Field" />
                <ListItemIcon>
                  {showRepeatField && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(76, 175, 80, 0.2)',
                        borderRadius: '50%',
                        width: '24px',
                        height: '24px',
                        marginLeft: 'auto'
                      }}
                    >
                      <Icon
                        name="Check"
                        fontSize="small"
                        sx={{ color: '#4caf50' }}
                      />
                    </Box>
                  )}
                </ListItemIcon>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          float: 'right',
          marginTop: '10px',
          marginBottom: '20px',
          height: '50px',
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end'
        }}
      >
        <Box
          sx={{
            boxShadow: '0px 2px 4px 0px rgba(0,0,0,0.3)',
            borderRadius: '4px',
            padding: '3px',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            width: 'fit-content'
          }}
        >
          <Button
            startIcon={<Icon name="AddCircleOutline" color="primary" />}
            onClick={addField}
            sx={{
              color: '#616161',
              height: '45px',
              padding: '0px 20px',
              '&:hover': {
                backgroundColor: '#FFF'
              }
            }}
          >
            Add Input
          </Button>
          <Divider
            orientation="vertical"
            variant="middle"
            flexItem
            sx={{ padding: '15px 4px' }}
          />
          {formProps.columnIndex === formData.fields.length - 1 && (
            <SubmitButton
              title="Submit"
              startIcon={<Icon name="TurnedInNotOutlined" color="primary" />}
              sx={{
                color: '#616161',
                backgroundColor: 'transparent',
                height: '45px',
                padding: '0px 20px',
                boxShadow: 'none',
                '&:hover': {
                  backgroundColor: '#FFF'
                }
              }}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};
export default TemplateActiveField;
