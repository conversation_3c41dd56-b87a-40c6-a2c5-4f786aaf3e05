// Package Imports
import { Box } from '@mui/material';

// Local Imports
import { Icon } from '../../reusable/Icon';
import {
  FormCheckbox,
  FormFileInput,
  FormInput,
  FormParagraph,
  FormPhoneInput,
  FormRadio,
  FormSelect,
  FormSignature,
  FormToggle,
  SubmitButton
} from '../../form.elements';
import { getCanvasWidth } from '../../../utils/functions';
import '../../../css/index.scss';

const TemplateInactiveField: React.FC<{
  colIndex: any;
  formData: any;
  field: any;
  countries: any;
  updateColIndex: any;
  handleSelectField: any;
}> = ({
  colIndex,
  formData,
  field,
  countries,
  updateColIndex,
  handleSelectField
}) => {
  const handleFieldsClick = () => {
    updateColIndex(colIndex);
    handleSelectField(field);
  };

  const generateSignatureNames = (type: 'date' | 'agree'): string => {
    const fieldName = `${field.name}_${type}`;
    return fieldName;
  };
  // console.log(field, 'inactive');
  return (
    <>
      <Box
        className="bg-white d-flex flex-column mt-10 justify-content-center align-items-center"
        sx={{
          borderRadius: '4px',
          padding: '30px 30px 30px 30px',
          boxShadow: '0px 1px 0px 2px #24242410'
        }}
        onClick={handleFieldsClick}
      >
        <Box className=" cursor-pointer">
          <Icon
            name="OpenWith"
            sx={{
              fontSize: '18px'
            }}
          />
        </Box>
        {field?.input_type !== 'phone' &&
          field?.input_type !== 'fax' &&
          field?.input_type !== 'file' &&
          field?.input_type !== 'date' &&
          field?.input_type !== 'time' &&
          field?.input_type !== 'datetime-local' &&
          field?.input_type !== 'checkbox' &&
          field?.input_type !== 'radio' &&
          field?.type !== 'textarea' &&
          field?.type !== 'select' &&
          field?.type !== 'signature' &&
          field?.type !== 'image' &&
          field?.type !== 'paragraph' &&
          field?.type !== 'address' &&
          field?.type !== 'editor' &&
          field?.type !== 'scribble' &&
          field?.type !== 'toggle' && (
            <FormInput
              type={field?.type}
              inputMode={field?.input_type}
              description={field?.description_status ? field?.description : ''}
              name={field?.name}
              label={field?.label}
              containerStyles={{
                width: '100%'
              }}
            />
          )}

        {field?.input_type === 'phone' && (
          <FormPhoneInput
            name={field?.name}
            label={field?.label}
            description={field?.description_status ? field?.description : ''}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'fax' && (
          <FormPhoneInput
            name={field?.name}
            label={field?.label}
            description={field?.description_status ? field?.description : ''}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'date' && (
          <FormInput
            name={field?.name}
            type={field?.input_type}
            description={field?.description_status ? field?.description : ''}
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'time' && (
          <FormInput
            name={field?.name}
            type={field?.input_type}
            description={field?.description_status ? field?.description : ''}
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'datetime-local' && (
          <FormInput
            name={field?.name}
            type={field?.input_type}
            description={field?.description_status ? field?.description : ''}
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'checkbox' && (
          <FormCheckbox
            data={field?.options}
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'radio' && (
          <FormRadio
            data={field?.options}
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'toggle' && (
          <FormToggle
            name={field?.name}
            label={field?.label}
            data={field?.options}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'textarea' && (
          <FormInput
            name={field?.name}
            type="text"
            label={field?.label}
            description={field?.description_status ? field?.description : ''}
            multiline
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'select' && (
          <FormSelect
            name={field?.name}
            label={field?.label}
            data={field?.options}
            containerStyles={{
              width: '100%'
            }}
          />
        )}

        {field?.type === 'signature' && (
          <FormSignature
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
            canvasProps={{
              width: getCanvasWidth(),
              height: 180
            }}
            imageProps={{
              width: getCanvasWidth(),
              height: 180
            }}
            dateName={generateSignatureNames('date')}
            agreeName={generateSignatureNames('agree')}
          />
        )}
        {field?.type === 'image' && (
          <FormFileInput
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            type="file"
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.input_type === 'file' && field?.type === 'input' && (
          <FormFileInput
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            type="file"
            label={field?.label}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'paragraph' && (
          <FormParagraph
            label={field?.label}
            description={field?.description}
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'address' && field?.input_type === 'country' && (
          <FormSelect
            name={field?.name}
            label={field?.label}
            description={field?.description_status ? field?.description : ''}
            data={countries}
            placeholder="Select Country"
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'address' && field?.input_type === 'state' && (
          <FormSelect
            name={field?.name}
            label={field?.label}
            description={field?.description_status ? field?.description : ''}
            data={field?.options}
            placeholder="Select State"
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'address' && field?.input_type === 'city' && (
          <FormSelect
            name={field?.name}
            label={field?.label}
            description={field?.description_status ? field?.description : ''}
            data={field?.options}
            placeholder="Select City"
            containerStyles={{
              width: '100%'
            }}
          />
        )}
        {field?.type === 'address' && field?.input_type === 'zipcode' && (
          <FormInput
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            type="text"
            label={field?.label}
            containerStyles={{
              width: {
                xs: '100%'
              }
            }}
          />
        )}
        {field?.type === 'editor' && (
          <FormInput
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            type="text"
            label={field?.label}
            containerStyles={{
              width: {
                xs: '100%'
              }
            }}
          />
        )}
        {field?.type === 'scribble' && (
          <FormInput
            name={field?.name}
            description={field?.description_status ? field?.description : ''}
            type="text"
            label={field?.label}
            containerStyles={{
              width: {
                xs: '100%'
              }
            }}
          />
        )}
      </Box>

      {colIndex === formData.fields.length - 1 && (
        <Box
          className="d-flex justify-content-end mt-10 h-50 w-full"
          sx={{
            float: 'right',
            marginBottom: '20px'
          }}
        >
          <Box
            className="d-flex align-items-center gap-6"
            sx={{
              boxShadow: '0px 2px 4px 0px rgba(0,0,0,0.3)',
              borderRadius: '4px',
              padding: '3px',
              width: 'fit-content'
            }}
          >
            <SubmitButton
              title="Submit"
              startIcon={<Icon name="TurnedInNotOutlined" color="primary" />}
              sx={{
                color: '#616161',
                backgroundColor: 'transparent',
                height: '45px',
                padding: '0px 20px',
                boxShadow: 'none',
                '&:hover': {
                  backgroundColor: '#FFF'
                }
              }}
            />
          </Box>
        </Box>
      )}
    </>
  );
};
export default TemplateInactiveField;
