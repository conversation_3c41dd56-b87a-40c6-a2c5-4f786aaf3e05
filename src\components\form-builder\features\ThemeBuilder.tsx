import { useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  CardMedia,
  Divider,
  FormControl,
  InputBase,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material';
import { Link } from 'react-router-dom';
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  activatetheme,
  addtheme,
  getthemes,
  updatetheme
} from '../../../redux/reducers/form.reducer';

import { Icon } from '../../reusable/Icon';
import '../../../css/index.scss';
import { ThemeBuilderTypes } from '../../../types';

export const ThemeBuilder: React.FC<ThemeBuilderTypes> = ({
  type,
  setActiveSelection = null
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { appId }: any = useSelector((state: AppState) => state.form);

  const [activeButton, setActiveButton] = useState('Select Theme');
  const [selectedTheme, setSelectedTheme] = useState('');
  const [selectedThemeError, setSelectedThemeError] = useState('');
  const [themesList, setThemesList]: any = useState();
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [buttonTextColor, setButtonTextColor] = useState('#ffffff');
  const [buttonColor, setButtonColor] = useState('#F47B20');
  const [buttonStyle, setButtonStyle] = useState('standard');
  const [titleFont, setTitleFont] = useState('roboto');
  const [bodyFont, setBodyFont] = useState('roboto');
  const [linkColor, setLinkColor] = useState('#26BBFA');
  const [textColor, setTextColor] = useState('#000000');
  const [navigationColor, setNavigationColor] = useState('#d4d4d4');
  const [navigationTextColor, setNavigationTextColor] = useState('#000000');
  const [activeTheme, setActiveTheme]: any = useState();
  const [newTheme, setNewTheme] = useState('');
  const [logo, setLogo]: any = useState(null);
  const inputLogo: any = useRef(null);
  const [showPreview, setShowPreview] = useState(false);

  const uploadLogo = async () => {
    inputLogo.current.click();
  };
  const handleLogoChange = async (event: any) => {
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      setLogo(reader.result);
    };
  };

  const getTheme = async () => {
    const res = await dispatch(getthemes(appId));
    if (res.payload.status) {
      setThemesList(res.payload.data);
      if (res.payload.data.length > 0) {
        setActiveButton('Select Theme');

        const appActiveTheme = res.payload.data?.find(
          (theme: any) => theme.status
        );

        if (appActiveTheme) {
          setActiveTheme(appActiveTheme);
          setSelectedTheme(appActiveTheme.name);
          setBackgroundColor(appActiveTheme.theme.backgroundColor);
          setButtonTextColor(appActiveTheme.theme.buttonTextColor);
          setButtonColor(appActiveTheme.theme.buttonColor);
          setButtonStyle(appActiveTheme.theme.buttonStyle);
          setTitleFont(appActiveTheme.theme.titleFont);
          setBodyFont(appActiveTheme.theme.bodyFont);
          setLinkColor(appActiveTheme.theme.linkColor);
          setTextColor(appActiveTheme.theme.textColor);
          setNavigationColor(appActiveTheme.theme.navigationColor);
          setNavigationTextColor(appActiveTheme.theme.navigationTextColor);
          setLogo(appActiveTheme.theme.headerImage);
        }
      } else {
        setActiveButton('New Theme');
      }
    }
  };

  const applyTheme = async () => {
    setSelectedThemeError(selectedThemeError);
    const data = {
      name: activeButton === 'New Theme' ? newTheme : selectedTheme,
      theme: {
        backgroundColor,
        bodyFont,
        buttonColor,
        buttonStyle,
        buttonTextColor,
        linkColor,
        mobileLayout: 'singleColumn',
        navigationColor,
        navigationTextColor,
        sectionViewType: 'plain',
        tabletLayout: 'singleColumn',
        textColor,
        titleFont,
        headerImage: logo
      }
    };
    if (themesList?.length > 0 && activeButton === 'Select Theme') {
      const res = await dispatch(
        updatetheme({
          appId,
          themeId: activeTheme?.theme_id,
          data
        })
      );
      const res2 = await dispatch(
        activatetheme({
          appId,
          themeId: activeTheme?.theme_id,
          data
        })
      );
      if (res.payload.status && res2.payload.status) {
        getTheme();
        setActiveSelection('mode');
      }
    } else {
      const res = await dispatch(addtheme({ appId, data }));
      if (res.payload.status) {
        getTheme();
        setActiveSelection('mode');
      }
    }
  };
  useMemo(() => {
    if (type === 'preview-form') {
      getTheme();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const closeSettings = () => {
    setActiveSelection('');
    setShowPreview(false);
  };

  return (
    <Box
      className="position-relative w-full bg-f6f6f6 d-flex p-20"
      sx={{
        maxHeight: '65vh',
        overflowY: 'auto',
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)'
      }}
    >
      {/* Preview */}
      {showPreview && (
        <Box
          className="position-sticky"
          sx={{
            top: '0px',
            minWidth: '300px',
            marginRight: '20px',
            height: '59vh',
            maxHeight: '100vh',
            overflowY: 'auto',
            paddingRight: '20px'
          }}
        >
          <Typography
            variant="h6"
            className="color-2B2B2B justify-content-center d-flex"
            sx={{
              fontWeight: 'bold'
            }}
          >
            Preview
          </Typography>
          <Box
            sx={{
              padding: '16px',
              backgroundColor,
              color: textColor,
              fontFamily: titleFont,
              borderRadius: '8px',
              boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* Navigation Preview */}
            <Box
              sx={{
                backgroundColor: navigationColor,
                color: navigationTextColor,
                padding: '8px',
                borderRadius: '4px',
                marginBottom: '16px'
              }}
            >
              Navigation Bar
            </Box>

            {/* Content Preview */}
            <Typography
              variant="h5"
              sx={{ fontWeight: 'bold', marginBottom: '8px' }}
            >
              This is a Heading
            </Typography>
            <Typography sx={{ marginBottom: '16px' }}>
              This is some sample text to demonstrate the text color.
            </Typography>

            {/* Link Preview */}
            <Link
              to={window.location}
              style={{
                color: linkColor, // Applies the color based on the variable `linkColor`
                textDecoration: 'none' // Removes the underline
              }}
            >
              Click Here
            </Link>

            {/* Button Preview */}
            <Box sx={{ marginTop: '16px' }}>
              <Button
                sx={{
                  backgroundColor: buttonColor,
                  color: buttonTextColor,
                  borderRadius: buttonStyle === 'rounded' ? '50px' : '4px',
                  padding: '8px 16px',
                  '&:hover': {
                    backgroundColor: buttonColor,
                    opacity: 0.9
                  }
                }}
              >
                Button
              </Button>
            </Box>
          </Box>
          <Box
            className="text-center d-flex gap-10 justify-content-center"
            sx={{ marginTop: 2 }}
          >
            <Button
              variant="outlined"
              color="primary"
              onClick={() => setShowPreview(false)}
            >
              Close Preview
            </Button>
            <Button variant="contained" color="primary" onClick={applyTheme}>
              Apply Theme
            </Button>
          </Box>
        </Box>
      )}
      <Box sx={{ padding: '10px' }}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Icon
            name="Close"
            fontSize="medium"
            sx={{ cursor: 'pointer' }}
            onClick={closeSettings}
          />
        </Box>
        {/* Buttons to select themes */}
        <Box
          className="d-flex justify-content-space-around w-full bg-f5f5f5"
          sx={{
            borderRadius: '10px',
            padding: '5px',
            marginBottom: 2,
            maxHeight: '70vh',
            overflowY: 'auto'
          }}
        >
          {themesList?.length > 0 &&
            ['Select Theme', 'New Theme'].map((btn) => (
              <Button
                key={btn}
                variant={activeButton === btn ? 'contained' : 'text'}
                sx={{
                  color: activeButton === btn ? '#fff' : '#08366b',
                  backgroundColor:
                    activeButton === btn ? '#08366b' : 'transparent',
                  borderRadius: '10px',
                  textTransform: 'none',
                  fontWeight: 'bold',
                  boxShadow:
                    activeButton === btn
                      ? '0px 2px 4px rgba(0, 0, 0, 0.1)'
                      : 'none',
                  transition: 'background-color 0.3s, color 0.3s'
                }}
                onClick={() => {
                  setActiveButton(btn);
                  if (btn === 'Select Theme') {
                    themesList?.forEach((theme: any) => {
                      if (theme.name === selectedTheme) {
                        setActiveTheme(theme);
                        setBackgroundColor(theme.theme.backgroundColor);
                        setButtonTextColor(theme.theme.buttonTextColor);
                        setButtonColor(theme.theme.buttonColor);
                        setButtonStyle(theme.theme.buttonStyle);
                        setTitleFont(theme.theme.titleFont);
                        setBodyFont(theme.theme.bodyFont);
                        setLinkColor(theme.theme.linkColor);
                        setTextColor(theme.theme.textColor);
                        setNavigationColor(theme.theme.navigationColor);
                        setNavigationTextColor(theme.theme.navigationTextColor);
                        setLogo(theme.theme.headerImage);
                      }
                    });
                  } else {
                    setActiveTheme({});
                    setBackgroundColor('#ffffff');
                    setButtonTextColor('#ffffff');
                    setButtonColor('#F47B20');
                    setButtonStyle('standard');
                    setTitleFont('roboto');
                    setBodyFont('roboto');
                    setLinkColor('#26BBFA');
                    setTextColor('#000000');
                    setNavigationColor('#d4d4d4');
                    setNavigationTextColor('#000000');
                    setLogo(null);
                  }
                }}
              >
                {btn}
              </Button>
            ))}
        </Box>

        {/* Theme selection */}
        {activeButton === 'Select Theme' && (
          <Box sx={{ minWidth: 120, marginBottom: 2 }}>
            <Typography sx={{ marginBottom: 1, fontWeight: '500' }}>
              Select Theme
            </Typography>
            <FormControl fullWidth variant="outlined">
              <Select
                value={selectedTheme}
                onChange={(e: any) => {
                  setSelectedTheme(e.target.value);
                  themesList?.forEach((theme: any) => {
                    if (theme.name === e.target.value) {
                      setActiveTheme(theme);
                      setBackgroundColor(theme.theme.backgroundColor);
                      setButtonTextColor(theme.theme.buttonTextColor);
                      setButtonColor(theme.theme.buttonColor);
                      setButtonStyle(theme.theme.buttonStyle);
                      setTitleFont(theme.theme.titleFont);
                      setBodyFont(theme.theme.bodyFont);
                      setLinkColor(theme.theme.linkColor);
                      setTextColor(theme.theme.textColor);
                      setNavigationColor(theme.theme.navigationColor);
                      setNavigationTextColor(theme.theme.navigationTextColor);
                      setLogo(theme.theme.headerImage);
                    }
                  });
                }}
                displayEmpty
              >
                {themesList?.map((theme: any) => {
                  return (
                    <MenuItem key={theme?.id} value={theme?.name}>
                      {theme?.name}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
        )}

        {activeButton === 'New Theme' && (
          <Box sx={{ marginBottom: 2 }}>
            <Typography sx={{ marginBottom: 1, fontWeight: '500' }}>
              Theme Name
            </Typography>
            <TextField
              value={newTheme}
              fullWidth
              variant="outlined"
              placeholder="Theme Name"
              className="w-full"
              onChange={(e) => setNewTheme(e.target.value)}
            />
          </Box>
        )}

        {/* Include Header Section with Image Selection */}
        <Divider sx={{ marginY: 1 }} />
        <Box sx={{ marginBottom: 2 }}>
          <Typography
            sx={{ marginBottom: 1, fontSize: '18px', fontWeight: 'bold' }}
          >
            Header
          </Typography>
          <Box
            className="d-flex justify-content-space-between align-items-center bg-fafafa"
            sx={{
              marginBottom: 2,
              padding: '8px 12px',
              borderRadius: '8px'
            }}
          >
            <Typography sx={{ fontWeight: '500' }}>Choose Image</Typography>
            <Icon
              name="Image"
              color="primary"
              sx={{ cursor: 'pointer' }}
              onClick={uploadLogo}
            />
            <Box
              className="bg-white"
              sx={{
                borderRadius: '10px',
                padding: '15px'
              }}
            >
              <CardMedia
                sx={{ height: 60, width: 60 }}
                image={logo}
                title="Logo"
              />
              <input
                type="file"
                ref={inputLogo}
                style={{ display: 'none' }}
                onChange={handleLogoChange}
              />
            </Box>
          </Box>
        </Box>
        <Divider sx={{ marginY: 2 }} />
        {/* Color pickers */}
        <Typography
          sx={{ marginBottom: 1, fontSize: '18px', fontWeight: 'bold' }}
        >
          Color Styles
        </Typography>

        {[
          {
            label: 'Background Color',
            value: backgroundColor,
            setter: setBackgroundColor
          },
          { label: 'Text Color', value: textColor, setter: setTextColor },
          { label: 'Link Color', value: linkColor, setter: setLinkColor },
          {
            label: 'Button Text Color',
            value: buttonTextColor,
            setter: setButtonTextColor
          },
          {
            label: 'Button Color',
            value: buttonColor,
            setter: setButtonColor
          },
          {
            label: 'Navbar Color',
            value: navigationColor,
            setter: setNavigationColor
          },
          {
            label: 'Navbar Text Color',
            value: navigationTextColor,
            setter: setNavigationTextColor
          }
        ].map((item) => (
          <Box
            key={item.label}
            className="d-flex justify-content-space-between align-items-center bg-fafafa"
            sx={{
              marginBottom: 2,
              padding: '8px 12px',
              borderRadius: '8px'
            }}
          >
            <Typography sx={{ fontWeight: '500' }}>{item.label}</Typography>
            <InputBase
              type="color"
              value={item.value}
              onChange={(e: any) => item.setter(e.target.value)}
              className="cursor-pointer"
              sx={{
                width: '24px',
                height: '24px',
                padding: '2px',
                borderRadius: '5px',
                border: '1px solid #ddd'
              }}
            />
          </Box>
        ))}

        <Divider sx={{ marginY: 2 }} />

        {/* Fonts */}
        <Typography
          sx={{ marginBottom: 1, fontSize: '18px', fontWeight: 'bold' }}
        >
          Fonts
        </Typography>

        <Box sx={{ minWidth: 120, marginBottom: 3 }}>
          <Typography sx={{ marginBottom: 1, fontWeight: '500' }}>
            Title Font
          </Typography>
          <FormControl fullWidth variant="outlined">
            <Select
              value={titleFont}
              onChange={(e) => setTitleFont(e.target.value)}
            >
              <MenuItem value="roboto">Roboto</MenuItem>
              <MenuItem value="arial">Arial</MenuItem>
              <MenuItem value="montserrat">Montserrat</MenuItem>
            </Select>
          </FormControl>
        </Box>
        <Box sx={{ minWidth: 120, marginBottom: 3 }}>
          <Typography sx={{ marginBottom: 1, fontWeight: '500' }}>
            Body Font
          </Typography>
          <FormControl fullWidth variant="outlined">
            <Select
              value={bodyFont}
              onChange={(e) => setBodyFont(e.target.value)}
            >
              <MenuItem value="roboto">Roboto</MenuItem>
              <MenuItem value="arial">Arial</MenuItem>
              <MenuItem value="montserrat">Montserrat</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Divider sx={{ marginY: 2 }} />

        {/* Button style */}
        <Typography
          sx={{ marginBottom: 1, fontSize: '18px', fontWeight: 'bold' }}
        >
          Button Style
        </Typography>

        <Box
          className="d-flex justify-content-space-between"
          sx={{
            marginBottom: 2
          }}
        >
          <Box sx={{ padding: '5px' }}>
            <Button
              variant={buttonStyle === 'standard' ? 'contained' : 'outlined'}
              sx={{
                padding: '8px 24px',
                backgroundColor:
                  buttonStyle === 'standard' ? '#008996' : 'transparent',
                color: buttonStyle === 'standard' ? '#ffffff' : '#2B2B2B',
                borderColor: buttonStyle === 'standard' ? '#008996' : '#ccc',
                borderRadius: '8px',
                transition:
                  'background-color 0.3s, color 0.3s, border-color 0.3s',
                '&:hover': {
                  backgroundColor:
                    buttonStyle === 'standard' ? '#006b73' : 'transparent',
                  borderColor: '#006b73'
                }
              }}
              onClick={() => setButtonStyle('standard')}
            >
              Standard
            </Button>
          </Box>

          <Box sx={{ padding: '5px' }}>
            <Button
              variant={buttonStyle === 'rounded' ? 'contained' : 'outlined'}
              sx={{
                padding: '8px 24px',
                backgroundColor:
                  buttonStyle === 'rounded' ? '#008996' : 'transparent',
                color: buttonStyle === 'rounded' ? '#ffffff' : '#2B2B2B',
                borderColor: buttonStyle === 'rounded' ? '#008996' : '#ccc',
                borderRadius: '30px',
                transition:
                  'background-color 0.3s, color 0.3s, border-color 0.3s',
                '&:hover': {
                  backgroundColor:
                    buttonStyle === 'rounded' ? '#006b73' : 'transparent',
                  borderColor: '#006b73'
                }
              }}
              onClick={() => setButtonStyle('rounded')}
            >
              Rounded
            </Button>
          </Box>
        </Box>

        {!showPreview && (
          <Box sx={{ background: '#0483BA', padding: '30px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
              <Button
                variant="contained"
                onClick={() => setShowPreview(true)}
                sx={{
                  width: '220px',
                  height: '50px',
                  mt: 1,
                  bgcolor: '#ffffff',
                  color: '#2b2b2b'
                }}
              >
                PREVIEW
              </Button>
              <Button
                variant="contained"
                onClick={applyTheme}
                sx={{
                  width: '220px',
                  height: '50px',
                  mt: 1,
                  bgcolor: '#ffffff',
                  color: '#2b2b2b'
                }}
              >
                APPLY STYLE
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
};
export default ThemeBuilder;
