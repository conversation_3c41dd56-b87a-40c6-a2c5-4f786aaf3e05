// Global Imports
import { Box } from '@mui/material';

// Local Imports
import '../../css/layout/shell.scss';

const Shell = ({
  showSubMenu = true,
  subMenu,
  children,
  showDrawer = false,
  drawerData,
}: any) => {
  return (
    <Box className="shell-container">
      {showSubMenu && subMenu}
      {!showDrawer && (
        <Box
          className={`shell-content ${showSubMenu ? 'shell-content-submenu' : ''}`}
        >
          {children}
        </Box>
      )}
      {showDrawer && (
        <Box className="shell-drawer-container">
          {drawerData}
          <Box className="shell-drawer-content">{children}</Box>
        </Box>
      )}
    </Box>
  );
};
export default Shell;
