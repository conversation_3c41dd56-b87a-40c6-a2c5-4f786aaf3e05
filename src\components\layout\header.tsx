// Global Imports
import {
  <PERSON>,
  <PERSON>,
  App<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  Di<PERSON>r,
  <PERSON><PERSON><PERSON>,
  ListItem,
  IconButton,
  Typography,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Popover,
  Modal,
  Button
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import LogoutIcon from '@mui/icons-material/Logout';
import GroupWorkIcon from '@mui/icons-material/GroupWork';
import DeleteIcon from '@mui/icons-material/Delete';
import { toast } from 'react-toastify';

// Local Imports
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  AppsIcon,
  MenuIcon,
  MoreVertIcon,
  GroupWorkOutlinedIcon,
  SupervisedUserCircleIcon
} from '../icons';
import logo from '../../assets/logo.svg';
import { Menu, USERTYPE } from '../../types';
import { RootState } from '../../redux/reducers';
import '../../css/index.scss';
import { AppDispatch } from '../../redux/app.store';
import { deleteorganization } from '../../redux/reducers/org.reducer';

const HeaderMenu: React.FC<{
  isMobile?: boolean;
  userType: USERTYPE['userType'];
  activeItem?: string;
  handleClickActive?: (item: string) => void;
}> = ({ isMobile = false, userType, activeItem = '', handleClickActive }) => {
  const [menu, setMenu] = useState<any>([]);
  const navigate = useNavigate();

  const menuItems = {
    organization: [
      // {
      //   name: 'Dashboard',
      //   to: '/dashboard',
      //   icon: (
      //     <Icon
      //       name="Dashboard"
      //       style={{
      //         color: activeItem.includes('/dashboard') ? '#08366B' : '#0483ba',
      //         fontSize: '24px'
      //       }}
      //     />
      //   )
      // },
      {
        name: 'Apps',
        to: '/apps',
        icon: (
          <AppsIcon
            style={{
              color: activeItem.includes('/apps') ? '#08366B' : '#0483ba',
              fontSize: '24px'
            }}
          />
        )
      },
      {
        name: 'Form Builder',
        to: '/form-builder',
        icon: (
          <GroupWorkIcon
            style={{
              color: activeItem.includes('/form-builder')
                ? '#08366B'
                : '#0483ba',
              fontSize: '24px'
            }}
          />
        )
      },
      {
        name: 'Users',
        to: '/users',
        icon: (
          <SupervisedUserCircleIcon
            style={{
              color: activeItem.includes('/users') ? '#08366B' : '#0483ba',
              fontSize: '24px'
            }}
          />
        )
      }
    ],

    superAdmin: [
      // {
      //   name: 'Dashboard',
      //   to: '/dashboard',
      //   icon: (
      //     <Icon
      //       name="Dashboard"
      //       style={{
      //         color: activeItem.includes('/dashboard') ? '#08366B' : '#0483ba',
      //         fontSize: '24px'
      //       }}
      //     />
      //   )
      // },
      {
        name: 'Apps',
        to: '/apps',
        icon: (
          <AppsIcon
            style={{
              color: activeItem.includes('/apps') ? '#08366B' : '#0483ba',
              fontSize: '24px'
            }}
          />
        )
      },
      {
        name: 'Clients',
        to: '/org-setup',
        icon: (
          <GroupWorkOutlinedIcon
            style={{
              color: activeItem.includes('/org-setup') ? '#08366B' : '#0483ba',
              fontSize: '24px'
            }}
          />
        )
      },
      {
        name: 'Users',
        to: '/users',
        icon: (
          <SupervisedUserCircleIcon
            style={{
              color: activeItem.includes('/users') ? '#08366B' : '#0483ba',
              fontSize: '24px'
            }}
          />
        )
      }
    ]
  };

  useEffect(() => {
    if (userType === 'organization') {
      setMenu(menuItems.organization);
    } else if (userType === 'super_admin') {
      setMenu(menuItems.superAdmin);
    }
  }, [userType]);

  return (
    <List
      className="header-icons"
      sx={{
        display: !isMobile ? 'flex' : 'block',
        padding: 0,
        gap: '20px'
      }}
    >
      {menu.map((item: any) => (
        <ListItem
          key={item.name}
          disablePadding
          sx={{
            width: 'auto',
            position: 'relative'
          }}
        >
          <ListItemButton
            sx={{
              gap: '12px',
              background:
                activeItem === item.to
                  ? 'radial-gradient(circle at bottom, rgba(72, 191, 227, 0.3), transparent 80%)' // Soft glow inside box
                  : 'transparent',
              borderRadius: '5px',
              padding: '10px 18px',
              margin: '6px 0',
              transition:
                'background 0.3s ease-in-out, opacity 0.3s, filter 0.3s ease-in-out',
              position: 'relative',
              opacity: activeItem === item.to ? 1 : 0.85, // Slight dim for non-selected
              filter:
                activeItem === item.to
                  ? 'brightness(1.05)'
                  : 'brightness(0.95)', // Slight brightness difference
              '&:hover': {
                background:
                  'radial-gradient(circle at bottom, rgba(72, 191, 227, 0.4), transparent 80%)', // Slightly stronger glow
                opacity: 1, // Restore full opacity on hover
                filter: 'brightness(1)' // Restore brightness on hover
              }
            }}
            onClick={(e) => {
              e.preventDefault();
              if (handleClickActive) {
                handleClickActive(item.to);
              }
              window.history.pushState({}, '', item.to);
              navigate(item.to);
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 'auto',
                color: activeItem === item.to ? '#0483BA' : '#6B7280',
                transition: 'color 0.3s ease-in-out, opacity 0.3s ease-in-out',
                opacity: activeItem === item.to ? 1 : 0.85 // Slightly dim icon if not selected
              }}
            >
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.name}
              sx={{
                '& .MuiTypography-root': {
                  color: activeItem === item.to ? '#08366B' : '#242424',
                  fontWeight: activeItem === item.to ? '600' : '400',
                  fontSize: '16px',
                  transition:
                    'color 0.2s ease-in-out, opacity 0.3s ease-in-out',
                  opacity: activeItem === item.to ? 1 : 0.85 // Slightly dim text if not selected
                }
              }}
            />
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );
};
const Header: React.FC = () => {
  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 950,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4
  };

  const navigate = useNavigate();
  const location = useLocation();
  const [activeItem, setActiveItem] = useState<string>('');
  const [openDeActivateModel, setOpenDeActivateModel] = useState(false);
  const { pathname } = location;
  const dispatch = useDispatch<AppDispatch>();

  const { userType, user } = useSelector((state: RootState) => state.auth);

  const topHeaderMenu = React.useRef<Menu[]>([
    {
      name: 'Configurations',
      to: 'configurations/address-configuration',
      icon: <AppsIcon />
    },
    {
      name: 'Logout',
      to: 'org-setup',
      icon: <LogoutIcon />
    },
    {
      name: 'De Activate',
      to: '',
      icon: <DeleteIcon />
    }
  ]);

  const topHeaderMenuSuperAdmin = React.useRef<Menu[]>([
    {
      name: 'Logout',
      to: 'org-setup',
      icon: <LogoutIcon />
    }
  ]);

  const [mobileOpen, setMobileOpen] = React.useState(false);
  const drawerWidth = (window.innerWidth / 100) * 60;
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null
  );

  useEffect(() => {
    setActiveItem(location.pathname);
  }, [location.pathname]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  const handleDrawerToggle = () => {
    setMobileOpen((prevState) => !prevState);
  };

  const handleClickActive = (element: any) => {
    setActiveItem(element);
  };

  const handleLogOut = () => {
    localStorage.clear();
    if (userType === 'organization') {
      localStorage.setItem('login_route', '/organization/login');
      navigate('/organization/login');
    } else {
      localStorage.setItem('login_route', '/login');
      navigate('/login');
    }
  };

  const handleSubmit = async () => {
    try {
      const orgUser: any = user;
      const response = await dispatch(
        deleteorganization(orgUser?.organization_id)
      );

      if (response?.payload?.error || response?.payload?.statusCode) {
        toast.error(response.payload?.error);
      } else {
        handleLogOut();
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const MobileMenu = (
    <Box onClick={handleDrawerToggle}>
      <Box sx={{ textAlign: 'center' }}>
        <Box component="img" src={logo} />

        <Typography variant="h5">Kleza FAB</Typography>
      </Box>
      <Divider />
      <HeaderMenu userType={userType} />
    </Box>
  );

  return (
    <>
      <AppBar
        component="nav"
        position="static"
        sx={{
          boxShadow: '0px 2px 10px 0px rgba(0,0,0,0.14)',
          zIndex: 1,
          backgroundColor: '#FAF9F8',
          height: '82px',
          display: 'flex',
          alignItems: 'center',
          width: '100%'
        }}
      >
        <Toolbar
          className="header-container"
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            height: '100%',
            padding: '0px 100px'
          }}
        >
          <Box className="flex" sx={{ marginRight: { md: 10 } }}>
            <IconButton
              size="large"
              edge="start"
              color="inherit"
              onClick={handleDrawerToggle}
              sx={{ display: { xs: 'block', md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>

            <Box
              component="img"
              src={userType === 'super_admin' ? logo : user?.logo}
              alt="logo"
              height="80px"
              width="80px"
              onClick={() => navigate('/apps')}
              className="cursor-pointer"
              sx={{
                borderRadius: '50%',
                backgroundColor: 'transparent',
                objectFit: 'contain',
                cursor: 'pointer'
              }}
            />

            <Typography
              // variant="h5"
              onClick={() => navigate('/apps')}
              className="cursor-pointer"
              sx={{
                background:
                  userType === 'super_admin'
                    ? 'linear-gradient(90deg, #87D68D, #6CD3DA, #37B5F3) !important'
                    : '#595959 !important',
                WebkitBackgroundClip: 'text !important',
                WebkitTextFillColor: 'transparent !important',
                fontSize: '22px !important',
                fontWeight: 600,
                lineHeight: 1,
                cursor: 'pointer',
                transition: 'opacity 0.3s ease-in-out',
                '&:hover': {
                  opacity: 0.8
                }
              }}
            >
              {userType === 'super_admin' ? 'Kleza FAB' : user?.name}
            </Typography>
          </Box>
          <Box
            sx={{
              flexGrow: 1,
              display: { xs: 'none', md: 'block' }
            }}
          >
            <HeaderMenu
              userType={userType}
              handleClickActive={handleClickActive}
              activeItem={activeItem}
            />
          </Box>
          <Box
            className="user-info-container"
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              padding: '6px 12px',
              borderRadius: '8px',
              backgroundColor: '#F8F9FA',
              transition: 'background 0.3s ease-in-out'
            }}
          >
            {/* User Avatar */}
            {/* <Avatar
              sx={{
                width: 38,
                height: 38,
                boxShadow: '0px 4px 6px rgba(0,0,0,0.1)',
                border: '2px solid #FFF'
              }}
              alt={user?.name || 'User Avatar'}
              src={user?.avatar || ''}
            /> */}

            {/* More Options Button */}
            <IconButton
              sx={{
                color: '#6B7280',
                transition: 'color 0.2s ease-in-out',
                '&:hover': { color: '#333' }
              }}
              aria-describedby={id}
              onClick={handleClick}
            >
              <MoreVertIcon />
            </IconButton>

            {/* Popover Menu */}
            <Popover
              id={id}
              open={open}
              anchorEl={anchorEl}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right'
              }}
            >
              {/* <TopHeaderMenu /> */}
              <List
                className="user-profile-list"
                sx={{
                  display: 'block',
                  padding: '10px',
                  gap: '20px',
                  minWidth: '300px',
                  backgroundColor: '#FAF9F8',
                  borderRadius: '8px',
                  boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)'
                }}
              >
                {(userType === 'super_admin'
                  ? topHeaderMenuSuperAdmin.current
                  : topHeaderMenu.current
                ).map((item: Menu) => (
                  <ListItem
                    key={item.name}
                    disablePadding
                    sx={{
                      width: 'auto',
                      gap: '20px',
                      '&::before': {
                        content: pathname
                          ?.toLowerCase()
                          .includes(item.to?.toLowerCase())
                          ? '""'
                          : undefined,
                        position: 'absolute',
                        width: '100%',
                        borderBottom: '2px solid',
                        borderRadius: '4px',
                        bottom: 0
                      }
                    }}
                  >
                    <ListItemButton
                      sx={{
                        gap: '20px',
                        borderRadius: '5px',
                        padding: '12px 18px',
                        transition:
                          'background 0.3s ease-in-out, opacity 0.3s ease-in-out',
                        '&:hover': {
                          background:
                            'radial-gradient(circle at bottom, rgba(72, 191, 227, 0.4), transparent 80%)',
                          opacity: 1,
                          filter: 'brightness(1)'
                        }
                      }}
                      onClick={(e) => {
                        switch (item.name) {
                          case 'Logout':
                            handleLogOut();
                            break;
                          case 'De Activate':
                            setOpenDeActivateModel(true);
                            break;
                          default:
                            navigate(item.to);
                            break;
                        }
                        e.preventDefault();
                        handleClickActive(item.to);
                        setAnchorEl(null);
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 'auto',
                          color: activeItem === item.to ? '#0483BA' : '#6B7280',
                          transition:
                            'color 0.3s ease-in-out, opacity 0.3s ease-in-out',
                          opacity: activeItem === item.to ? 1 : 0.85
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.name}
                        sx={{
                          '& .MuiTypography-root': {
                            color:
                              activeItem === item.to ? '#08366B' : '#242424',
                            fontWeight: activeItem === item.to ? '600' : '400',
                            fontSize: '16px',
                            transition:
                              'color 0.2s ease-in-out, opacity 0.3s ease-in-out',
                            opacity: activeItem === item.to ? 1 : 0.85
                          }
                        }}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}

                <Divider sx={{ margin: '10px 0' }} />

                <Box
                  className="user-profile"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    padding: '10px 15px',
                    borderRadius: '8px',
                    backgroundColor: '#F8F9FA !important',
                    transition: 'background 0.3s ease-in-out',
                    '&:hover': {
                      backgroundColor: '#EAECEF'
                    }
                  }}
                >
                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      boxShadow: '0px 4px 6px rgba(0,0,0,0.1)',
                      border: '2px solid #FFF',
                      backgroundColor: '#0483BA'
                    }}
                  >
                    <GroupWorkIcon sx={{ color: '#FFF' }} />
                  </Avatar>

                  <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <Typography
                      sx={{
                        fontSize: '16px',
                        fontWeight: 600,
                        color: '#2B2B2B'
                      }}
                    >
                      {user?.name}
                    </Typography>
                    <Typography sx={{ fontSize: '14px', color: '#6B7280' }}>
                      {user?.username ? user?.username : user?.email}
                    </Typography>
                    {user?.mobile_number && (
                      <Typography sx={{ fontSize: '14px', color: '#6B7280' }}>
                        {user?.mobile_number}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </List>
            </Popover>
          </Box>
        </Toolbar>
      </AppBar>
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth
          }
        }}
      >
        {MobileMenu}
      </Drawer>

      <Modal
        open={openDeActivateModel}
        onClose={() => setOpenDeActivateModel(false)}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Box style={{ backgroundColor: '#f6f6f6', padding: '20px' }}>
            <Typography id="modal-modal-title" variant="h6" component="h2">
              De Activate Organization
            </Typography>
            <Typography id="modal-modal-description" sx={{ mt: 2 }}>
              Are you sure you want to De Activate Organization?, You will not
              be able to access the organization after deactivation.
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', mt: 2, justifyContent: 'end' }}>
            <Button
              variant="contained"
              onClick={() => setOpenDeActivateModel(false)}
              sx={{
                mr: 1,
                backgroundColor: 'white2.main',
                color: '#242424'
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: 'white !important',
                color: 'red !important'
              }}
              onClick={() => {
                handleSubmit();
              }}
            >
              Delete
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default Header;
