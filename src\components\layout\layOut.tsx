/* eslint-disable react-hooks/exhaustive-deps */

// Global Imports
import React, { useEffect } from 'react';
import { Box } from '@mui/material';
import { Outlet, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

// Local Imports
import {
  getOrganizationAdmin,
  getSuperAdminDetails,
  logout
} from '../../redux/reducers/auth.reducer';
import { RootState } from '../../redux/reducers';
import Header from './header';
import { AppDispatch } from '../../redux/app.store';
import LoaderUI from '../reusable/loaderUI';

const Layout: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useSelector(
    (state: RootState) => state.auth
  );
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const getAuthenticationStatus = async () => {
    if (isAuthenticated && !user) {
      if (localStorage.getItem('login_route') === '/login') {
        const res = await dispatch(getSuperAdminDetails(null));
        if (res.payload.statusCode === 403) {
          dispatch(logout());
        }
      } else if (localStorage.getItem('org_id')) {
        const res = await dispatch(
          getOrganizationAdmin(localStorage.getItem('org_id'))
        );
        if (res.payload.statusCode === 403) {
          dispatch(logout());
        }
      }
    } else if (!isAuthenticated) {
      navigate(
        localStorage.getItem('login_route') === '/login'
          ? '/login'
          : '/organization/login'
      );
    }
  };

  useEffect(() => {
    getAuthenticationStatus();
  }, []);

  return (
    <Box sx={{ overflow: 'hidden', width: '100%', height: '100%' }}>
      {isLoading ? (
        <LoaderUI />
      ) : (
        isAuthenticated && (
          <Box
            sx={{
              overflow: 'hidden',
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: '#F0F0F0'
            }}
          >
            <Header />
            <Box
              sx={{
                display: 'flex',
                flexGrow: 1,
                width: '100%',
                overflow: 'hidden'
              }}
            >
              <Box sx={{ width: '100%', height: '100%', overflow: 'auto' }}>
                <Outlet />
              </Box>
            </Box>
          </Box>
        )
      )}
    </Box>
  );
};

export default Layout;
