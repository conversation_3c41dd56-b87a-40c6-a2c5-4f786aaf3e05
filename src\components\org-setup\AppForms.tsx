/* eslint-disable react-hooks/exhaustive-deps */

// Global Imports
import { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Tooltip,
  IconButton,
  CardMedia
} from '@mui/material';
import {
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  AssignmentOutlined as AssignmentOutlinedIcon
} from '@mui/icons-material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { useSearchParams, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import Shell from '../layout/Shell';
import '../../css/org-setup.scss/appForms.scss';
import { RootState } from '../../redux/reducers';
import { AppDispatch } from '../../redux/app.store';
import { getappforms } from '../../redux/reducers/apps.reducer';
import { SubMenu } from '../form.elements';
import { appformconfiguration } from '../../redux/reducers/org.reducer';
import LoaderUI from '../reusable/loaderUI';

const AppForms: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: RootState) => state.org);
  const [queryParam] = useSearchParams();

  // Optional Snackbar State (commented out)
  // const [openSnackbar, setOpenSnackbar] = useState(false);
  // const [snackbarMessage, setSnackbarMessage] = useState('');
  // const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');

  const [getAppForms, setGetAppForms] = useState([]);
  const { id } = useParams<{ id: string }>();
  const organizationId: string = queryParam.get('orgId') || '';

  const getAppFormsDetails = async () => {
    try {
      const response = await dispatch(getappforms({ id, organizationId }));
      if (response.payload.status) {
        setGetAppForms(response.payload.data);
        // Optionally toast success or show snackbar here
        // toast.success('App forms fetched successfully');
      } else if (response.payload.error) {
        const error =
          response.payload.message.length > 0
            ? response.payload.message[0]
            : response.payload.message;
        toast.error(error);
      } else if (response.payload.statusCode === 400) {
        toast.error(response?.payload?.message);
      }
    } catch (error: any) {
      toast.error(error?.message);
    }
  };

  useEffect(() => {
    getAppFormsDetails();
  }, [id, organizationId]);

  const handleCardClick = async (formId: string, org_id: any, type: string) => {
    const updatedFormsList: any = getAppForms?.map((form: any) =>
      form.form_id === formId
        ? { ...form, formExisted: !form.formExisted }
        : form
    );
    setGetAppForms(updatedFormsList);

    const data = { form_id: formId };

    const response = await dispatch(
      appformconfiguration({ orgId: org_id, data, type })
    );
    if (response.payload.status) {
      // Optionally toast success or set snackbar state here
      // toast.success(type === 'add' ? 'Form added successfully!' : 'Form removed successfully!');
    } else if (response.payload.error) {
      const error =
        response.payload.message.length > 0
          ? response.payload.message[0]
          : response.payload.message;
      toast.error(error);
    } else if (response.payload.statusCode === 400) {
      toast.error(response?.payload?.message);
    }
  };

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="appFrms-container">
          <Box className="appFrms-content">
            <Box className="appFrms-main">
              <Box>
                {isLoading && <LoaderUI />}
                {!isLoading && (
                  <Box className="appFrms-titleSection">
                    {getAppForms &&
                    getAppForms?.filter((form: any) => form?.status).length >
                      0 ? (
                      <Typography variant="h3" className="appFrms-heading">
                        Select Form Status
                      </Typography>
                    ) : (
                      <Box className="appFrms-emptyMessage">
                        <Typography
                          variant="h6"
                          color="text.primary"
                          fontWeight="bold"
                        >
                          This App do not have any forms!
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          mt={1}
                        >
                          You can start creating forms for the app.
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}
                <Box className="appFrms-cardContainer">
                  {isLoading && <LoaderUI />}
                  {!isLoading && (
                    <Grid
                      container
                      rowSpacing={3}
                      columnSpacing={4}
                      sx={{ padding: '0px 80px' }}
                    >
                      {getAppForms
                        ?.filter(
                          (form: any) => form?.status || form?.formExisted
                        )
                        ?.map((card: any, index: number) => {
                          const key = `key-card-${index}-${index * 3}`;
                          return (
                            <Grid item xs={12} sm={12} md={6} lg={4} key={key}>
                              <Card
                                onClick={() => {
                                  if (!card?.status && card?.formExisted) {
                                    toast.error(
                                      'Form has been Unpublished, Please publish the form to add it to the app'
                                    );
                                  } else {
                                    handleCardClick(
                                      card?.form_id,
                                      organizationId,
                                      card?.formExisted ? 'remove' : 'add'
                                    );
                                  }
                                }}
                                sx={{
                                  borderRadius: 1,
                                  boxShadow: 1,
                                  p: 0,
                                  width: '100%',
                                  height: 80,
                                  cursor: 'pointer',
                                  backgroundColor: 'background.paper',
                                  border: card?.formExisted
                                    ? '1px solid #36C0ED'
                                    : '1px solid transparent',
                                  transition: 'all 0.3s ease-in-out',
                                  '&:hover': {
                                    backgroundColor: 'background.default',
                                    border: '1px solid',
                                    borderColor: 'primary.main',
                                    transform: 'scale(1.05)',
                                    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
                                  }
                                }}
                              >
                                <CardContent
                                  sx={{
                                    display: 'grid',
                                    gridTemplateColumns: '1fr 10fr 1fr',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    py: 2 // vertical padding
                                  }}
                                >
                                  {/* Left Portion: Icon and Text */}
                                  {card?.icon ? (
                                    <CardMedia
                                      component="img"
                                      image={card.icon}
                                      alt={card?.name}
                                      sx={{
                                        width: '40px',
                                        height: '40px',
                                        backgroundColor: '#F9F9F9'
                                      }}
                                    />
                                  ) : (
                                    <AssignmentOutlinedIcon
                                      sx={{
                                        color: 'text.secondary',
                                        fontSize: 30
                                      }}
                                    />
                                  )}
                                  <Tooltip
                                    title={
                                      !card?.status && card?.formExisted
                                        ? 'Form has been Unpublished'
                                        : card?.name
                                    }
                                    arrow
                                  >
                                    <Typography
                                      sx={{
                                        fontSize: '18px',
                                        fontWeight: 600,
                                        color: 'text.secondary',
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        // width: 200
                                        paddingLeft: '8px'
                                      }}
                                    >
                                      {card?.name}
                                    </Typography>
                                  </Tooltip>

                                  {/* Right Portion: Check Icon */}
                                  <IconButton
                                    sx={{
                                      color: card.status
                                        ? 'primary.main'
                                        : 'grey.400',
                                      p: 1 // increased click area
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (card?.formExisted !== undefined) {
                                        handleCardClick(
                                          card?.form_id,
                                          organizationId,
                                          card?.formExisted ? 'remove' : 'add'
                                        );
                                      }
                                    }}
                                  >
                                    {card?.formExisted ? (
                                      <CheckCircleOutlineIcon
                                        sx={{ fontSize: '2rem' }}
                                      />
                                    ) : (
                                      <RadioButtonUncheckedIcon
                                        sx={{ fontSize: '2rem' }}
                                      />
                                    )}
                                  </IconButton>
                                </CardContent>
                              </Card>
                            </Grid>
                          );
                        })}
                    </Grid>
                  )}
                </Box>
              </Box>
            </Box>
          </Box>
          {/*
          <SnackbarElement
            message={snackbarMessage}
            statusType={snackbarSeverity}
            snackbarOpen={openSnackbar}
            setSnackbarOpen={() => setOpenSnackbar(false)}
          />
          */}
        </Box>
      )}
    </Shell>
  );
};

export default AppForms;
