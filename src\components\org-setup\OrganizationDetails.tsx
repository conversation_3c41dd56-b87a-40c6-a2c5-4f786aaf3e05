// Global Imports
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { Box, Typography, Grid, Tooltip, Stack, Button } from '@mui/material';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
// Local Imports
import Shell from '../layout/Shell';
import '../../css/org-set-up-styles.scss';
import { RootState } from '../../redux/reducers';
import { OrganizationsListProps } from '../../types';
import '../../css/org-setup.scss/organizationsDetails.scss';
import { Icon, SubMenu } from '../form.elements';
import LoaderUI from '../reusable/loaderUI';

const OrganizationsList: React.FC<OrganizationsListProps> = ({
  orgData
}: any) => {
  const { id } = useParams();
  const { isLoading } = useSelector((state: RootState) => state.org);
  const navigate = useNavigate();

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ padding: '20px 100px' }}>
          <Box>
            <Box className="org-dtls-mainStyles">
              <Box className="org-dtls-backgroundBox">
                <Box className="org-dtls-devStyles">
                  <Box>
                    <Box className="org-dtls-innernavStyles">
                      <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                        spacing={2}
                      >
                        <Box>
                          <Tooltip
                            title="Organization Name"
                            arrow
                            placement="top"
                          >
                            <Typography
                              variant="h5"
                              className="org-dtls-captalizedStyles org-dtls-typographyTitle"
                            >
                              {orgData?.name}
                            </Typography>
                          </Tooltip>
                        </Box>

                        {/* Edit Button using MUI Button component */}
                        <Box>
                          <Tooltip title="Edit Organization Details" arrow>
                            <Button
                              onClick={() =>
                                navigate(
                                  `/org-setup/organization-registration/${orgData?.organization_id}`
                                )
                              }
                              disableRipple
                              sx={{
                                minWidth: 'auto',
                                p: 1,
                                color: 'text.secondary', // Default gray color
                                '&:hover': {
                                  backgroundColor: 'transparent',
                                  color: 'primary.main' // Blue color on hover
                                }
                              }}
                            >
                              <EditOutlinedIcon />
                            </Button>
                          </Tooltip>
                        </Box>
                      </Stack>
                    </Box>

                    <Box className="org-dtls-paddingTop30px">
                      <Box className="org-dtls-flexRow">
                        <Box className="org-dtls-flexColumn">
                          <Box className="org-dtls-flexRowGap20px">
                            <Tooltip title="Email Icon" arrow placement="top">
                              <Icon
                                name="MailOutline"
                                sx={{ color: '#dadada', margin: '0px' }}
                              />
                            </Tooltip>
                            <Tooltip
                              title="Organization Email"
                              arrow
                              placement="top"
                            >
                              <Typography
                                variant="inherit"
                                className="org-dtls-typographyDetails"
                              >
                                {orgData?.email}
                              </Typography>
                            </Tooltip>
                          </Box>
                          <Box className="org-dtls-flexRowGap20px">
                            <Tooltip title="Phone Icon" arrow placement="top">
                              <Icon
                                name="PhoneInTalk"
                                sx={{ color: '#dadada', margin: '0px' }}
                              />
                            </Tooltip>
                            <Tooltip
                              title="Contact Number"
                              arrow
                              placement="top"
                            >
                              <Typography
                                variant="inherit"
                                noWrap
                                className="org-dtls-typographyDetails"
                              >
                                {orgData?.mobile_number}
                              </Typography>
                            </Tooltip>
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </Box>

                {/* Apps Section */}
                {orgData?.apps && (
                  <Box sx={{ padding: '0px 20px' }}>
                    <Box className="org-dtls-appsBox">
                      <Typography className="org-dtls-typographyApps">
                        Apps
                      </Typography>
                      {isLoading && <LoaderUI />}
                      {!isLoading && (
                        <Grid
                          container
                          spacing={2.4}
                          style={{
                            display: 'flex',
                            justifyContent: 'flex-start',
                            margin: '0 auto',
                            maxWidth: 'calc(100% - 40px)'
                          }}
                        >
                          {orgData?.apps.map((app: any, index: number) => (
                            <Grid item key={app?.app_id || index} lg={2.4}>
                              <Box
                                sx={{
                                  width: '160px',
                                  height: '160px',
                                  borderRadius: '16px',
                                  background: '#fff',
                                  boxShadow: '0px 4px 8px rgba(0,0,0,0.15)',
                                  transition: '0.3s ease-in-out',
                                  cursor: 'pointer',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  overflow: 'hidden',
                                  '&:hover': {
                                    boxShadow: '0px 6px 12px rgba(0,0,0,0.2)',
                                    transform: 'translateY(-3px)'
                                  }
                                }}
                                onClick={() =>
                                  navigate(
                                    `/org-setup/organization-profile/app-forms/${app?.app_id}?orgId=${id}`
                                  )
                                }
                              >
                                {/* App Logo/Icon Section */}
                                <Box
                                  sx={{
                                    width: '100%',
                                    height: '120px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: '#e9feff'
                                  }}
                                >
                                  {app?.orgAppConfiguration?.logo ? (
                                    <Box
                                      component="img"
                                      src={app?.orgAppConfiguration?.logo}
                                      alt={
                                        app?.orgAppConfiguration?.app_name ||
                                        app.name
                                      }
                                      sx={{
                                        width: '80px',
                                        height: '80px',
                                        objectFit: 'contain',
                                        borderRadius: '50%',
                                        backgroundColor: '#ffffff'
                                      }}
                                    />
                                  ) : (
                                    <Box
                                      sx={{
                                        width: '80px',
                                        height: '80px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        borderRadius: '50%',
                                        backgroundColor: '#ffffff'
                                      }}
                                    >
                                      <Icon
                                        name="Widgets"
                                        sx={{ color: '#595959' }}
                                        fontSize="large"
                                      />
                                    </Box>
                                  )}
                                </Box>
                                {/* App Name Section */}
                                <Box
                                  sx={{
                                    padding: '10px',
                                    width: '100%',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                  }}
                                >
                                  <Tooltip
                                    title={
                                      app?.orgAppConfiguration?.app_name ||
                                      app.name
                                    }
                                    arrow
                                    placement="bottom"
                                  >
                                    <Typography
                                      noWrap
                                      sx={{
                                        maxWidth: 180,
                                        fontWeight: 700,
                                        textAlign: 'center',
                                        textTransform: 'capitalize',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        color: '#595959'
                                      }}
                                    >
                                      {app?.orgAppConfiguration?.app_name ||
                                        app.name}
                                    </Typography>
                                  </Tooltip>
                                </Box>
                              </Box>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      )}
    </Shell>
  );
};

export default OrganizationsList;
