// Global Imports
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

// Local Imports
import { Tooltip } from '@mui/material';
import { useState } from 'react';
import { ORGDATA } from '../../types';
import Shell from '../layout/Shell';
import { SubMenu } from '../form.elements';
import { RootState } from '../../redux/reducers';
import '../../css/org-setup.scss/organizationsList.scss';
import LoaderUI from '../reusable/loaderUI';
import { AppsIcon } from '../icons';
import fallbackImage from '../../assets/users-avatar.png';

const OrganizationsList: React.FC<{ list: ORGDATA[] }> = ({ list }) => {
  const navigate = useNavigate();
  const { isLoading } = useSelector((state: RootState) => state.org);
  const [imgErrored, setImgErrored] = useState(false);

  const getSubMenu = () => {
    return (
      <SubMenu
        pageName=""
        buttonWithoutBg={{
          status: true,
          title: 'Client Registration',
          icon: 'AddCircleOutline',
          redirectUrl: '/org-setup/organization-registration'
        }}
        iconStyles={{
          color: '#fff'
        }}
      />
    );
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="org-lst-container">
          <Box className="org-lst-padding">
            <Box className="org-lst-inner-box">
              <Box>
                <Box className="org-lst-total">
                  <Typography className="org-lst-total-text">
                    Clients :
                  </Typography>
                  <Typography className="org-lst-count">
                    {list.length}
                  </Typography>
                </Box>
                <Box className="org-lst-header">
                  {/* Optional: Search bar and filters may be added here */}
                </Box>
                {isLoading && <LoaderUI />}
                {!isLoading && (
                  <Grid
                    container
                    spacing={2}
                    style={{
                      display: 'flex',
                      justifyContent: 'flex-start',
                      background: '#faf9f8',
                      borderTop: '0.125rem solid #0483ba',
                      padding: '1.875rem 2.5rem'
                    }}
                  >
                    {list.map((org) => (
                      <Grid
                        key={org.organization_id}
                        item
                        xs={12}
                        sm={6}
                        md={4}
                        lg={3}
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          padding: '10px'
                        }}
                      >
                        {/* Card Box */}
                        <Box
                          onClick={() => {
                            navigate(
                              `/org-setup/organization-profile/${org.organization_id}`
                            );
                          }}
                          style={{
                            width: '260px',
                            height: '130px',
                            display: 'flex',
                            alignItems: 'center',
                            borderRadius: '12px',
                            boxShadow: '0px 3px 6px rgba(0, 0, 0, 0.15)',
                            background: '#fff',
                            cursor: 'pointer',
                            border: '1px solid #EAEAEA',
                            transition: '0.2s ease-in-out',
                            overflow: 'hidden'
                          }}
                        >
                          {/* Left Section: Logo & Name */}
                          <Box
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              height: '100%',
                              flex: 1,
                              minWidth: '0px'
                            }}
                          >
                            {/* Organization Logo Container */}
                            <Box
                              sx={{
                                width: 80,
                                height: 80,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '50%',
                                border: '3px solid #e9feff',
                                p: 1,
                                mt: 1.5,
                                backgroundColor: '#fff',
                                maxWidth: '100%',
                                maxHeight: '100%',
                                overflow: 'hidden',
                                flexShrink: 1
                              }}
                            >
                              {!imgErrored ? (
                                <img
                                  src={org?.logo || fallbackImage}
                                  alt={org?.name || 'Fallback'}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: '50%',
                                    objectFit: 'contain'
                                  }}
                                  onError={() => setImgErrored(true)}
                                />
                              ) : (
                                <AppsIcon
                                  style={{
                                    width: 60,
                                    height: 60,
                                    color: '#757575'
                                  }}
                                />
                              )}
                            </Box>

                            {/* Organization Name with Ellipsis */}
                            <Tooltip title={org?.name} arrow placement="bottom">
                              <Typography
                                style={{
                                  fontWeight: 'bold',
                                  fontSize: '14px',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                  // maxWidth: '140px',
                                  // width: 'fit-content',
                                  width: '100%',
                                  marginBottom: '12px',
                                  textAlign: 'center',
                                  color: '#595959',
                                  padding: '0 8%',
                                  flexShrink: 1
                                }}
                              >
                                {org?.name}
                              </Typography>
                            </Tooltip>
                          </Box>

                          {/* Right Section: App Count with Background */}
                          <Box
                            style={{
                              width: '85px',
                              height: '100%',
                              backgroundColor: '#e9feff',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderLeft: '1px solid #EAEAEA'
                            }}
                          >
                            <Typography
                              style={{
                                fontWeight: 'bold',
                                fontSize: '20px',
                                color: '#595959'
                              }}
                            >
                              {org?.apps?.length || 0}
                            </Typography>
                            <Typography
                              style={{
                                fontSize: '12px',
                                color: '#595959'
                              }}
                            >
                              {(org?.apps?.length || 0) > 1 ? 'Apps' : 'App'}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            </Box>
          </Box>
          {/* Optional Snackbar component can be added here */}
        </Box>
      )}
    </Shell>
  );
};

export default OrganizationsList;
