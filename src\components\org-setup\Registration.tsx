// Global Imports
import {
  // Card,
  // CardMedia,
  Grid,
  // InputLabel,
  Typography,
  Box,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Dialog,
  useMediaQuery,
  useTheme,
  Card,
  CardMedia,
  InputLabel,
  CardContent,
  Tooltip,
  IconButton
  // colors
} from '@mui/material';
// import { Upload } from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  useEffect,
  useRef,
  // useRef,
  useState
} from 'react';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton';
import { toast } from 'react-toastify';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
// Local Imports
import '../../css/org-registration-styles.scss';
import { Delete, Upload } from '@mui/icons-material';
import { AppDispatch } from '../../redux/app.store';
import {
  createorganization,
  getapps,
  updateorganization
} from '../../redux/reducers/org.reducer';
import {
  AppForm,
  FormInput,
  FormPhoneInput,
  FormSelect,
  SubMenu,
  SubmitButton
} from '../form.elements';
import Shell from '../layout/Shell';
import { RootState } from '../../redux/reducers';
import { PasswordInput } from '../reusable/PasswordInput';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import { OrganizationRegistrationFormProps } from '../../types';
import '../../css/org-setup.scss/organizationRegistrationForm.scss';
import LoaderUI from '../reusable/loaderUI';

const OrganizationRegistrationForm: React.FC<
  OrganizationRegistrationFormProps
> = ({ getappsData, selectedAppsList, industryTypes, formData }) => {
  const param = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const { loadingSpinner, isLoading } = useSelector(
    (state: RootState) => state.org
  );
  const dispatch = useDispatch<AppDispatch>();
  const [selectedApps, setselectedApps]: any = useState([]);
  const [appsData, setAppsData]: any = useState([]);
  const [organizationDetails, setOrganizationDetails]: any = useState({});

  const [appsErrorMessage, setAppsErrorMessage] = useState('');
  const [selectedIndustryType, setSelectedIndustryType] = useState('');

  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [openModel, setOpenModal] = useState(false);

  // const [openSnackbar, setOpenSnackbar] = useState(false);
  const [isOrganizationAppsLoading, setIsOrganizationAppsLoading] =
    useState(false);
  // const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // const [snackbarSeverity, setSnackbarSeverity] = useState<
  //   'success' | 'error' | 'warning' | 'info'
  // >('success');

  // const addSnackbarMessage = (
  //   message: string,
  //   severity: 'success' | 'error' | 'warning' | 'info'
  // ) => {
  //   setSnackbarMessage(message);
  //   setSnackbarSeverity(severity);
  // };

  const inputLogo: any = useRef(null);
  const [logo, setLogo]: any = useState(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const validatePassword = () => {
    if (!param.id) {
      return Yup.string().required('Password is required');
    }
    return Yup.string().optional();
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .trim()
      .strict(true)
      .required('Organization name is required'),
    industry_type_id: Yup.string().required('Please select Industry Type'),
    email: Yup.string()
      .email('Invalid email format')
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        'Please enter a valid email address'
      )
      .required('Email is required'),
    password: validatePassword(),
    mobile_number: Yup.string().required('Mobile Number is required')
  });

  const handleInputChange = async (e: any) => {
    try {
      const { value } = e.target;
      setSelectedIndustryType(value);
      setIsOrganizationAppsLoading(true);
      const apps = await dispatch(getapps(value));
      if (apps.payload.status) {
        setAppsData(apps.payload.data);
        setIsOrganizationAppsLoading(false);
      }
    } catch (error) {
      setIsOrganizationAppsLoading(true);
    }
  };

  const uploadLogo = async () => {
    inputLogo.current.click();
  };

  const deleteLogo = async () => {
    setLogo('');
  };

  const handleLogoChange = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        setLogo(reader.result);
      };
    }
  };

  const handleSubmit = async (event: any) => {
    if (logo) {
      if (selectedApps.length > 0) {
        setAppsErrorMessage('');
        const data = {
          name: event.name,
          mobile_number: event.mobile_number,
          email: event.email,
          industry_type_id: event.industry_type_id,
          apps: selectedApps,
          logo,
          ...(event.password && { password: event.password })
        };
        setOrganizationDetails(data);

        try {
          const orgData = param.id
            ? await dispatch(
                updateorganization({
                  id: param.id,
                  data: {
                    ...data,
                    check_org_apps_assigned_to_user: true,
                    confirm_remove_user_apps: event?.confirm_remove_user_apps
                  }
                })
              )
            : await dispatch(createorganization(data));

          if (orgData.payload.status) {
            // toast.success(
            //   param.id
            //     ? 'Organization updated successfully!'
            //     : 'Organization created successfully!'
            // );
            setTimeout(() => {
              navigate(
                param.id
                  ? `/org-setup/organization-profile/${param.id}`
                  : '/org-setup'
              );
            }, 1000);
          } else if (
            param.id &&
            !orgData.payload.status &&
            orgData.payload?.users_existed_for_the_removed_apps
          ) {
            setOpenModal(true);
          } else {
            const errorMessage = orgData?.payload?.message;
            if (Array.isArray(errorMessage) && errorMessage.length > 0) {
              errorMessage.forEach((error: any) => {
                toast.error(error);
              });
            } else {
              toast.error(
                errorMessage || 'An error occurred. Please try again.'
              );
            }
          }
        } catch (error) {
          toast.error('An error occurred. Please try again.');
        }
      } else {
        toast.error('Please select at least one app.');
      }
    }
  };

  const handleModalClose = () => setOpenModal(false);

  const selectedApp = async (id: string) => {
    const isIdExist = selectedApps?.find((appId: any) => appId === id);
    const selectedAppData = appsData.find((app: any) => app.app_id === id);
    const processCode = selectedAppData?.industry_app_process?.process_code;

    if (!isIdExist) {
      // Trying to select an app
      if (processCode === 'HC_CARE') {
        // Check if client assessment app is already selected
        const clientAssessmentSelected = selectedApps.some((appId: any) => {
          const clientAssessmentApp = appsData.find(
            (app: any) => app.app_id === appId
          );

          return (
            clientAssessmentApp?.industry_app_process?.process_code ===
            'HC_CLIASS'
          );
        });

        if (!clientAssessmentSelected) {
          toast.error(
            'Please select the Client Assessment app before selecting the Caregiver app.'
          );
          return;
        }
      }
      setselectedApps([...selectedApps, id]);
    } else {
      // Trying to deselect an app
      if (processCode === 'HC_CLIASS') {
        // Check if caregiver app is selected
        const caregiverSelected = selectedApps.some((appId: any) => {
          const caregiverApp = appsData.find(
            (app: any) => app.app_id === appId
          );

          return caregiverApp?.industry_app_process?.process_code === 'HC_CARE';
        });

        if (caregiverSelected) {
          toast.error(
            'Please deselect the Caregiver app before deselecting the Client Assessment app.'
          );
          return;
        }
      }
      setselectedApps(selectedApps.filter((appId: any) => appId !== id));
    }
  };

  useEffect(() => {
    setselectedApps(selectedAppsList);
    setAppsData(getappsData);
    setLogo(formData?.logo);
  }, [selectedAppsList, getappsData, formData]);

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="org-registn-container">
          <Box className="org-registn-inner-box">
            <Typography className="org-registn-title" sx={{ color: '#595959' }}>
              Client Registration
            </Typography>
            <AppForm
              initialValues={formData}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              <Box className="org-registn-form-container">
                {isLoading && <LoaderUI />}
                {!isLoading && (
                  <Grid
                    container
                    columnSpacing={4}
                    className="org-registn-grid-container"
                  >
                    <Grid item xs={6}>
                      <Box>
                        <FormInput
                          name="name"
                          label=" Client / Organization Name"
                          required
                          autoComplete="off"
                          containerStyles={{
                            width: '100%'
                          }}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box>
                        <FormPhoneInput
                          name="mobile_number"
                          label=" Mobile Number"
                          required
                          containerStyles={{
                            width: '100%'
                          }}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box>
                        <FormInput
                          name="email"
                          label=" Email"
                          required
                          autoComplete="off"
                          type="email"
                          containerStyles={{
                            width: '100%',
                            marginTop: '4px'
                          }}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box>
                        <PasswordInput
                          name="password"
                          label="password"
                          containerStyles={{}}
                          required
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <div>
                        {/* <Box>
                          <FormInput
                            name="custom_name"
                            label="Custom Name"
                            autoComplete="off"
                            containerStyles={{
                              width: '100%'
                            }}
                          />
                        </Box> */}
                      </div>
                      <div>
                        <Box>
                          <FormSelect
                            name="industry_type_id"
                            label="Industry Type"
                            required
                            placeholder="Select Industry Type"
                            data={industryTypes}
                            containerStyles={{
                              width: '100%',
                              color: '#595959'
                            }}
                            onChange={handleInputChange}
                            value={
                              formData?.industry_type_id
                                ? formData?.industry_type_id
                                : selectedIndustryType
                            }
                          />
                        </Box>
                      </div>
                    </Grid>
                    <Grid item xs={6}>
                      <div className="org-registn-upload-logo-container">
                        <InputLabel className="org-registn-upload-logo-label">
                          Upload Logo
                        </InputLabel>
                        <Box className="org-registn-upload-logo-box">
                          <Box className="d-flex">
                            <Card
                              className="org-registn-upload-logo-card"
                              onClick={uploadLogo}
                            >
                              <Box className="org-registn-logo-box">
                                <CardMedia
                                  className="org-registn-logo-media"
                                  image={
                                    logo ||
                                    `https://ui-avatars.com/api/?name=${encodeURIComponent(
                                      'upload logo'
                                    )}&size=128&rounded=true`
                                  }
                                  title="Logo"
                                />
                                <input
                                  type="file"
                                  ref={inputLogo}
                                  style={{ display: 'none' }}
                                  onChange={handleLogoChange}
                                />
                              </Box>
                              <Upload className="org-registn-upload-icon" />
                            </Card>
                            <Box>
                              {logo && (
                                <Delete
                                  onClick={deleteLogo}
                                  sx={{
                                    color: 'red2.light',
                                    cursor: 'pointer'
                                  }}
                                />
                              )}
                            </Box>
                          </Box>
                        </Box>
                        {!logo && isSubmitted && (
                          <Typography
                            color="error"
                            fontSize="0.7rem"
                            marginTop="4px"
                          >
                            Logo is required
                          </Typography>
                        )}
                      </div>
                    </Grid>
                  </Grid>
                )}
                <div className="errorMessage">{appsErrorMessage}</div>
                {isOrganizationAppsLoading && <LoaderUI />}
                {appsData.length > 0 && (
                  <Box className="org-registn-apps-container">
                    <Typography
                      className="org-registn-apps-title"
                      sx={{ color: '#595959' }}
                    >
                      Apps
                    </Typography>
                    <Box className="org-registn-apps-box">
                      {isLoading && <LoaderUI />}
                      {!isLoading && (
                        <Grid container spacing={3}>
                          {appsData.map((app: any) => {
                            const isSelected = selectedApps.includes(
                              app.app_id
                            );
                            return (
                              <Grid
                                item
                                key={app.app_id}
                                xs={12}
                                sm={12}
                                md={6}
                                lg={4}
                              >
                                <Card
                                  onClick={() => selectedApp(app?.app_id)}
                                  sx={{
                                    padding: 0,
                                    width: '100%',
                                    height: '80px',
                                    cursor: 'pointer',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    position: 'relative',
                                    background: '#F9F9F9',
                                    border: isSelected
                                      ? '1px solid #36C0ED'
                                      : '1px solid transparent',
                                    transition: 'all 0.3s ease-in-out',
                                    '&:hover': {
                                      backgroundColor:
                                        theme.palette.background.default,
                                      border: `1px solid ${theme.palette.primary.main}`,
                                      transform: 'scale(1.05)',
                                      boxShadow:
                                        '0px 4px 20px rgba(0, 0, 0, 0.1)'
                                    }
                                  }}
                                >
                                  {/* Icon and Text Content */}
                                  <CardContent
                                    sx={{
                                      display: 'grid',
                                      gridTemplateColumns: '1fr 10fr 1fr',
                                      alignItems: 'center',
                                      justifyContent: 'flex-start',
                                      paddingTop: '20px',
                                      width: '100%'
                                    }}
                                  >
                                    {/* Left Icon */}
                                    <Box
                                      component="img"
                                      src={
                                        app?.logo
                                          ? app?.logo
                                          : `https://ui-avatars.com/api/?name=${encodeURIComponent(
                                              app?.name || 'App Name'
                                            )}&size=128&rounded=true`
                                      }
                                      alt={app?.name}
                                      sx={{
                                        width: '50px',
                                        height: '50px',
                                        borderRadius: '50%'
                                      }}
                                    />

                                    {/* App Name */}
                                    <Tooltip
                                      title={
                                        isSelected
                                          ? app?.orgAppConfiguration
                                              ?.app_name || app?.name
                                          : app?.name
                                      }
                                      arrow
                                    >
                                      <Typography
                                        sx={{
                                          fontSize: '16px',
                                          fontWeight: '500',
                                          color: '#595959',
                                          whiteSpace: 'nowrap',
                                          overflow: 'hidden',
                                          textOverflow: 'ellipsis',
                                          // width: '220px'
                                          paddingLeft: '8px'
                                        }}
                                      >
                                        {app?.orgAppConfiguration?.app_name ||
                                          app?.name}
                                      </Typography>
                                    </Tooltip>
                                  </CardContent>
                                  {/* Selection Checkmark */}
                                  <IconButton
                                    sx={{
                                      color: isSelected ? '#395e88' : '#E0E0E0',

                                      // Increase click area to match the bigger icon
                                      p: 1
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      selectedApp(app?.app_id);
                                    }}
                                  >
                                    {isSelected ? (
                                      <CheckCircleIcon
                                        sx={{
                                          fontSize: '2rem' // bigger size
                                        }}
                                      />
                                    ) : (
                                      <RadioButtonUncheckedIcon
                                        sx={{
                                          fontSize: '2rem' // bigger size
                                        }}
                                      />
                                    )}
                                  </IconButton>
                                </Card>
                              </Grid>
                            );
                          })}
                        </Grid>
                      )}
                    </Box>
                  </Box>
                )}
                <Box
                  sx={{
                    float: 'right',
                    marginTop: '30px',
                    paddingBottom: '20px'
                  }}
                  onClick={() => setIsSubmitted(true)}
                >
                  <SubmitButton
                    isLoading={loadingSpinner}
                    title={`${param.id ? 'UPDATE' : 'CREATE'}`}
                    sx={{
                      color: 'white2.main',
                      padding: '10px 35px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      '&:hover': {
                        color: 'white2.main',
                        backgroundColor: '#75E6DA',
                        boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                      }
                    }}
                  />
                </Box>
              </Box>
            </AppForm>
          </Box>
        </Box>
      )}

      <Dialog
        fullScreen={fullScreen}
        open={openModel}
        onClose={handleModalClose}
        aria-labelledby="customized-dialog-title"
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '20px',
            padding: '20px',
            backgroundImage: 'linear-gradient(to right, #ece9e6, #ffffff)',
            textTransform: 'capitalize'
          }
        }}
        PaperProps={{
          component: 'form'
        }}
      >
        <DialogTitle
          id="customized-dialog-title"
          sx={{
            fontSize: '24px',
            fontWeight: 'bold',
            textAlign: 'center',
            color: '#08366B'
          }}
        >
          Update Confirmation
        </DialogTitle>
        <DialogContent dividers>
          <DialogContentText
            sx={{
              fontSize: '18px',
              textAlign: 'center',
              color: '#616161',
              marginBottom: '20px'
            }}
          >
            Some users are still assigned to the apps being removed. Confirm
            removal before proceeding.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center' }}>
          <Button
            onClick={handleModalClose}
            sx={{
              backgroundColor: 'white2.main',
              color: 'primaryBlue.main',
              padding: '10px 30px',
              boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
              textTransform: 'capitalize'
            }}
          >
            CANCEL
          </Button>
          <LoadingButton
            onClick={() =>
              handleSubmit({
                ...organizationDetails,
                confirm_remove_user_apps: true
              })
            }
            loading={isLoading}
            sx={{
              backgroundColor: 'primaryBlue.main',
              color: 'white2.main',
              padding: '10px 35px',
              boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
              '&:hover': {
                color: 'white2.main',
                backgroundColor: '#08366b',
                boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
              }
            }}
          >
            Update
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* <SnackbarElement
        message={snackbarMessage}
        statusType={snackbarSeverity}
        snackbarOpen={openSnackbar}
        setSnackbarOpen={setOpenSnackbar}
      /> */}
    </Shell>
  );
};

export default OrganizationRegistrationForm;
