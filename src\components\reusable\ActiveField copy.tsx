// // Package Imports
// import { useDispatch, useSelector } from "react-redux";
// import { useEffect, useState } from "react";
// import { CSS } from "@dnd-kit/utilities";
// import { useFormikContext } from "formik";
// import {
//   Box,
//   Button,
//   Dialog,
//   DialogContent,
//   Divider,
//   FormControl,
//   FormControlLabel,
//   IconButton,
//   InputAdornment,
//   ListItemIcon,
//   ListItemText,
//   Menu,
//   MenuItem,
//   Select,
//   Switch,
//   TextField,
//   Tooltip,
//   Typography,
// } from "@mui/material";
// import { Circle, Close } from "@mui/icons-material";
// // import { ToWords } from "to-words";
// import ReactQuill from "react-quill";

// // Locale Imports
// import { RootState } from "../../redux/reducers";
// import { AppDispatch } from "../../redux/app.store";
// import { FormInput, FormSelect } from "../form.elements";
// import {
//   addFieldMethod,
//   addFormField,
//   addSectionMethod,
//   deletefield,
//   duplicatefield,
//   refetchform,
//   updateColumnIndex,
//   updatefield,
//   updateFieldActive,
//   updateFieldIndexes,
//   updatePreviousColumnIndex,
//   updatePreviousSectionIndex,
//   updateSectionIndex,
// } from "../../redux/reducers/form.reducer";
// import { Icon } from "./Icon";
// import VideoUploader from "./VideoUploader";
// import { AntSwitch } from "./AntSwitch";
// import "react-quill/dist/quill.snow.css";
// import { toPascalCase } from "../../utils/functions";
// import { numberToWords, small, toCamelCase } from "./StringToNumber";
// import { LoadingButton } from "@mui/lab";
// import AutoFill from "./AutoFill";
// import { CustomValidations } from "./CustomValidations";

// interface ActiveFieldProps {
//   name: string;
//   field: any;
//   setNodeRef?: any;
//   attributes?: any;
//   listeners?: any;
//   transform?: any;
//   transition?: any;
//   formFieldId?: string;
//   fieldLabelId?: any;
//   fieldOriginalId?: string;
//   fieldIteration: string;
//   fieldMaxIterationLength: string;
//   groupKey: string;
//   fieldId: string;
//   fielddescription: string;
//   fieldpoints: string;
//   fieldCheckboxAnswerType: string;
//   fieldCheckboxAnswerLimit: string;
//   fieldrequired: string;
// }

// // const toWords = new ToWords();

// export const ActiveField: React.FC<ActiveFieldProps> = ({
//   name,
//   field,
//   setNodeRef,
//   attributes,
//   listeners,
//   transform,
//   transition,
//   fieldLabelId,
//   fieldOriginalId,
//   fieldIteration,
//   fieldMaxIterationLength,
//   groupKey,
//   fieldId,
//   fielddescription,
//   fieldpoints,
//   fieldCheckboxAnswerType,
//   fieldCheckboxAnswerLimit,
//   fieldrequired,
// }) => {
//   const { formFields, form, is_quiz_form, formId, sectionIndex, columnIndex, loadingSpinner } =
//     useSelector((state: RootState) => state.form);
//   const dispatch = useDispatch<AppDispatch>();
//   const { values } = useFormikContext<any>();
//   const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
//   const [showFormControl, setShowFormControl] = useState<boolean>(false);
//   const [showCustomValidations, setShowCustomValidations] =
//     useState<boolean>(false);
//   const [showAutoFill, setShowAutoFill] =
//     useState<boolean>(false);
//   const [showDescription, setShowDescription] = useState<boolean>(false);
//   const [showRepeatField, setShowRepeatField] = useState<any>(
//     field?.is_iterative_or_not
//   );

//   const style = {
//     transition,
//     transform: CSS.Transform.toString(transform),
//   };

//   const formFieldsData = formFields?.map((s: any) => {
//     return {
//       label: s.name,
//       value: s.field_id,
//       icon: s?.skelton?.icon ? toPascalCase(s?.skelton?.icon) : null,
//     };
//   });

//   const [radioElements, setRadioElements] = useState<any>([]);
//   const [checkboxElements, setCheckboxElements] = useState<any>([]);
//   const [selectElements, setSelectElements] = useState<any>([]);
//   const [toggleElements, setToggleElements] = useState<any>();
//   // const [fieldConditions, setFieldConditions] = useState<any>([]);
//   const [fieldType, setFieldType] = useState<any>({});
//   const [selectedCountry, setSelectedCountry] = useState<any>(null);
//   const [selectedState, setSelectedState] = useState<any>(null);
//   const [openUploadDialog, setOpenUploadDialog] = useState(false);
//   const [openDialogInOptions, setOpenDialogInOptions] = useState(false);
//   const [showYoutubeLinkField, setShowYoutubeLinkField] = useState(false);
//   const [paragraphText, setParagraphText] = useState(field?.description || "");

//   const [conditionalFields, setConditionalFields] = useState([]);
//   const [selectedFieldOptions, setSelectedFieldOptions] = useState<any>([]);
//   const [selectedValue, setSelectedValue] = useState<any>();

//   // const small: any = {
//   //   zero: 0,
//   //   one: 1,
//   //   two: 2,
//   //   three: 3,
//   //   four: 4,
//   //   five: 5,
//   //   six: 6,
//   //   seven: 7,
//   //   eight: 8,
//   //   nine: 9,
//   //   ten: 10,
//   //   eleven: 11,
//   //   twelve: 12,
//   //   thirteen: 13,
//   //   fourteen: 14,
//   //   fifteen: 15,
//   //   sixteen: 16,
//   //   seventeen: 17,
//   //   eighteen: 18,
//   //   nineteen: 19,
//   //   twenty: 20,
//   //   twentyone: 21,
//   //   twentytwo: 22,
//   //   twentythree: 23,
//   //   twentyfour: 24,
//   //   twentyfive: 25,
//   //   twentysix: 26,
//   //   twentyseven: 27,
//   //   twentyeight: 28,
//   //   twentynine: 29,
//   //   thirty: 30,
//   //   forty: 40,
//   //   fifty: 50,
//   //   sixty: 60,
//   //   seventy: 70,
//   //   eighty: 80,
//   //   ninety: 90,
//   // };

//   useEffect(() => {
//     setShowDescription(field?.description?.length > 0 ? true : false);
//     if (field?.options) {
//       const textType = formFields?.find(
//         (s: any) => s.field_id == field?.original_field_id
//       );
//       setFieldType(textType);
//       if (textType?.skelton?.input_type == "radio") {
//         setRadioElements(field?.options);
//       }
//       if (textType?.skelton?.input_type == "checkbox") {
//         setCheckboxElements(field?.options);
//       }
//       if (textType?.skelton?.type == "select") {
//         setSelectElements(field?.options);
//       }
//       if (textType?.skelton?.type == "toggle") {
//         setToggleElements(field?.options);
//       }
//     }
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, []);

//   const handleClick = (event: React.MouseEvent<HTMLElement>) => {
//     setAnchorEl(event.currentTarget);
//   };

//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   const handleConditionalInputClick = () => {
//     setShowFormControl((prev) => !prev);
//     handleClose();
//   };

//   const handleCloseUploadDialog = () => {
//     setOpenUploadDialog(false);
//   };

//   const getFields = () => {
//     const updatedValues = { ...values };
//     const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

//     const data = updatedValues.groups[parseInt(keys[1])].fields.map(
//       (fd: any) => ({
//         value: fd?.field_id,
//         label: fd?.label,
//         name: fd?.name,
//         options: fd?.options,
//       })
//     );
//     setConditionalFields(data);
//   };

//   const addSection = async () => {
//     console.log("addSection")
//     dispatch(updatePreviousSectionIndex(sectionIndex));
//     dispatch(updatePreviousColumnIndex(columnIndex));

//     let type: any = {};

//     if (is_quiz_form) {
//       const textType = formFields?.find(
//         (s: any) => s.skelton.input_type == "radio"
//       );
//       setFieldType(textType);
//       type = textType;

//       setRadioElements([]);
//       setRadioElements([
//         ...radioElements,
//         {
//           label: "option1",
//           value: "option1",
//           status: false,
//           url: "",
//           url_type: "",
//         },
//       ]);
//     } else {
//       const textType = formFields?.find(
//         (s: any) => s.skelton.input_type == "text"
//       );
//       setFieldType(textType);
//       type = textType;
//     }

//     const city = formFields?.find((s: any) => s?.skelton?.input_type == "city");
//     const getSectionNumber = values.groups[values.groups.length - 1].group_key.split("_")[1].toLowerCase();

//     const newSection = {
//       id: "",
//       group_title: "Section Title ",
//       group_key:
//         "section_" + numberToWords[small[toCamelCase(getSectionNumber)] + 1],
//       group_index: "",
//       group_description: "",
//       is_iterative_or_not: false,
//       iteration_min_length: 1,
//       iteration_max_length: 2,
//       fields: [],
//     };

//     const secIdx = values.groups.length;

//     const data = {
//       group: {
//         key: newSection.group_key,
//         title: newSection.group_title,
//         group_description: newSection.group_description,
//         is_iterative_or_not: newSection.is_iterative_or_not,
//         iteration_max_length: newSection.iteration_max_length,
//         iteration_min_length: newSection.iteration_min_length,
//       },
//     };

//     await dispatch(addSectionMethod({ data, formId })).then(async (response: any) => {
//       if (response.payload.status) {
//         const getFieldNumber = values.groups[values.groups.length - 1].fields[
//           values.groups[values.groups.length - 1].fields.length - 1
//         ].name
//           .split("_")[1]
//           .toLowerCase();

//         const newField = {
//           name:
//             "field_" + numberToWords[small[toCamelCase(getFieldNumber)] + 1],
//           field_index: "",
//           field_id: "",
//           original_field_id: type?.field_id,
//           type: type?.skelton?.type,
//           input_type: type?.skelton?.input_type,
//           label: "Text",
//           label_url: "",
//           label_url_type: "",
//           placeHolder: "",
//           description_status: false,
//           description: '',
//           validation_schema: {
//             required: false,
//           },
//           options: is_quiz_form ? radioElements : "",
//           is_iterative_or_not: false,
//           iteration_min_length: 1,
//           iteration_max_length: 2,
//           points: 0,
//           checkbox_answer_type: "No limit",
//           checkbox_answer_limit: "",
//           value: "",
//           is_quiz_field: is_quiz_form ? true : false,
//           default_country: city ? city?.skelton?.default_country : "",
//           default_state: city ? city?.skelton?.default_state : "",
//         };

//         const fieldData = {
//           field: newField,
//           group_title: newSection?.group_title,
//           group_key: newSection?.group_key,
//         };
//         await dispatch(addFieldMethod({ fieldData, formId })).then((res: any) => {
//           if (res.payload.status) {
//             dispatch(updateSectionIndex(secIdx));
//             dispatch(updateColumnIndex(0));
//             dispatch(refetchform(formId));
//           }
//         });
//       }
//     });
//   };

//   const addField = async () => {
//     dispatch(updatePreviousColumnIndex(columnIndex));
//     let type: any = {};

//     if (is_quiz_form) {
//       const textType = formFields?.find(
//         (s: any) => s.skelton.input_type == "radio"
//       );
//       setFieldType(textType);
//       type = textType;

//       setRadioElements([]);
//     } else {
//       const textType = formFields?.find(
//         (s: any) => s.skelton.input_type == "text"
//       );
//       setFieldType(textType);
//       type = textType;
//     }

//     // For setting the default country and default state of this field
//     const city = formFields?.find((s: any) => s.skelton.input_type == "city");

//     // For setting the field_name
//     const getNumberText = values.groups[values.groups.length - 1].fields[
//       values.groups[values.groups.length - 1].fields.length - 1
//     ].name
//       .split("_")[1]
//       .toLowerCase();

//     const column = {
//       name:
//         "field_" + numberToWords[small[toCamelCase(getNumberText)] + 1],
//       field_index: "",
//       field_id: "",
//       original_field_id: type?.field_id,
//       type: type?.skelton.type,
//       input_type: type?.skelton?.input_type,
//       label: "Title",
//       label_url: "",
//       label_url_type: "",
//       placeHolder: "",
//       description_status: false,
//       description: "",
//       validation_schema: {
//         required: false,
//       },
//       options: is_quiz_form
//         ? [
//           {
//             label: `option ${radioElements?.length}`,
//             value: `option ${radioElements?.length}`,
//             status: false,
//             url: "",
//             url_type: "",
//           },
//         ]
//         : "",
//       is_iterative_or_not: false,
//       iteration_min_length: 1,
//       iteration_max_length: 2,
//       points: 0,
//       checkbox_answer_type: "No limit",
//       checkbox_answer_limit: "",
//       value: "",
//       is_quiz_field: is_quiz_form ? true : false,
//       default_country: city ? city.skelton.default_country : "",
//       default_state: city ? city.skelton.default_state : "",
//     };

//     if (columnIndex == values.groups[sectionIndex].fields.length - 1) {
//       dispatch(updateColumnIndex(values.groups[sectionIndex].fields.length));
//     } else {
//       dispatch(updateColumnIndex(columnIndex + 1));
//     }

//     const fieldData = {
//       field: column,
//       group_title: values.groups[sectionIndex].group_title,
//       group_key: values.groups[sectionIndex].group_key,
//     };

//     const response = await dispatch(addFieldMethod({ fieldData, formId }));
//     if (response.payload.status) {
//       const data = response.payload.data;
//       dispatch(addFormField({ data, sectionIndex, columnIndex }));

//       const fields = [
//         ...values.groups[sectionIndex].fields.slice(0, columnIndex + 1),
//         data,
//         ...values.groups[sectionIndex].fields.slice(columnIndex + 1),
//       ]
//       const indexes = fields.map((fd: any, index: number) => {
//         return {
//           field_id: fd?.field_id,
//           field_index: index,
//         }
//       });

//       const fieldsData = {
//         group_key: values.groups[sectionIndex].group_key,
//         group_index: values.groups[sectionIndex].group_index,
//         fields: indexes,
//       }

//       await dispatch(updateFieldIndexes({ fieldsData, formId }))
//     }
//   };

//   const deleteField = async (name: any) => {
//     const updatedValues = { ...values };
//     const keys = name.split(/[[\].]+/).filter(Boolean);

//     if (updatedValues.groups[parseInt(keys[1])].fields.length > 1) {
//       const data = {
//         field_id:
//           updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//             .field_id,
//         group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//       };

//       const response = await dispatch(deletefield({ formId, data }));

//       if (response.payload.status) {
//         if (columnIndex !== 0) {
//           dispatch(updateColumnIndex(columnIndex - 1));
//         }

//         const textType = formFields?.find(
//           (s: any) =>
//             s?.field_id ==
//             updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//               .original_field_id
//         );
//         setFieldType(textType);

//         if (textType?.skelton?.input_type == "radio") {
//           setRadioElements(
//             updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//               .options
//           );
//         }
//         if (textType?.skelton?.input_type == "checkbox") {
//           setCheckboxElements(
//             updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//               .options
//           );
//         }

//         if (textType?.skelton?.type == "select") {
//           setSelectElements(
//             updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//               .options
//           );
//         }

//         if (textType?.skelton?.type == "toggle") {
//           setToggleElements(
//             updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//               .options
//           );
//         }

//         if (textType?.skelton?.type == "city") {
//           setSelectedCountry(null);
//           setSelectedState(null);
//         }

//         if (textType?.skelton?.type == "state") {
//           setSelectedCountry(null);
//           setSelectedState(null);
//         }

//         window.location.reload();
//       }
//     } else {
//       window.alert(`Cant delete the Filed.`);
//     }
//   };

//   const addDuplicateField = async () => {
//     const data = {
//       field_id: fieldId,
//       group_key: groupKey,
//     };

//     const response = await dispatch(duplicatefield({ formId, data }));
//     if (response.payload.status) {
//       window.location.reload();
//     }
//   };

//   const handleSelect = (e: any) => {
//     const data: any = conditionalFields.find(
//       (field: any) => field?.value == e.target.value
//     );

//     setSelectedValue(e.target.value);
//     setSelectedFieldOptions(data?.options);
//   };

//   const handleFieldOptionSelect = async (e: any) => {
//     const updatedValues = { ...values };
//     const parent: any = conditionalFields.find(
//       (field: any) => field?.value == selectedValue
//     );

//     const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

//     const fieldData = {
//       ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
//       conditions: [
//         {
//           field_id: selectedValue,
//           field_name: parent.name,
//           field_value: e.target.value,
//         },
//       ],
//       group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//       group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//     };
//     await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//       dispatch(updateFieldActive(res.payload.data));
//     });
//   };

//   const changeOption = async (e: any) => {
//     const { value, name } = e.target;
//     const keys = name.split(/[[\].]+/).filter(Boolean); // Split name to get keys
//     const updatedValues = { ...values };

//     let obj = updatedValues;
//     keys.forEach((key: any, index: number) => {
//       if (index === keys.length - 1) {
//         obj[key] = value;
//       } else {
//         obj[key] = { ...obj[key] }; // Ensure immutability
//         obj = obj[key];
//       }
//     });

//     const textType = formFields?.find((s: any) => s?.field_id == value);
//     setFieldType(textType);

//     if (textType?.skelton?.input_type == "radio") {
//       setRadioElements([
//         {
//           value: "option1",
//           label: "Option1",
//           status: false,
//           url: "",
//           url_type: "",
//         },
//       ]);
//     }
//     if (textType?.skelton?.input_type == "checkbox") {
//       setCheckboxElements([
//         {
//           value: "option1",
//           label: "Option1",
//           status: false,
//           url: "",
//           url_type: "",
//         },
//       ]);
//     }

//     if (textType?.skelton?.type == "select") {
//       setSelectElements([
//         {
//           value: "option1",
//           label: "Option1",
//         },
//       ]);
//     }

//     if (textType?.skelton?.type == "toggle") {
//       setToggleElements(textType?.skelton?.options);
//     }

//     updatedValues.groups[parseInt(keys[1])]["fields"][
//       parseInt(keys[3])
//     ].original_field_id = value;
//     updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])].type =
//       textType?.skelton?.type;
//     updatedValues.groups[parseInt(keys[1])]["fields"][
//       parseInt(keys[3])
//     ].input_type = textType?.skelton?.input_type;
//     updatedValues.groups[parseInt(keys[1])]["fields"][
//       parseInt(keys[3])
//     ].options = textType?.skelton?.options;

//     const fieldData = {
//       ...updatedValues.groups[parseInt(keys[1])].fields[parseInt(keys[3])],
//       group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//       group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//     };
//     await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//       dispatch(updateFieldActive(res.payload.data));
//     });
//   };

//   const addElement = async (type: any) => {
//     const updatedValues = { ...values };
//     const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);
//     let fieldOptions: any[] = [];

//     if (type == "radio") {
//       setRadioElements([
//         ...radioElements,
//         {
//           label: `Option ${radioElements.length + 1}`,
//           value: `Option ${radioElements.length + 1}`,
//           status: false,
//           url: "",
//           url_type: "",
//         },
//       ]);
//     }
//     if (type == "checkbox") {
//       setCheckboxElements([
//         ...checkboxElements,
//         {
//           label: `Option ${checkboxElements.length + 1}`,
//           value: `Option ${checkboxElements.length + 1}`,
//           status: false,
//           url: "",
//           url_type: "",
//         },
//       ]);
//     }
//     if (type == "select") {
//       fieldOptions = [
//         ...selectElements,
//         {
//           label: `Option ${selectElements.length + 1}`,
//           value: `Option ${selectElements.length + 1}`,
//         },
//       ];

//       // setSelectElements([
//       //   ...selectElements,
//       //   {
//       //     label: `Option ${selectElements.length + 1}`,
//       //     value: `Option ${selectElements.length + 1}`,
//       //   },
//       // ]);
//     }

//     const fieldData = {
//       ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
//       options: fieldOptions,
//       group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//       group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//     };
//     await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//       dispatch(updateFieldActive(res.payload.data));
//       // dispatch(refetchform(formId));
//     });
//   };

//   const changeElement = (e: any, type: any, i: number, id: any) => {
//     const { value } = e.target;
//     const updatedValues = { ...values };

//     if (type == "select") {
//       if (value !== "" || null || undefined) {
//         setSelectElements((prevElements: any) => {
//           const updatedSelectElements = prevElements.map(
//             (select: any, index: number) =>
//               index === i ? { label: value, value: value } : select
//           );
//           const fieldIndex = updatedValues.groups[
//             sectionIndex
//           ].fields.findIndex((fd: any) => fd?.field_id === id);

//           updatedValues.groups[sectionIndex].fields[fieldIndex].options =
//             updatedSelectElements;
//           updateFieldValue();
//           return updatedSelectElements;
//         });
//       }
//     }
//     if (type == "checkbox") {
//       if (value !== "" || null || undefined) {
//         setCheckboxElements((prevElements: any) => {
//           const updatedCheckboxElements = prevElements.map(
//             (checkbox: any, index: number) =>
//               index === i ? { label: value, value: value } : checkbox
//           );
//           const fieldIndex = updatedValues.groups[
//             sectionIndex
//           ].fields.findIndex((fd: any) => fd?.field_id === id);

//           updatedValues.groups[sectionIndex].fields[fieldIndex].options =
//             updatedCheckboxElements;
//           updateFieldValue();
//           return updatedCheckboxElements;
//         });
//       }
//     }
//     if (type == "radio") {
//       if (value !== "" || null || undefined) {
//         const updatedRadioelements = radioElements.map(
//           (radio: any, index: number) =>
//             index === i ? { label: value, value: value } : radio
//         );
//         const fieldIndex = updatedValues.groups[
//           sectionIndex
//         ].fields.findIndex((fd: any) => fd?.field_id === id);

//         updatedValues.groups[sectionIndex].fields[fieldIndex].options =
//           updatedRadioelements;

//         setRadioElements(updatedRadioelements);
//         updateFieldValue();
//       }
//     }
//     if (type == "toggle") {
//       if (value !== "" || null || undefined) {
//         setToggleElements((prevElements: any) => {
//           const updatedToggleElements = prevElements.map(
//             (toggle: any, index: number) =>
//               index === i ? { label: value, value: value } : toggle
//           );
//           const fieldIndex = updatedValues.groups[
//             sectionIndex
//           ].fields.findIndex((fd: any) => fd?.field_id === id);

//           updatedValues.groups[sectionIndex].fields[fieldIndex].options =
//             updatedToggleElements;
//           updateFieldValue();
//           return updatedToggleElements;
//         });
//       }
//     }
//   };

//   const deleteElement = async (type: string, name: any, i: number) => {
//     const updatedValues = { ...values };
//     const keys = name.split(/[[\].]+/).filter(Boolean);

//     if (type == "radio") {
//       const updatedRadioelements = radioElements.filter(
//         (_ele: any, index: number) => index !== i
//       );
//       setRadioElements(updatedRadioelements);

//       const fieldData = {
//         ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
//         options: updatedRadioelements,
//         group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//         group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//       };
//       await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//         dispatch(updateFieldActive(res.payload.data));
//         location.reload();
//       });
//     } else if (type == "checkbox") {
//       setCheckboxElements(async (prevElements: any) => {
//         const updatedCheckboxelements =
//           prevElements.length > 1 ? prevElements.splice(i, 1) : prevElements;

//         const fieldData = {
//           ...updatedValues.groups[parseInt(keys[1])]["fields"][
//           parseInt(keys[3])
//           ],
//           options: updatedCheckboxelements,
//           group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//           group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//         };
//         await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//           dispatch(updateFieldActive(res.payload.data));
//           location.reload();
//         });

//         return updatedCheckboxelements;
//       });
//     }
//   };

//   const handleVideoAdd = (data: { value1: boolean; value2: string }): void => {
//     const keys = name.split(/[[\].]+/).filter(Boolean); // Split name to get keys
//     const updatedValues = { ...values };

//     let obj = updatedValues;
//     keys.forEach((key: any, index: number) => {
//       if (index === keys.length - 1) {
//         obj[key] = data.value2;
//       } else {
//         obj[key] = { ...obj[key] }; // Ensure immutability
//         obj = obj[key];
//       }
//     });
//   };

//   const handleFileInput = (event: any, type: any, i?: any): void => {
//     const file = event.target.files?.[0];

//     if (file) {
//       const reader = new FileReader();

//       reader.onload = async () => {
//         const imageSrc = reader.result as string;
//         const updatedValues = { ...values };
//         const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

//         if (imageSrc && type == "label") {
//           const fieldData = {
//             ...updatedValues.groups[parseInt(keys[1])]["fields"][
//             parseInt(keys[3])
//             ],
//             label_url: imageSrc,
//             label_url_type: "image",
//             options:
//               updatedValues.groups[parseInt(keys[1])]["fields"][
//                 parseInt(keys[3])
//               ].options,

//             group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//             group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//           };
//           await dispatch(updatefield({ fieldData, formId })).then(
//             (res: any) => {
//               dispatch(updateFieldActive(res.payload.data));
//             }
//           );
//           setOpenUploadDialog(false);
//         } else if (type == "option") {
//           if (fieldType.skelton.input_type == "radio") {
//             setRadioElements(async (prevElements: any) => {
//               const updatedRadioelements = prevElements.map(
//                 (radio: any, index: number) =>
//                   index === i
//                     ? { ...radio, url: imageSrc, url_type: "image" }
//                     : radio
//               );
//               const fieldData = {
//                 ...updatedValues.groups[parseInt(keys[1])]["fields"][
//                 parseInt(keys[3])
//                 ],
//                 options: updatedRadioelements,

//                 group_title:
//                   updatedValues.groups[parseInt(keys[1])].group_title,
//                 group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//               };
//               await dispatch(updatefield({ fieldData, formId })).then(
//                 (res: any) => {
//                   dispatch(updateFieldActive(res.payload.data));
//                 }
//               );
//               return updatedRadioelements;
//             });
//           } else if (fieldType.skelton.input_type == "checkbox") {
//             setCheckboxElements(async (prevElements: any) => {
//               const updatedCheckboxelements = prevElements.map(
//                 (checkbox: any, index: number) =>
//                   index === i
//                     ? { ...checkbox, url: imageSrc, url_type: "image" }
//                     : checkbox
//               );
//               const fieldData = {
//                 ...updatedValues.groups[parseInt(keys[1])]["fields"][
//                 parseInt(keys[3])
//                 ],
//                 options: updatedCheckboxelements,

//                 group_title:
//                   updatedValues.groups[parseInt(keys[1])].group_title,
//                 group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//               };
//               await dispatch(updatefield({ fieldData, formId })).then(
//                 (res: any) => {
//                   dispatch(updateFieldActive(res.payload.data));
//                 }
//               );
//               return updatedCheckboxelements;
//             });
//           }

//           setOpenUploadDialog(false);
//           setOpenDialogInOptions(false);
//         }
//       };

//       reader.readAsDataURL(file);
//     }
//   };

//   const insertImage = (type: any, i?: any): void => {
//     setShowYoutubeLinkField(false);
//     const fileInput = document.createElement("input");
//     fileInput.type = "file";
//     fileInput.style.display = "none";
//     fileInput.addEventListener("change", (e) => handleFileInput(e, type, i));
//     fileInput.click();
//   };

//   const deleteMedia = async (type: string, name: any, i?: number) => {
//     const updatedValues = { ...values };
//     const keys = name.split(/[[\].]+/).filter(Boolean);

//     if (type == "label") {
//       const fieldData = {
//         ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
//         label_url: "",
//         label_url_type: "",
//         group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//         group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//       };
//       await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//         dispatch(updateFieldActive(res.payload.data));
//       });
//     } else if (type == "option") {
//       if (fieldType.skelton.input_type == "radio") {
//         setRadioElements(async (prevElements: any) => {
//           const updatedRadioelements = prevElements.map(
//             (radio: any, index: number) =>
//               index === i ? { ...radio, url: "", url_type: "" } : radio
//           );

//           const fieldData = {
//             ...updatedValues.groups[parseInt(keys[1])]["fields"][
//             parseInt(keys[3])
//             ],
//             options: updatedRadioelements,
//             group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//             group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//           };
//           await dispatch(updatefield({ fieldData, formId })).then(
//             (res: any) => {
//               dispatch(updateFieldActive(res.payload.data));
//             }
//           );

//           return updatedRadioelements;
//         });
//       } else if (fieldType.skelton.input_type == "checkbox") {
//         setCheckboxElements(async (prevElements: any) => {
//           const updatedCheckboxelements = prevElements.map(
//             (checkbox: any, index: number) =>
//               index === i ? { ...checkbox, url: "", url_type: "" } : checkbox
//           );

//           const fieldData = {
//             ...updatedValues.groups[parseInt(keys[1])]["fields"][
//             parseInt(keys[3])
//             ],
//             options: updatedCheckboxelements,
//             group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//             group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//           };
//           await dispatch(updatefield({ fieldData, formId })).then(
//             (res: any) => {
//               dispatch(updateFieldActive(res.payload.data));
//             }
//           );

//           return updatedCheckboxelements;
//         });
//       }
//     }
//   };

//   const handlePragraphText = async (value: any) => {
//     setParagraphText(value);
//     const updatedValues = { ...values };
//     const keys = fielddescription.split(/[[\].]+/).filter(Boolean);

//     const fieldData = {
//       ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
//       description: value,
//       group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//       group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//     };
//     await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//       dispatch(updateFieldActive(res.payload.data));
//     });
//   };

//   const handleInputChange = async (e: any, values: any) => {
//     e.preventDefault();
//     const { name, value } = e.target;

//     const keys = name.split(/[[\].]+/).filter(Boolean); // Split name to get keys
//     const updatedValues = { ...values };

//     let obj = updatedValues;
//     keys.forEach((key: any, index: number) => {
//       if (index === keys.length - 1) {
//         obj[key] = value;
//       } else {
//         obj[key] = { ...obj[key] }; // Ensure immutability
//         obj = obj[key];
//       }
//     });

//     if (value == "on") {
//       let fieldData = null;
//       if (keys[5] == "required") {
//         fieldData = {
//           ...form?.groups[parseInt(keys[1])]?.fields[parseInt(keys[3])],
//           validation_schema: {
//             required:
//               !form?.groups[parseInt(keys[1])]?.fields[parseInt(keys[3])]
//                 ?.validation_schema?.required,
//           },
//           group_title: form?.groups[parseInt(keys[1])]?.group_title,
//           group_key: form?.groups[parseInt(keys[1])]?.group_key,
//         };
//       } else {
//         fieldData = {
//           ...form?.groups[parseInt(keys[1])].fields[parseInt(keys[3])],
//           iteration_max_length:
//             form?.groups[parseInt(keys[1])].fields[parseInt(keys[3])]
//               .iteration_max_length < 1
//               ? 1
//               : form?.groups[parseInt(keys[1])].fields[parseInt(keys[3])]
//                 .iteration_max_length,
//           group_title: form?.groups[parseInt(keys[1])].group_title,
//           group_key: form?.groups[parseInt(keys[1])].group_key,
//         };
//       }

//       await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//         dispatch(updateFieldActive(res.payload.data));
//       });
//     } else {
//       const fieldData = {
//         ...updatedValues.groups[parseInt(keys[1])].fields[parseInt(keys[3])],
//         iteration_max_length:
//           updatedValues.groups[parseInt(keys[1])].fields[parseInt(keys[3])]
//             .iteration_max_length < 1
//             ? 1
//             : updatedValues.groups[parseInt(keys[1])].fields[parseInt(keys[3])]
//               .iteration_max_length,
//         group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//         group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//       };
//       await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//         dispatch(updateFieldActive(res.payload.data));
//       });
//     }
//   };

//   const updateFieldValue = async () => {
//     const updatedValues = { ...values };
//     const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

//     const fieldData = {
//       ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
//       // iteration_max_length:
//       //   updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//       //     .iteration_max_length < 1
//       //     ? 2
//       //     : updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
//       //       .iteration_max_length,
//       group_title: updatedValues.groups[parseInt(keys[1])].group_title,
//       group_key: updatedValues.groups[parseInt(keys[1])].group_key,
//     };

//     await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
//       dispatch(updateFieldActive(res.payload.data));
//       // dispatch(refetchform(formId));
//     });
//   };

//   return (
//     <Box sx={{
//       position: 'relative',
//       marginBottom: '70px',
//     }}>
//       <Box
//         sx={{
//           backgroundColor: "#fff",
//           borderRadius: "4px",
//           padding: "30px 30px 10px 30px",
//           boxShadow: "0px 1px 0px 2px #24242410",
//           display: "flex",
//           flexDirection: "column",
//           gap: "10px",
//           marginTop: "10px",
//           borderLeft: "4px solid #36C0ED",
//           ...style,
//         }}
//       >
//         <Box
//           ref={setNodeRef}
//           {...attributes}
//           {...listeners}
//           sx={{
//             display: "flex",
//             alignItems: "center",
//             justifyContent: "center",
//             cursor: "pointer",
//           }}
//         >
//           <Icon
//             name="OpenWith"
//             sx={{
//               fontSize: "18px",
//             }}
//           />
//         </Box>

//         <Box
//           sx={{
//             display: "flex",
//             alignItems: "center",
//             justifyContent: "space-between",
//             backgroundColor: "#fff",
//             gap: is_quiz_form ? "20px" : "10px",
//             paddingTop: "10px",
//           }}
//         >
//           <Box sx={{ display: "flex", flexGrow: 1, height: "40px" }}>
//             <FormInput
//               className="section-options"
//               name={fieldLabelId}
//               label=""
//               // type={field?.type}
//               placeholder="Title"
//               handleInputChange={handleInputChange}
//               containerStyles={{
//                 width: "100%",
//                 height: "45px",
//                 margin: "0px",
//                 "& .section-options": {
//                   marginTop: "0px",
//                   height: "45px",
//                   "& input": {
//                     height: "45px",
//                     padding: "0px 10px",
//                   },
//                 },
//               }}
//             />
//           </Box>
//           {field?.is_quiz_field && (
//             <>
//               <IconButton onClick={() => setOpenUploadDialog(true)}>
//                 <Icon name="Image" sx={{ color: "black" }} fontSize="medium" />
//               </IconButton>
//               <Dialog
//                 open={openUploadDialog}
//                 onClose={handleCloseUploadDialog}
//                 aria-labelledby="alert-dialog-title"
//                 aria-describedby="alert-dialog-description"
//               >
//                 <DialogContent
//                   sx={{
//                     backgroundColor: "#FFF",
//                     padding: "20px",
//                   }}
//                 >
//                   <Box
//                     sx={{
//                       backgroundColor: "#FAF9F8",
//                       padding: "60px",
//                       display: "flex",
//                       flexDirection: "column",
//                     }}
//                   >
//                     <Button
//                       onClick={() => insertImage("label")}
//                       sx={{
//                         fontSize: "18px",
//                         padding: "10px 20px",
//                       }}
//                     >
//                       Insert Image
//                     </Button>
//                     <Button
//                       onClick={() => setShowYoutubeLinkField(true)}
//                       sx={{
//                         fontSize: "18px",
//                         padding: "10px 20px",
//                       }}
//                     >
//                       Insert Video
//                     </Button>
//                     {showYoutubeLinkField && (
//                       <Box>
//                         <h1>Paste Youtube URL</h1>
//                         <VideoUploader onVideoAdd={handleVideoAdd} />
//                       </Box>
//                     )}
//                   </Box>
//                 </DialogContent>
//               </Dialog>
//             </>
//           )}

//           <Box
//             sx={{
//               width: "300px",
//               height: "45px",
//               display: "flex",
//               alignItems: "center",
//             }}
//           >
//             <FormSelect
//               name={fieldOriginalId}
//               data={formFieldsData}
//               onChange={changeOption}
//               containerStyles={{
//                 width: "100%",
//                 height: "45px",
//                 marginBottom: "0px",
//                 position: "relative",
//                 top: "0px",
//                 padding: "0px",
//                 "& .MuiInputBase-root": {
//                   height: "45px",
//                   margin: "0px",
//                   paddingTop: "0px",
//                   paddingBottom: "0px",
//                   display: 'flex',
//                   "& .MuiSelect-select": {
//                     height: "45px",
//                     margin: "0px",
//                     paddingTop: "0px",
//                     paddingBottom: "0px",
//                   },
//                 },
//               }}
//               iconStyles={{
//                 height: "45px",
//                 marginRight: "10px",
//                 fontSize: "24px",
//               }}
//               menuStyles={{
//                 height: "45px",
//                 fontSize: "16px",
//               }}
//             />
//           </Box>
//         </Box>

//         {field?.label_url_type == "video" ? (
//           <Box>
//             <iframe
//               width="260"
//               height="180"
//               src={field?.label_url}
//               frameBorder="0"
//               allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
//               allowFullScreen
//               style={{ marginLeft: "8px" }}
//               title="Label related video"
//             ></iframe>
//             <IconButton>
//               <Icon name="Delete" />
//             </IconButton>
//           </Box>
//         ) : (
//           field?.label_url_type == "image" && (
//             <Box>
//               <img
//                 src={field?.label_url}
//                 alt="Label related image"
//                 style={{ marginLeft: "8px", width: "260px", height: "180px" }}
//               />
//               <IconButton onClick={() => deleteMedia("label", fieldLabelId)}>
//                 <Icon name="Delete" />
//               </IconButton>
//             </Box>
//           )
//         )}

//         <Box sx={{ marginTop: "10px" }}>
//           {fieldType?.skelton?.type === "select" && (
//             <Box>
//               {selectElements.map((select: any, index: number) => {
//                 return (
//                   <Box key={index}>
//                     <TextField
//                       id="input-with-icon-textfield"
//                       // label="TextField"
//                       defaultValue={select?.label}
//                       onChange={(e) =>
//                         changeElement(e, "select", index, field?.field_id)
//                       }
//                       InputProps={{
//                         startAdornment: (
//                           <InputAdornment position="start">
//                             <Circle />
//                           </InputAdornment>
//                         ),
//                         endAdornment: (
//                           <InputAdornment position="start">
//                             <Close />
//                           </InputAdornment>
//                         ),
//                       }}
//                       variant="standard"
//                       sx={{
//                         width: "100%",
//                         height: "50px",
//                       }}
//                     />
//                   </Box>
//                 );
//               })}

//               <Box
//                 onClick={() => addElement("select")}
//                 sx={{
//                   borderBottom: '1px solid rgba(0,0,0,0.42)',
//                   display: 'flex',
//                   alignItems: 'center',
//                   paddingBottom: '4px',
//                   cursor: 'pointer',
//                 }}>
//                 {/* <TextField
//                   id="input-with-icon-textfield"
//                   InputProps={{
//                     startAdornment: (
//                       <InputAdornment position="start">
//                         <Circle />
//                       </InputAdornment>
//                     ),
//                   }}
//                   variant="standard"
//                   onClick={() => addElement("select")}
//                   sx={{
//                     width: "100%",
//                     margin: "10px 0px",
//                   }}
//                   placeholder="Enter Your Text..."
//                 /> */}
//                 <Icon name="Circle" sx={{ color: 'rgba(0,0,0,0.5)' }} />
//                 <Typography sx={{
//                   paddingLeft: '8px',
//                 }}>Enter Your Text...</Typography>
//               </Box>
//             </Box>
//           )}

//           {fieldType?.skelton?.input_type === "radio" && (
//             <Box>
//               {radioElements.map((radio: any, index: number) => {
//                 return (
//                   <Box key={index}>
//                     <TextField
//                       id="input-with-icon-textfield"
//                       defaultValue={radio?.label}
//                       onChange={(e) =>
//                         changeElement(e, "radio", index, field?.field_id)
//                       }
//                       InputProps={{
//                         startAdornment: (
//                           <InputAdornment position="start">
//                             <Circle />
//                           </InputAdornment>
//                         ),
//                         endAdornment: (
//                           <InputAdornment position="start">
//                             {is_quiz_form && (
//                               <>
//                                 <IconButton
//                                   onClick={() => setOpenDialogInOptions(true)}
//                                 >
//                                   <Icon
//                                     name="Image"
//                                     sx={{ color: "black" }}
//                                     fontSize="medium"
//                                   />
//                                 </IconButton>
//                                 <Dialog
//                                   open={openDialogInOptions}
//                                   onClose={() => setOpenDialogInOptions(false)}
//                                   aria-labelledby="alert-dialog-title"
//                                   aria-describedby="alert-dialog-description"
//                                 >
//                                   <DialogContent
//                                     sx={{
//                                       backgroundColor: "#FFF",
//                                       padding: "20px",
//                                     }}
//                                   >
//                                     <Box
//                                       sx={{
//                                         backgroundColor: "#FAF9F8",
//                                         padding: "60px",
//                                         display: "flex",
//                                         flexDirection: "column",
//                                       }}
//                                     >
//                                       <Button
//                                         onClick={() =>
//                                           insertImage("option", index)
//                                         }
//                                         sx={{
//                                           fontSize: "18px",
//                                           padding: "10px 20px",
//                                         }}
//                                       >
//                                         Insert Image
//                                       </Button>
//                                     </Box>
//                                   </DialogContent>
//                                 </Dialog>
//                               </>
//                             )}
//                             <IconButton
//                               onClick={() =>
//                                 deleteElement("radio", fieldLabelId, index)
//                               }
//                             >
//                               <Close />
//                             </IconButton>
//                           </InputAdornment>
//                         ),
//                       }}
//                       variant="standard"
//                       sx={{
//                         width: "100%",
//                         height: "50px",
//                         "& input": {
//                           borderBottomWidth: 0,
//                         },
//                       }}
//                     />
//                     {radio?.url_type == "image" && (
//                       <Box>
//                         <img
//                           src={radio?.url}
//                           alt="Label related image"
//                           style={{
//                             marginLeft: "8px",
//                             width: "260px",
//                             height: "180px",
//                           }}
//                         />
//                         <IconButton
//                           onClick={() =>
//                             deleteMedia("option", fieldLabelId, index)
//                           }
//                         >
//                           <Icon name="Delete" />
//                         </IconButton>
//                       </Box>
//                     )}
//                     {radio?.url_type == "video" && (
//                       <Box>
//                         <iframe
//                           width="260"
//                           height="180"
//                           src={radio?.url}
//                           frameBorder="0"
//                           allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
//                           allowFullScreen
//                           style={{ marginLeft: "8px" }}
//                           title="Label related video"
//                         ></iframe>
//                         <IconButton
//                           onClick={() =>
//                             deleteMedia("option", fieldLabelId, index)
//                           }
//                         >
//                           <Icon name="Delete" />
//                         </IconButton>
//                       </Box>
//                     )}
//                   </Box>
//                 );
//               })}

//               <Box>
//                 <TextField
//                   id="input-with-icon-textfield"
//                   InputProps={{
//                     startAdornment: (
//                       <InputAdornment position="start">
//                         <Circle />
//                       </InputAdornment>
//                     ),
//                   }}
//                   variant="standard"
//                   // disabled
//                   onClick={() => addElement("radio")}
//                   sx={{
//                     width: "100%",
//                     margin: "10px 0px",
//                   }}
//                   placeholder="Add Option..."
//                 />
//               </Box>
//             </Box>
//             // <Options type='radio' elements={radioElements} field={field} fieldLabelId={fieldLabelId} fieldType={fieldType} />
//           )}

//           {fieldType?.skelton?.input_type === "checkbox" && (
//             <Box>
//               {checkboxElements.map((checkbox: any, index: number) => {
//                 return (
//                   <Box key={index}>
//                     <TextField
//                       id="input-with-icon-textfield"
//                       defaultValue={checkbox?.label}
//                       onChange={(e) =>
//                         changeElement(e, "checkbox", index, field?.field_id)
//                       }
//                       InputProps={{
//                         startAdornment: (
//                           <InputAdornment position="start">
//                             <Icon name="CheckBoxOutlineBlankOutlined" />
//                           </InputAdornment>
//                         ),
//                         endAdornment: (
//                           <InputAdornment position="start">
//                             {is_quiz_form && (
//                               <>
//                                 <IconButton
//                                   onClick={() => setOpenDialogInOptions(true)}
//                                 >
//                                   <Icon
//                                     name="Image"
//                                     sx={{ color: "black" }}
//                                     fontSize="medium"
//                                   />
//                                 </IconButton>
//                                 <Dialog
//                                   open={openDialogInOptions}
//                                   onClose={() => setOpenDialogInOptions(false)}
//                                   aria-labelledby="alert-dialog-title"
//                                   aria-describedby="alert-dialog-description"
//                                 >
//                                   <DialogContent
//                                     sx={{
//                                       backgroundColor: "#FFF",
//                                       padding: "20px",
//                                     }}
//                                   >
//                                     <Box
//                                       sx={{
//                                         backgroundColor: "#FAF9F8",
//                                         padding: "60px",
//                                         display: "flex",
//                                         flexDirection: "column",
//                                       }}
//                                     >
//                                       <Button
//                                         onClick={() =>
//                                           insertImage("option", index)
//                                         }
//                                         sx={{
//                                           fontSize: "18px",
//                                           padding: "10px 20px",
//                                         }}
//                                       >
//                                         Insert Image
//                                       </Button>
//                                     </Box>
//                                   </DialogContent>
//                                 </Dialog>
//                               </>
//                             )}
//                             <IconButton
//                               onClick={() =>
//                                 deleteElement("checkbox", fieldLabelId, index)
//                               }
//                             >
//                               <Close
//                                 sx={{
//                                   cursor: "pointer",
//                                 }}
//                               />
//                             </IconButton>
//                           </InputAdornment>
//                         ),
//                       }}
//                       variant="standard"
//                       sx={{
//                         width: "100%",
//                         height: "50px",
//                         "& input": {
//                           borderBottomWidth: 0,
//                         },
//                       }}
//                     />
//                     {checkbox?.url_type == "image" && (
//                       <Box>
//                         <img
//                           src={checkbox?.url}
//                           alt="Label related image"
//                           style={{
//                             marginLeft: "8px",
//                             width: "260px",
//                             height: "180px",
//                           }}
//                         />
//                         <IconButton
//                           onClick={() =>
//                             deleteMedia("option", fieldLabelId, index)
//                           }
//                         >
//                           <Icon name="Delete" />
//                         </IconButton>
//                       </Box>
//                     )}
//                     {checkbox?.url_type == "video" && (
//                       <Box>
//                         <iframe
//                           width="260"
//                           height="180"
//                           src={checkbox?.url}
//                           frameBorder="0"
//                           allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
//                           allowFullScreen
//                           style={{ marginLeft: "8px" }}
//                           title="Label related video"
//                         ></iframe>
//                         <IconButton
//                           onClick={() =>
//                             deleteMedia("option", fieldLabelId, index)
//                           }
//                         >
//                           <Icon name="Delete" />
//                         </IconButton>
//                       </Box>
//                     )}
//                   </Box>
//                 );
//               })}

//               <Box>
//                 <TextField
//                   id="input-with-icon-textfield"
//                   InputProps={{
//                     startAdornment: (
//                       <InputAdornment position="start">
//                         <Icon name="CheckBoxOutlineBlankOutlined" />
//                       </InputAdornment>
//                     ),
//                   }}
//                   variant="standard"
//                   onClick={() => addElement("checkbox")}
//                   sx={{
//                     width: "100%",
//                     margin: "10px 0px",
//                   }}
//                   placeholder="Enter Your Text..."
//                 />
//               </Box>
//             </Box>
//           )}

//           {fieldType?.skelton?.type === "toggle" && (
//             <Box>
//               {toggleElements.map((toggle: any, index: number) => {
//                 return (
//                   <Box key={index}>
//                     <TextField
//                       id="input-with-icon-textfield"
//                       defaultValue={toggle?.label}
//                       onChange={(e) =>
//                         changeElement(e, "toggle", index, field?.field_id)
//                       }
//                       InputProps={{
//                         startAdornment: (
//                           <InputAdornment position="start">
//                             <Circle />
//                           </InputAdornment>
//                         ),
//                         endAdornment: (
//                           <InputAdornment position="start">
//                             <Close />
//                           </InputAdornment>
//                         ),
//                       }}
//                       variant="standard"
//                       sx={{
//                         width: "100%",
//                         height: "50px",
//                       }}
//                     />
//                   </Box>
//                 );
//               })}
//             </Box>
//           )}
//           {fieldType?.skelton?.input_type === "state" && (
//             <Box>
//               <Typography>{selectedCountry}</Typography>
//             </Box>
//           )}
//           {fieldType?.skelton?.input_type === "city" && (
//             <Box>
//               <Typography>{selectedCountry}</Typography>
//               <Typography>{selectedState}</Typography>
//             </Box>
//           )}
//           {field?.original_field_id == "ad014e76-af87-4aca-8514-46b278cff9f0" &&
//             fieldType?.skelton?.type === "paragraph" && (
//               <Box>
//                 <ReactQuill
//                   theme="snow"
//                   value={paragraphText}
//                   onChange={handlePragraphText}
//                 />
//               </Box>
//             )}
//         </Box>

//         {/* {showCustomValidations && <CustomValidations selectedGroupData={selectedGroupData} />} */}

//         {showFormControl && (
//           <>
//             {/* <ConditionalInput /> */}
//             <Box display="flex" justifyContent="space-between" alignItems="center" gap={2}>
//               <Box flex={1}>
//                 <FormControl fullWidth>
//                   <Typography variant="subtitle1" gutterBottom>
//                     Select Field
//                   </Typography>
//                   <Select
//                     value={selectedValue}
//                     onChange={handleSelect}
//                     displayEmpty
//                   >
//                     <MenuItem value=""><em>Select Field</em></MenuItem>
//                     {conditionalFields?.map((field: any) => (
//                       <MenuItem key={field?.value} value={field?.value}>
//                         {field?.label}
//                       </MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>
//               </Box>

//               <Box flex={1}>
//                 <FormControl fullWidth>
//                   <Typography variant="subtitle1" gutterBottom>
//                     Select Field Option
//                   </Typography>
//                   <Select
//                     onChange={handleFieldOptionSelect}
//                     displayEmpty
//                   >
//                     <MenuItem value=""><em>Select Field option type</em></MenuItem>
//                     {selectedFieldOptions?.map((field: any) => (
//                       <MenuItem key={field?.value} value={field?.value}>
//                         {field?.label}
//                       </MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>
//               </Box>
//             </Box>

//           </>
//         )}
//         {is_quiz_form && fieldType?.skelton?.input_type === "checkbox" && (
//           <Box>
//             <Box
//               sx={{
//                 display: "flex",
//                 alignItems: "center",
//                 gap: "10px",
//               }}
//             >
//               <Typography>Select Total Options :</Typography>
//               <FormSelect
//                 name={fieldCheckboxAnswerType}
//                 onChange={handleInputChange}
//                 label=""
//                 data={[
//                   {
//                     value: "No limit",
//                     label: "No limit",
//                   },
//                   {
//                     value: "Equal to",
//                     label: "Equal to",
//                   },
//                   {
//                     value: "At most",
//                     label: "At most",
//                   },
//                 ]}
//               />
//             </Box>
//             {is_quiz_form && (
//               <FormInput
//                 name={fieldCheckboxAnswerLimit}
//                 label=""
//                 type="number"
//                 inputMode="numeric"
//                 handleInputChange={handleInputChange}
//                 containerStyles={{
//                   margin: "0px",
//                   height: "45px",
//                   minWidth: "100px",
//                   "& .MuiInputBase-formControl": {
//                     marginTop: "0px",
//                   },
//                   "& input": {
//                     marginTop: "0px",
//                     height: "45px",
//                   },
//                 }}
//               />
//             )}
//           </Box>
//         )}
//         {(showDescription && fieldType?.skelton?.type !== 'paragraph') && (
//           <Box>
//             <ReactQuill
//               theme="snow"
//               value={paragraphText}
//               onChange={handlePragraphText}
//             />
//           </Box>
//         )}

//         {showAutoFill && (
//           <Box>
//             <AutoFill />
//           </Box>
//         )}

//         {showCustomValidations && (
//           <Box>
//             <CustomValidations />
//           </Box>
//         )}

//         <Divider sx={{ padding: "5px" }} />
//         <Box
//           sx={{
//             display: "flex",
//             justifyContent: "end",
//             alignItems: "center",
//           }}
//         >
//           {showRepeatField && (
//             <Box
//               sx={{
//                 display: "flex",
//                 alignItems: "center",
//               }}
//             >
//               {showRepeatField && (
//                 <Box
//                   sx={{
//                     width: "100px",
//                   }}
//                 >
//                   <FormInput
//                     name={fieldMaxIterationLength}
//                     label=""
//                     type="number"
//                     inputMode="numeric"
//                     defaultValue={field.iteration_max_length || 1}
//                     handleInputChange={handleInputChange}
//                     containerStyles={{
//                       margin: "0px",
//                       height: "45px",
//                       "& .MuiInputBase-formControl": {
//                         marginTop: "0px",
//                       },
//                       "& input": {
//                         marginTop: "0px",
//                         height: "45px",
//                       },
//                     }}
//                   />
//                 </Box>
//               )}
//               <FormControlLabel
//                 name={fieldIteration}
//                 control={<Switch defaultChecked={field?.is_iterative_or_not} />}
//                 onChange={handleInputChange}
//                 label="Repeat Field"
//                 labelPlacement="start"
//               />
//             </Box>
//           )}
//           {!is_quiz_form && (
//             <Tooltip title={"Copy/Duplicate Field"}>
//               <IconButton onClick={addDuplicateField}>
//                 <Icon
//                   name="FileCopyOutlined"
//                   fontSize="medium"
//                   sx={{ color: "#36C0ED" }}
//                 />
//               </IconButton>
//             </Tooltip>
//           )}

//           {is_quiz_form && (
//             <Box
//               sx={{
//                 display: "flex",
//                 alignItems: "center",
//                 flexGrow: 1,
//               }}
//             >
//               {is_quiz_form && (
//                 <Box
//                   sx={{
//                     width: "100px",
//                     display: "flex",
//                     alignItems: "center",
//                     minWidth: "150px",
//                     gap: "10px",
//                     flexGrow: 1,
//                   }}
//                 >
//                   <Typography>Points :</Typography>
//                   <FormInput
//                     name={fieldpoints}
//                     label=""
//                     type="number"
//                     inputMode="numeric"
//                     handleInputChange={handleInputChange}
//                     containerStyles={{
//                       margin: "0px",
//                       height: "45px",
//                       minWidth: "100px",
//                       "& .MuiInputBase-formControl": {
//                         marginTop: "0px",
//                       },
//                       "& input": {
//                         marginTop: "0px",
//                         height: "45px",
//                       },
//                     }}
//                   />
//                 </Box>
//               )}
//             </Box>
//           )}
//           {values.groups[sectionIndex].fields.length > 1 && (
//             <Tooltip title={"Delete Field"}>
//               <IconButton onClick={() => deleteField(fieldLabelId)}>
//                 <Icon
//                   name="DeleteOutline"
//                   fontSize="medium"
//                   sx={{ color: "#36C0ED" }}
//                 />
//               </IconButton>
//             </Tooltip>
//           )}
//           {is_quiz_form && (
//             <Box>
//               <FormControlLabel
//                 name={fieldOriginalId}
//                 control={
//                   <AntSwitch
//                     defaultChecked={
//                       fieldType?.skelton?.input_type == "checkbox"
//                     }
//                     inputProps={{ "aria-label": "ant design" }}
//                     sx={{ marginLeft: "6px" }}
//                   />
//                 }
//                 label="Multiple Answers:"
//                 labelPlacement="start"
//               />
//             </Box>
//           )}
//           <Divider
//             orientation="vertical"
//             variant="middle"
//             flexItem
//             sx={{ padding: "15px 4px" }}
//           />
//           <Box>
//             <FormControlLabel
//               name={fieldrequired}
//               control={
//                 <AntSwitch
//                   defaultChecked={field?.validation_schema?.required}
//                   inputProps={{ "aria-label": "ant design" }}
//                   sx={{ marginLeft: "6px" }}
//                   onChange={handleInputChange}
//                 />
//               }
//               label="Required"
//               labelPlacement="start"
//             />
//           </Box>
//           <Box sx={{ paddingLeft: "10px" }}>
//             <IconButton onClick={handleClick}>
//               <Icon name="MoreVert" sx={{ cursor: "pointer" }} />
//             </IconButton>
//             <Menu
//               anchorEl={anchorEl}
//               open={Boolean(anchorEl)}
//               onClose={handleClose}
//             >
//               {fieldType?.skelton?.type !== 'paragraph' && <MenuItem
//                 onClick={() => {
//                   setShowDescription(!showDescription);
//                   handleClose();
//                 }}
//               >
//                 <ListItemText primary="Description" />

//                 <ListItemIcon>
//                   {showDescription && (
//                     <Box
//                       sx={{
//                         display: "flex",
//                         alignItems: "center",
//                         justifyContent: "center",
//                         backgroundColor: "rgba(76, 175, 80, 0.2)",
//                         borderRadius: "50%",
//                         width: "24px",
//                         height: "24px",
//                         marginLeft: "auto",
//                       }}
//                     >
//                       <Icon
//                         name="Check"
//                         fontSize="small"
//                         sx={{ color: "#4caf50" }}
//                       />
//                     </Box>
//                   )}
//                 </ListItemIcon>
//               </MenuItem>}

//               {!is_quiz_form && (
//                 <MenuItem
//                   onClick={() => {
//                     handleConditionalInputClick();
//                     getFields();
//                   }}
//                 >
//                   <ListItemText primary="Conditional Input" />

//                   <ListItemIcon>
//                     {showFormControl && (
//                       <Box
//                         sx={{
//                           display: "flex",
//                           alignItems: "center",
//                           justifyContent: "center",
//                           backgroundColor: "rgba(76, 175, 80, 0.2)",
//                           borderRadius: "50%",
//                           width: "24px",
//                           height: "24px",
//                           marginLeft: "auto",
//                         }}
//                       >
//                         <Icon
//                           name="Check"
//                           fontSize="small"
//                           sx={{ color: "#4caf50" }}
//                         />
//                       </Box>
//                     )}
//                   </ListItemIcon>
//                 </MenuItem>
//               )}

//               {!is_quiz_form && (
//                 <MenuItem
//                   onClick={() => {
//                     setShowCustomValidations(!showCustomValidations);
//                     handleClose();
//                   }}
//                 >
//                   <ListItemText primary="Custom Validations" />
//                   <ListItemIcon>
//                     {showCustomValidations && (
//                       <Box
//                         sx={{
//                           display: "flex",
//                           alignItems: "center",
//                           justifyContent: "center",
//                           backgroundColor: "rgba(76, 175, 80, 0.2)",
//                           borderRadius: "50%",
//                           width: "24px",
//                           height: "24px",
//                           marginLeft: "auto",
//                         }}
//                       >
//                         <Icon
//                           name="Check"
//                           fontSize="small"
//                           sx={{ color: "#4caf50" }}
//                         />
//                       </Box>
//                     )}
//                   </ListItemIcon>
//                 </MenuItem>
//               )}

//               {!is_quiz_form && (
//                 <MenuItem
//                   onClick={() => {
//                     setShowAutoFill(!showAutoFill);
//                     handleClose();
//                   }}
//                 >
//                   <ListItemText primary="Auto Fill" />
//                   <ListItemIcon>
//                     {showAutoFill && (
//                       <Box
//                         sx={{
//                           display: "flex",
//                           alignItems: "center",
//                           justifyContent: "center",
//                           backgroundColor: "rgba(76, 175, 80, 0.2)",
//                           borderRadius: "50%",
//                           width: "24px",
//                           height: "24px",
//                           marginLeft: "auto",
//                         }}
//                       >
//                         <Icon
//                           name="Check"
//                           fontSize="small"
//                           sx={{ color: "#4caf50" }}
//                         />
//                       </Box>
//                     )}
//                   </ListItemIcon>
//                 </MenuItem>
//               )}

//               {!is_quiz_form && (
//                 <MenuItem
//                   onClick={() => {
//                     setShowRepeatField(!showRepeatField);
//                     handleClose();
//                   }}
//                 >
//                   <ListItemText primary="Repeat Field" />
//                   <ListItemIcon>
//                     {showRepeatField && (
//                       <Box
//                         sx={{
//                           display: "flex",
//                           alignItems: "center",
//                           justifyContent: "center",
//                           backgroundColor: "rgba(76, 175, 80, 0.2)",
//                           borderRadius: "50%",
//                           width: "24px",
//                           height: "24px",
//                           marginLeft: "auto",
//                         }}
//                       >
//                         <Icon
//                           name="Check"
//                           fontSize="small"
//                           sx={{ color: "#4caf50" }}
//                         />
//                       </Box>
//                     )}
//                   </ListItemIcon>
//                 </MenuItem>
//               )}
//             </Menu>
//           </Box>
//         </Box>
//       </Box>

//       <Box
//         sx={{
//           float: "right",
//           marginTop: "10px",
//           marginBottom: "20px",
//           height: "50px",
//           width: "100%",
//           display: "flex",
//           justifyContent: "flex-end",
//           position: 'absolute',
//           // position: values?.groups[sectionIndex]?.fields?.length - 1 === columnIndex ? 'absolute' : 'relative',
//         }}
//       >
//         <Box
//           sx={{
//             boxShadow: "0px 2px 4px 0px rgba(0,0,0,0.3)",
//             borderRadius: "4px",
//             padding: "3px",
//             display: "flex",
//             alignItems: "center",
//             gap: "6px",
//             width: "fit-content",
//           }}
//         >
//           <Button
//             startIcon={<Icon name="AddCircleOutline" color="primary" />}
//             onClick={addField}
//             sx={{
//               color: "#616161",
//               height: "45px",
//               padding: "0px 20px",
//               "&:hover": {
//                 backgroundColor: "#FFF",
//               },
//             }}
//           >
//             Add Input
//           </Button>
//           <LoadingButton
//             loading={loadingSpinner}
//             startIcon={<Icon name="AddCircleOutline" color="primary" />}
//             onClick={addSection}
//             sx={{
//               color: "#616161",
//               height: "45px",
//               padding: "0px 20px",
//               "&:hover": {
//                 backgroundColor: "#FFF",
//               },
//             }}
//           >
//             Add Section
//           </LoadingButton>
//         </Box>
//       </Box>
//     </Box>
//   );
// };
