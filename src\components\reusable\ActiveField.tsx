/* eslint-disable react-hooks/exhaustive-deps */
// Package Imports
import { useDispatch, useSelector } from 'react-redux';
import {
  useEffect,
  //  useRef,
  useState
} from 'react';
import { CSS } from '@dnd-kit/utilities';
import { useFormikContext } from 'formik';
import '../../css/index.scss';
import '../../css/active-field-styles.scss';
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  InputAdornment,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Modal,
  Select,
  Switch,
  TextField,
  Tooltip,
  Typography,
  Checkbox,
  Radio,
  Autocomplete,
  Chip
} from '@mui/material';
import CalendarViewDayOutlinedIcon from '@mui/icons-material/CalendarViewDayOutlined';
import { Circle, Close } from '@mui/icons-material';
// import { ToWords } from "to-words";
import ReactQuill from 'react-quill';
import { toast } from 'react-toastify';

// Locale Imports
import { LoadingButton } from '@mui/lab';
import { RootState } from '../../redux/reducers';
import { AppDispatch } from '../../redux/app.store';
import { FormInput, FormSelect } from '../form.elements';
import {
  addFieldMethod,
  addSectionMethod,
  deletefield,
  duplicatefield,
  getcountrieslist,
  getstateslist,
  loadFieldSpinner,
  loadSectionSpinner,
  refetchform,
  updateColumnIndex,
  updatefield,
  updateFieldActive,
  updateFieldIndexes,
  updatePreviousColumnIndex,
  updatePreviousSectionIndex,
  updateSectionIndex,
  updateSelectedCountry,
  updateSelectedState
} from '../../redux/reducers/form.reducer';
import { Icon } from './Icon';
import VideoUploader from './VideoUploader';
import { AntSwitch } from './AntSwitch';
import 'react-quill/dist/quill.snow.css';
import { toPascalCase } from '../../utils/functions';
import { numberToWords, small, toCamelCase } from './StringToNumber';
import { useDebounceCallback } from '../../utils/customHooks/useDebounce';
import AutoFill from './AutoFill';
import { CustomValidations } from './CustomValidations';
import currencyOptions, { CurrencyOption } from '../../utils/currency_codes';

interface ActiveFieldProps {
  name?: string;
  field: any;
  setNodeRef?: any;
  attributes?: any;
  listeners?: any;
  transform?: any;
  transition?: any;
  formFieldId?: string;
  fieldLabelId?: any;
  fieldOriginalId?: string;
  fieldIteration: string;
  fieldMaxIterationLength: string;
  groupKey: string;
  fieldId: string;
  fielddescription: string;
  fieldpoints: string;
  fieldCheckboxAnswerType: string;
  fieldCheckboxAnswerLimit: string;
  fieldrequired: string;
  fieldValue?: any;
}

// const toWords = new ToWords();

export const ActiveField: React.FC<ActiveFieldProps> = ({
  field,
  setNodeRef,
  attributes,
  listeners,
  transform,
  transition,
  fieldLabelId,
  fieldOriginalId,
  fieldIteration,
  fieldMaxIterationLength,
  groupKey,
  fieldId,
  fielddescription,
  fieldpoints,
  fieldCheckboxAnswerType,
  fieldCheckboxAnswerLimit,
  fieldrequired,
  fieldValue
}) => {
  const {
    formFields,
    form,
    isQuizForm,
    formId,
    sectionIndex,
    columnIndex,
    fieldSpinner,
    sectionSpinner,
    countriesList,
    statesList,
    citiesList,
    selectedCountry,
    selectedState
  }: any = useSelector((state: RootState) => state.form);
  const dispatch = useDispatch<AppDispatch>();
  const { values } = useFormikContext<any>();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [showFormControl, setShowFormControl] = useState<boolean>(false);
  const [showCustomValidations, setShowCustomValidations] =
    useState<boolean>(false);
  const [showAutoFill, setShowAutoFill] = useState<boolean>(false);
  const [showDescription, setShowDescription] = useState<boolean>(false);
  const [showRepeatField, setShowRepeatField] = useState<any>(
    field?.is_iterative_or_not
  );
  const [lastUpdatedField, setLastUpdatedField] = useState<any>({ ...field }); // last changed input_type

  const checkBoxFieldData = [
    {
      value: 'No limit',
      label: 'No limit',
      disabled: false
    },
    {
      value: 'Equal to',
      label: 'Equal to',
      disabled: !(field?.options?.length > 1)
    },
    {
      value: 'At most',
      label: 'At most',
      disabled: !(field?.options?.length > 1)
    }
  ];

  // const optionSelect = field?.options?.length
  //   ? field?.options?.map((index: number) => {
  //       return index > 0 && { value: index + 1, label: index + 1 };
  //     })
  //   : '';

  const style = {
    transition,
    transform: CSS.Transform.toString(transform)
  };

  const formFieldsData = formFields?.map((s: any) => {
    return {
      label: s.name,
      value: s.field_id,
      icon: s?.skelton?.icon ? toPascalCase(s?.skelton?.icon) : null
    };
  });

  const [radioElements, setRadioElements] = useState<any>([]);
  const [checkboxElements, setCheckboxElements] = useState<any>([]);
  const [selectElements, setSelectElements] = useState<any>([]);
  const [toggleElements, setToggleElements] = useState<any>();
  // const [fieldConditions, setFieldConditions] = useState<any>([]);
  const [fieldType, setFieldType] = useState<any>({});
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [openDialogInOptions, setOpenDialogInOptions] = useState(false);
  const [dialogIndex, setDialogIndex] = useState<number>();
  const [showYoutubeLinkField, setShowYoutubeLinkField] = useState(false);
  const [paragraphText, setParagraphText] = useState(field?.description || '');

  const conditionalFields: any[] = [];
  // const [conditionalFields, setConditionalFields] = useState([]);
  const [selectedFieldOptions, setSelectedFieldOptions] = useState<any>([]);
  const [selectedValue, setSelectedValue] = useState<any>();

  const [countriesData, setCountriesData] = useState([]);
  const [statesData, setStatesData] = useState([]);
  const [citiesData, setCitiesData] = useState([]);
  const [urlErrMessage, setUrlErrMessage] = useState<boolean>(false);
  const [currency, setCurrency] = useState<CurrencyOption[]>([]);

  // const [value, setValue] = useState("")

  const getStatesData = async (countryCode: string) => {
    const res = await dispatch(getstateslist(countryCode));
    if (res.payload) {
      const states = res.payload?.map((state: any) => ({
        value: state?.isoCode,
        label: state?.name
      }));
      setStatesData(states);
    }
  };

  useEffect(() => {
    // setShowDescription(field?.description?.length > 0 ? true : false);
    const textType = formFields?.find(
      (s: any) => s.field_id === field?.original_field_id
    );
    setFieldType(textType);

    if (field?.options) {
      if (textType?.skelton?.input_type === 'radio') {
        setRadioElements(field?.options);
      }
      if (textType?.skelton?.input_type === 'checkbox') {
        setCheckboxElements(field?.options);
      }
      if (textType?.skelton?.type === 'select') {
        setSelectElements(field?.options);
      }
      if (textType?.skelton?.type === 'toggle') {
        setToggleElements(field?.options);
      }
    }
    if (field?.default_country) {
      dispatch(updateSelectedCountry(field?.default_country));
      if (field?.input_type === 'city') {
        getStatesData(field?.default_country);
      }
    } else {
      dispatch(updateSelectedCountry(''));
    }
    if (field?.default_state) {
      dispatch(updateSelectedState(field?.default_state));
    } else {
      dispatch(updateSelectedState(''));
    }
    if (field?.value) {
      // Ensure currency value is always an array for multiple selection
      if (Array.isArray(field?.value)) {
        setCurrency(field?.value);
      } else {
        // If field.value is a string or single value, convert it to array format
        const currencyValue =
          typeof field?.value === 'string'
            ? currencyOptions.filter((option) => option.code === field?.value)
            : [field?.value];
        setCurrency(currencyValue);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      !countriesList &&
      (field?.input_type === 'state' || field?.input_type === 'city')
    ) {
      (async () => {
        await dispatch(getcountrieslist(formId));
      })();
    }
    if (countriesList && countriesData?.length === 0) {
      const countries = countriesList?.map((country: any) => ({
        value: country?.isoCode,
        label: country?.name
      }));
      setCountriesData(countries);
    }
    if (statesList && statesData?.length === 0) {
      const states = statesList?.map((state: any) => ({
        value: state?.isoCode,
        label: state?.name
      }));
      setStatesData(states);
    }
    if (citiesList && citiesData?.length === 0) {
      const cities = citiesList?.map((city: any) => ({
        value: city?.isoCode,
        label: city?.name
      }));
      setCitiesData(cities);
    }
  }, [countriesList, statesList, citiesList]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleConditionalInputClick = () => {
    setShowFormControl((prev) => !prev);
    handleClose();
  };

  const handleCloseUploadDialog = () => {
    setOpenUploadDialog(false);
  };

  // const getFields = () => {
  //   const updatedValues = { ...values };
  //   const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

  //   const data = updatedValues.groups[parseInt(keys[1], 10)].fields.map(
  //     (fd: any) => ({
  //       value: fd?.field_id,
  //       label: fd?.label,
  //       name: fd?.name,
  //       options: fd?.options
  //     })
  //   );
  //   setConditionalFields(data);
  // };

  const handleDecriptionStatus = async () => {
    setShowDescription(!showDescription);
    handleClose();
    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      // iteration_max_length:
      //   updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
      //     .iteration_max_length < 1
      //     ? 2
      //     : updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])]
      //       .iteration_max_length,
      description_status: !field?.description_status,
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };

    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
    dispatch(refetchform(formId));
  };

  const handleCountrySelect = async (e: any) => {
    const countryCode = e.target.value;
    dispatch(updateSelectedCountry(countryCode));
    if (fieldType?.skelton?.input_type === 'city') {
      getStatesData(countryCode);
    }

    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      default_country: countryCode,
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
  };

  // const handleCurrency = (e: SelectChangeEvent<string[]>) => {
  //   console.log(e.target.value);
  //   const selectedCode = e.target.value.at(1); // latest clicked item
  //   const existing = [...currency]; // your state with full objects

  //   const isAlreadySelected = existing.some(
  //     (item) => item.code === selectedCode
  //   );

  //   let updatedSelected: CurrencyOption[];

  //   if (isAlreadySelected) {
  //     // Remove it
  //     updatedSelected = existing.filter((item) => item.code !== selectedCode);
  //   } else {
  //     // Add new object from master list
  //     const newItem = currencyOptions.find((opt) => opt.code === selectedCode);
  //     if (!newItem) return;
  //     updatedSelected = [...existing, newItem];
  //   }

  //   setCurrency(updatedSelected);

  //   const updatedValues = { ...values };
  //   const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

  //   const fieldData = {
  //     ...updatedValues.groups[parseInt(keys[1], 10)].fields[
  //       parseInt(keys[3], 10)
  //     ],
  //     value: updatedSelected, // full objects
  //     group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
  //     group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
  //   };

  //   dispatch(updatefield({ fieldData, formId })).then((res: any) => {
  //     setLastUpdatedField(res.payload.data);
  //     dispatch(updateFieldActive(res.payload.data));
  //   });
  // };

  // const handleCurrency = (e: SelectChangeEvent<string[]>) => {
  //   const selectedCodes = e.target.value as string[];

  //   const updatedSelected = currencyOptions.filter((opt) =>
  //     selectedCodes.includes(opt.code)
  //   );

  //   setCurrency(updatedSelected);

  //   const updatedValues = { ...values };
  //   const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

  //   const fieldData = {
  //     ...updatedValues.groups[parseInt(keys[1], 10)].fields[
  //       parseInt(keys[3], 10)
  //     ],
  //     value: updatedSelected,
  //     group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
  //     group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
  //   };

  //   dispatch(updatefield({ fieldData, formId })).then((res: any) => {
  //     setLastUpdatedField(res.payload.data);
  //     dispatch(updateFieldActive(res.payload.data));
  //   });
  // };

  const handleCurrency = (e: any, value: CurrencyOption[]) => {
    console.log(e);
    setCurrency(value);

    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      value,
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };

    // Validate currency selection
    if (value && value.length === 0) {
      toast.error('At least one currency must be selected');
      return;
    }

    dispatch(updatefield({ fieldData, formId })).then((res: any) => {
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
    });
  };

  const handleStateSelect = async (e: any) => {
    const stateCode = e.target.value;
    dispatch(updateSelectedState(stateCode));

    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      default_state: stateCode,
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
  };

  const addSection = async () => {
    dispatch(updatePreviousSectionIndex(sectionIndex));
    dispatch(updatePreviousColumnIndex(columnIndex));
    dispatch(loadSectionSpinner(true));

    let type: any = {};

    if (isQuizForm) {
      const textType = formFields?.find(
        (s: any) => s.skelton.input_type === 'radio'
      );
      setFieldType(textType);
      type = textType;
      setRadioElements([
        ...radioElements,
        {
          label: 'Option 1',
          value: 'option 1',
          status: false,
          url: '',
          url_type: ''
        }
      ]);
    } else {
      const textType = formFields?.find(
        (s: any) => s.skelton.input_type === 'text'
      );
      setFieldType(textType);
      type = textType;
    }

    const city = formFields?.find(
      (s: any) => s?.skelton?.input_type === 'city'
    );
    const getSectionNumber = values.groups[values.groups.length - 1].group_key
      .split('_')[1]
      .toLowerCase();

    const newSection = {
      id: '',
      group_title: 'Section Title',
      group_key: `section_${numberToWords[small[toCamelCase(getSectionNumber)] + 1]}`,
      group_index: '',
      group_description: '',
      is_iterative_or_not: false,
      iteration_min_length: 1,
      iteration_max_length: 2,
      fields: []
    };

    const secIdx = values.groups.length;

    const data = {
      group: {
        key: newSection.group_key,
        title: newSection.group_title,
        group_description: newSection.group_description,
        is_iterative_or_not: newSection.is_iterative_or_not,
        iteration_max_length: newSection.iteration_max_length,
        iteration_min_length: newSection.iteration_min_length
      }
    };

    const response = await dispatch(addSectionMethod({ data, formId }));

    if (response.payload.status) {
      toast.success('Section added successfully');

      // Fix: If no fields exist in the last group, start with field_One
      let getFieldNumber = 'zero';
      const lastGroup = values.groups[values.groups.length - 1];
      if (
        lastGroup.fields &&
        lastGroup.fields.length > 0 &&
        lastGroup.fields[lastGroup.fields.length - 1]?.name
      ) {
        getFieldNumber = lastGroup.fields[lastGroup.fields.length - 1].name
          .split('_')[1]
          .toLowerCase();
      }
      let nextFieldNumber = 1;
      try {
        nextFieldNumber = small[toCamelCase(getFieldNumber)] + 1;
      } catch {
        nextFieldNumber = 1;
      }
      const fieldName = `field_${numberToWords[nextFieldNumber]}`;

      // For quiz forms, always add at least one option
      const defaultOptions = isQuizForm
        ? [
            {
              label: 'Option 1',
              value: 'option 1',
              status: false,
              url: '',
              url_type: ''
            }
          ]
        : '';

      const newField = {
        name: fieldName,
        field_index: '',
        field_id: '',
        original_field_id: type?.field_id,
        type: type?.skelton?.type,
        input_type: type?.skelton?.input_type,
        label: 'Text',
        label_url: '',
        label_url_type: '',
        placeHolder: '',
        description_status: false,
        description: '',
        validation_schema: {
          required: false
        },
        options: isQuizForm ? defaultOptions : '',
        is_iterative_or_not: false,
        iteration_min_length: 1,
        iteration_max_length: 2,
        points: 0,
        checkbox_answer_type: 'No limit',
        checkbox_answer_limit: '',
        value: '',
        is_quiz_field: isQuizForm,
        default_country: city ? city?.skelton?.default_country : '',
        default_state: city ? city?.skelton?.default_state : ''
      };

      const fieldData = {
        field: newField,
        group_title: newSection?.group_title,
        group_key: newSection?.group_key
      };

      const res = await dispatch(addFieldMethod({ fieldData, formId }));
      if (res.payload.status) {
        dispatch(refetchform(formId)).then(() => {
          dispatch(updateSectionIndex(secIdx));
          dispatch(updateColumnIndex(0));
          dispatch(loadSectionSpinner(false));
        });
        // toast.success(res?.payload?.message);
      }
    } else {
      dispatch(loadSectionSpinner(false));
    }
  };

  const addField = async () => {
    dispatch(updatePreviousColumnIndex(columnIndex));
    dispatch(loadFieldSpinner(true));
    let type: any = {};
    if (isQuizForm) {
      const textType = formFields?.find(
        (s: any) => s.skelton.input_type === 'radio'
      );
      setFieldType(textType);
      type = textType;

      setRadioElements([]);
    } else {
      const textType = formFields?.find(
        (s: any) => s.skelton.input_type === 'text'
      );
      setFieldType(textType);
      type = textType;
    }
    // For setting the default country and default state of this field
    const city = formFields?.find((s: any) => s.skelton.input_type === 'city');
    // For setting the field_name
    const getNumberText = values.groups[values.groups.length - 1].fields[
      values.groups[values.groups.length - 1].fields.length - 1
    ].name
      .split('_')[1]
      .toLowerCase();

    const column = {
      name: `field_${numberToWords[small[toCamelCase(getNumberText)] + 1]}`,
      field_index: '',
      field_id: '',
      original_field_id: type?.field_id,
      type: type?.skelton.type,
      input_type: type?.skelton?.input_type,
      label: 'Title',
      label_url: '',
      label_url_type: '',
      placeHolder: '',
      description_status: false,
      description: '',
      validation_schema: {
        required: false
      },
      options: isQuizForm
        ? [
            {
              label: `Option 1`,
              value: `option 1`,
              status: false,
              url: '',
              url_type: ''
            }
          ]
        : '',
      is_iterative_or_not: false,
      iteration_min_length: 1,
      iteration_max_length: 2,
      points: 0,
      checkbox_answer_type: 'No limit',
      checkbox_answer_limit: '',
      value: '',
      is_quiz_field: isQuizForm,
      default_country: city ? city.skelton.default_country : '',
      default_state: city ? city.skelton.default_state : ''
    };

    const fieldData = {
      field: column,
      group_title: values.groups[sectionIndex].group_title,
      group_key: values.groups[sectionIndex].group_key
    };

    const response = await dispatch(addFieldMethod({ fieldData, formId }));
    if (response.payload.status) {
      const { data } = response.payload;

      const fields = [
        ...values.groups[sectionIndex].fields.slice(0, columnIndex + 1),
        data,
        ...values.groups[sectionIndex].fields.slice(columnIndex + 1)
      ];
      const indexes = fields.map((fd: any, index: number) => {
        return {
          field_id: fd?.field_id,
          field_index: index
        };
      });

      const fieldsData = {
        group_key: values.groups[sectionIndex].group_key,
        group_index: values.groups[sectionIndex].group_index,
        fields: indexes
      };

      await dispatch(updateFieldIndexes({ fieldsData, formId })).then(
        async () => {
          await dispatch(refetchform(formId)).then(() => {
            dispatch(updateColumnIndex(columnIndex + 1));
            dispatch(loadFieldSpinner(false));
          });
        }
      );
    }
  };

  const deleteField = async (fieldName: string) => {
    const updatedValues = { ...values };
    const keys = fieldName.split(/[[\].]+/).filter(Boolean);

    if (updatedValues.groups[parseInt(keys[1], 10)].fields.length > 1) {
      const data = {
        field_id:
          updatedValues.groups[parseInt(keys[1], 10)].fields[
            parseInt(keys[3], 10)
          ].field_id,
        group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
      };

      const response = await dispatch(deletefield({ formId, data }));

      if (response.payload.status) {
        const textType = formFields?.find(
          (s: any) =>
            s?.field_id ===
            updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ].original_field_id
        );
        setFieldType(textType);

        if (textType?.skelton?.input_type === 'radio') {
          setRadioElements(
            updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ].options
          );
        }
        if (textType?.skelton?.input_type === 'checkbox') {
          setCheckboxElements(
            updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ].options
          );
        }

        if (textType?.skelton?.type === 'select') {
          setSelectElements(
            updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ].options
          );
        }

        if (textType?.skelton?.type === 'toggle') {
          setToggleElements(
            updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ].options
          );
        }

        if (textType?.skelton?.type === 'city') {
          dispatch(updateSelectedCountry(null));
          dispatch(updateSelectedState(null));
        }

        if (textType?.skelton?.type === 'state') {
          dispatch(updateSelectedCountry(null));
          dispatch(updateSelectedState(null));
        }

        dispatch(refetchform(formId)).then(() => {
          if (
            columnIndex ===
            updatedValues.groups[parseInt(keys[1], 10)].fields.length - 1
          ) {
            dispatch(updateColumnIndex(columnIndex - 1));
          }
        });
      }
    }
  };

  const addDuplicateField = async () => {
    dispatch(updatePreviousColumnIndex(columnIndex));
    // dispatch(loadFieldSpinner(true));

    const fieldData = {
      field_id: fieldId,
      group_key: groupKey
    };

    const response = await dispatch(
      duplicatefield({ formId, data: fieldData })
    );
    if (response.payload.status) {
      const { data } = response.payload;

      const fields = [
        ...values.groups[sectionIndex].fields.slice(0, columnIndex + 1),
        data,
        ...values.groups[sectionIndex].fields.slice(columnIndex + 1)
      ];
      const indexes = fields.map((fd: any, index: number) => {
        return {
          field_id: fd?.field_id,
          field_index: index
        };
      });
      const fieldsData = {
        group_key: values.groups[sectionIndex].group_key,
        group_index: values.groups[sectionIndex].group_index,
        fields: indexes
      };

      await dispatch(updateFieldIndexes({ fieldsData, formId })).then(
        async () => {
          await dispatch(refetchform(formId)).then(async () => {
            dispatch(updateColumnIndex(columnIndex + 1));
            // dispatch(loadFieldSpinner(false));
          });
        }
      );
    }
  };

  const handleSelect = (e: any) => {
    const data: any = conditionalFields.find(
      (fieldData: any) => fieldData?.value === e.target.value
    );

    setSelectedValue(e.target.value);
    setSelectedFieldOptions(data?.options);
  };

  const handleFieldOptionSelect = async () => {};
  const handleConditionalInputSaveButton = async (e: any) => {
    const updatedValues = { ...values };
    const parent: any = conditionalFields.find(
      (fieldData: any) => fieldData?.value === selectedValue
    );

    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      conditions: [
        {
          field_id: selectedValue,
          field_name: parent.name,
          field_value: e.target.value
        }
      ],
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
    });
    setShowFormControl(false);
  };
  const updateMultipleAnswer = async (e: any) => {
    e.preventDefault();
    const updatedValues = { ...values };

    if (e.target.checked) {
      const textType = formFields?.find(
        (s: any) => s?.skelton.input_type === 'checkbox'
      );
      setFieldType(textType);
      const options = [
        {
          value: 'option 1',
          label: 'Option 1',
          status: false,
          url: '',
          url_type: ''
        }
      ];

      setCheckboxElements(radioElements.length > 0 ? radioElements : options);

      updatedValues.groups[sectionIndex].fields[columnIndex].options =
        radioElements.length > 0 ? radioElements : options;

      updatedValues.groups[sectionIndex].fields[columnIndex].type =
        textType?.skelton?.type;
      updatedValues.groups[sectionIndex].fields[columnIndex].input_type =
        textType?.skelton?.input_type;

      const fieldData = {
        ...updatedValues.groups[sectionIndex].fields[columnIndex],
        group_title: updatedValues.groups[sectionIndex].group_title,
        group_key: updatedValues.groups[sectionIndex].group_key
      };

      await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
        setLastUpdatedField(res.payload.data);
        dispatch(updateFieldActive(res.payload.data));
      });
    } else {
      const textType = formFields?.find(
        (s: any) => s?.skelton.input_type === 'radio'
      );
      setFieldType(textType);

      const options = [
        {
          value: 'option 1',
          label: 'Option 1',
          status: false,
          url: '',
          url_type: ''
        }
      ];
      setRadioElements(
        checkboxElements.length > 0 ? checkboxElements : options
      );
      updatedValues.groups[sectionIndex].fields[columnIndex].options =
        checkboxElements.length > 0 ? checkboxElements : options;

      updatedValues.groups[sectionIndex].fields[columnIndex].type =
        textType?.skelton?.type;
      updatedValues.groups[sectionIndex].fields[columnIndex].input_type =
        textType?.skelton?.input_type;

      const fieldData = {
        ...updatedValues.groups[sectionIndex].fields[columnIndex],
        group_title: updatedValues.groups[sectionIndex].group_title,
        group_key: updatedValues.groups[sectionIndex].group_key
      };
      const res = await dispatch(updatefield({ fieldData, formId }));
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
    }
  };
  const changeOption = async (e: any) => {
    e.preventDefault();
    const { value, name: optName } = e.target;

    const keys = optName.split(/[[\].]+/).filter(Boolean); // Split name to get keys
    const updatedValues = { ...values };

    let obj = updatedValues;
    keys.forEach((key: any, index: number) => {
      if (index === keys.length - 1) {
        obj[key] = value;
      } else {
        obj[key] = { ...obj[key] }; // Ensure immutability
        obj = obj[key];
      }
    });

    const textType = formFields?.find((s: any) => s?.field_id === value);
    setFieldType(textType);

    if (textType?.skelton?.input_type === 'radio') {
      const options = [
        {
          value: 'option 1',
          label: 'Option 1',
          status: false,
          url: '',
          url_type: ''
        }
      ];
      setRadioElements(options);
      updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ].options = options;
    }
    if (textType?.skelton?.input_type === 'checkbox') {
      const options = [
        {
          value: 'option 1',
          label: 'Option 1',
          status: false,
          url: '',
          url_type: ''
        }
      ];
      setCheckboxElements(options);
      updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ].options = options;
    }

    if (textType?.skelton?.type === 'select') {
      const options = [
        {
          value: 'option 1',
          label: 'Option 1'
        }
      ];
      setSelectElements(options);
      updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ].options = options;
    }

    if (textType?.skelton?.type === 'toggle') {
      setToggleElements(textType?.skelton?.options);
      updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ].options = textType?.skelton?.options;
    }

    updatedValues.groups[parseInt(keys[1], 10)].fields[
      parseInt(keys[3], 10)
    ].original_field_id = value;
    updatedValues.groups[parseInt(keys[1], 10)].fields[
      parseInt(keys[3], 10)
    ].type = textType?.skelton?.type;
    updatedValues.groups[parseInt(keys[1], 10)].fields[
      parseInt(keys[3], 10)
    ].input_type = textType?.skelton?.input_type;

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
  };

  const addElement = async (type: any) => {
    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);
    let fieldOptions: any[] = [];

    let hasEmptyFields: boolean = false;

    if (type === 'radio') {
      hasEmptyFields = radioElements.some(
        (option: any) => !option.value || !option.label
      );

      if (hasEmptyFields) {
        toast.error('Some options have empty label or value!');
        return;
      }

      fieldOptions = [
        ...radioElements,
        {
          label: `Option ${radioElements.length + 1}`,
          value: `Option ${radioElements.length + 1}`,
          status: false,
          url: '',
          url_type: ''
        }
      ];
      setRadioElements(fieldOptions);
    }
    if (type === 'checkbox') {
      hasEmptyFields = checkboxElements.some(
        (option: any) => !option.value || !option.label
      );
      if (hasEmptyFields) {
        toast.error('Some options have empty label or value!');
        return;
      }

      fieldOptions = [
        ...checkboxElements,
        {
          label: `Option ${checkboxElements.length + 1}`,
          value: `Option ${checkboxElements.length + 1}`,
          status: false,
          url: '',
          url_type: ''
        }
      ];
      setCheckboxElements(fieldOptions);
    }
    if (type === 'select') {
      hasEmptyFields = selectElements.some(
        (option: any) => !option.value || !option.label
      );
      if (hasEmptyFields) {
        toast.error('Some options have empty label or value!');
        return;
      }
      fieldOptions = [
        ...selectElements,
        {
          label: `Option ${selectElements.length + 1}`,
          value: `Option ${selectElements.length + 1}`
        }
      ];
      setSelectElements(fieldOptions);
    }

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      options: fieldOptions,
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
  };

  const updateOptionLabel = async (e: any, type: any, i: number) => {
    e.preventDefault();
    const { value } = e.target;

    if (value === null || value === undefined) return;

    const updatedValues = { ...values };
    let updatedElements: any[] = [];

    const updateElements = (prevElements: any[]) =>
      prevElements.map((element, index) =>
        index === i ? { ...element, label: value, value } : element
      );

    switch (type) {
      case 'select':
        updatedElements = updateElements(selectElements);
        break;
      case 'checkbox':
        updatedElements = updateElements(checkboxElements);
        break;
      case 'radio':
        updatedElements = updateElements(radioElements);
        break;
      case 'toggle':
        updatedElements = updateElements(toggleElements);
        break;
      default:
        window.console.error('Unknown element type:', type);
        return;
    }

    try {
      const fieldData = {
        ...updatedValues.groups[sectionIndex].fields[columnIndex],
        options: updatedElements,
        group_title: updatedValues.groups[sectionIndex].group_title,
        group_key: updatedValues.groups[sectionIndex].group_key
      };

      const res = await dispatch(updatefield({ fieldData, formId }));

      const payload = res?.payload;

      if (!payload?.status || payload?.statusCode) {
        const errorMessage = payload?.message;
        toast.error(
          Array.isArray(errorMessage) ? errorMessage.join('\n') : errorMessage
        );
      } else {
        dispatch(refetchform(formId));
      }
    } catch (error) {
      window.console.error('Error updating field:', error);
    }
  };

  const debouncedUpdateOptionLabel = useDebounceCallback(
    updateOptionLabel,
    800
  );

  const handleOptionLabelChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: string,
    index: number
  ) => {
    const { value } = e.target;

    if (value === null || value === undefined) return;

    let updatedElements: any[] = [];

    const updateElements = (prevElements: any[]) =>
      prevElements.map((element, i) =>
        i === index ? { ...element, label: value, value } : element
      );

    switch (type) {
      case 'select':
        setSelectElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      case 'checkbox':
        setCheckboxElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      case 'radio':
        setRadioElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      case 'toggle':
        setToggleElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      default:
        window.console.error('Unknown element type:', type);
        return;
    }

    debouncedUpdateOptionLabel(e, type, index);
  };

  const updateOptionStatus = async (e: any, type: any, i: number) => {
    e.preventDefault();
    const { checked } = e.target;
    if (checked === null || checked === undefined) return;
    const updatedValues = { ...values };
    let updatedElements: any[] = [];

    const updateElements = (prevElements: any[]) => {
      if (type === 'radio' || type === 'toggle') {
        // For radio, set the selected option's status to true and others to false
        return prevElements.map((element, index) =>
          index === i
            ? { ...element, status: checked }
            : { ...element, status: false }
        );
      }
      // For other types, handle as usual
      return prevElements.map((element, index) =>
        index === i ? { ...element, status: checked } : element
      );
    };

    switch (type) {
      case 'select':
        setSelectElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      case 'checkbox':
        setCheckboxElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      case 'radio':
        setRadioElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      case 'toggle':
        setToggleElements((prevElements: any) => {
          updatedElements = updateElements(prevElements);
          return updatedElements;
        });
        break;
      default:
        window.console.error('Unknown element type:', type);
        return;
    }

    try {
      const fieldData = {
        ...updatedValues.groups[sectionIndex].fields[columnIndex],
        options: updatedElements,
        group_title: updatedValues.groups[sectionIndex].group_title,
        group_key: updatedValues.groups[sectionIndex].group_key
      };

      const res = await dispatch(updatefield({ fieldData, formId }));
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
      if (res.payload.status) {
        const options = res.payload?.data?.options;
        // Update state with API response to ensure sync
        switch (type) {
          case 'select':
            setSelectElements(options);
            break;
          case 'checkbox':
            setCheckboxElements(options);
            break;
          case 'radio':
            setRadioElements(options);
            break;
          case 'toggle':
            setToggleElements(options);
            break;
          default:
            window.console.log('default type');
        }
      }
    } catch (error: any) {
      window.console.error('Error updating field:', error);
    }
  };

  const deleteElement = async (type: string, i: number) => {
    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    // Immediately update the UI
    if (type === 'radio' && radioElements?.length > 1) {
      setRadioElements((prevElements: any) =>
        prevElements.filter((_: any, index: number) => index !== i)
      );
    } else if (type === 'checkbox' && checkboxElements?.length > 1) {
      setCheckboxElements((prevElements: any) =>
        prevElements.filter((_: any, index: number) => index !== i)
      );
    } else if (type === 'select' && selectElements?.length > 1) {
      setSelectElements((prevElements: any) =>
        prevElements.filter((_: any, index: number) => index !== i)
      );
    }

    let option;
    if (type === 'radio') {
      option = radioElements.filter((_: any, index: number) => index !== i);
    } else if (type === 'checkbox') {
      option = checkboxElements.filter((_: any, index: number) => index !== i);
    } else if (type === 'select') {
      option = selectElements.filter((_: any, index: number) => index !== i);
    } else {
      option = [];
    }

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      options: option,
      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };

    try {
      const res = await dispatch(updatefield({ fieldData, formId }));
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
    } catch (error) {
      window.console.error('Error updating field:', error);
    }
  };

  const handleFileInput = (event: any, type: any, i?: number): void => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();

      reader.onload = async () => {
        const imageSrc = reader.result as string;
        const updatedValues = { ...values };
        const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

        if (imageSrc && type === 'label') {
          const fieldData = {
            ...updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ],
            label_url: imageSrc,
            label_url_type: 'image',
            options:
              updatedValues.groups[parseInt(keys[1], 10)].fields[
                parseInt(keys[3], 10)
              ].options,

            group_title:
              updatedValues.groups[parseInt(keys[1], 10)].group_title,
            group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
          };
          const res = await dispatch(updatefield({ fieldData, formId }));
          setLastUpdatedField(res.payload.data);
          dispatch(updateFieldActive(res.payload.data));
          setOpenUploadDialog(false);
        } else if (type === 'option') {
          if (fieldType.skelton.input_type === 'radio') {
            // await setRadioElements(async (prevElements: any) => {
            const updatedRadioelements = await radioElements.map(
              (radio: any, index: number) =>
                index === i
                  ? { ...radio, url: imageSrc, url_type: 'image' }
                  : radio
            );

            const fieldData = {
              ...updatedValues.groups[parseInt(keys[1], 10)].fields[
                parseInt(keys[3], 10)
              ],
              options: updatedRadioelements,
              group_title:
                updatedValues.groups[parseInt(keys[1], 10)].group_title,
              group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
            };

            await dispatch(updatefield({ fieldData, formId })).then(
              async (res: any) => {
                setLastUpdatedField(res.payload.data);
                await dispatch(updateFieldActive(res.payload.data));
              }
            );
            setRadioElements(updatedRadioelements);

            // });
          } else if (fieldType.skelton.input_type === 'checkbox') {
            // setCheckboxElements(async (prevElements: any) => {
            const updatedCheckboxelements = checkboxElements.map(
              (checkbox: any, index: number) =>
                index === i
                  ? { ...checkbox, url: imageSrc, url_type: 'image' }
                  : checkbox
            );
            const fieldData = {
              ...updatedValues.groups[parseInt(keys[1], 10)].fields[
                parseInt(keys[3], 10)
              ],
              options: updatedCheckboxelements,

              group_title:
                updatedValues.groups[parseInt(keys[1], 10)].group_title,
              group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
            };
            await dispatch(updatefield({ fieldData, formId })).then(
              async (res: any) => {
                setLastUpdatedField(res.payload.data);
                dispatch(updateFieldActive(res.payload.data));
              }
            );
            setCheckboxElements(updatedCheckboxelements);
            // });
          }
          setOpenUploadDialog(false);
          setOpenDialogInOptions(false);
        } else if (type === 'download') {
          const fieldData = {
            ...updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ],
            value: {
              file: imageSrc,
              mimetype: file.type,
              name: file.name
            },
            options:
              updatedValues.groups[parseInt(keys[1], 10)].fields[
                parseInt(keys[3], 10)
              ].options,

            group_title:
              updatedValues.groups[parseInt(keys[1], 10)].group_title,
            group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
          };
          await dispatch(updatefield({ fieldData, formId })).then(
            (res: any) => {
              setLastUpdatedField(res.payload.data);
              dispatch(updateFieldActive(res.payload.data));
            }
          );
        }
      };

      reader.readAsDataURL(file);
    }
  };

  const handleVideoUrlInput = async (url: string): Promise<void> => {
    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      label_url: url,
      label_url_type: 'video',
      options:
        updatedValues.groups[parseInt(keys[1], 10)].fields[
          parseInt(keys[3], 10)
        ].options,

      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
    setOpenUploadDialog(false);
  };

  const insertImage = (type: any, i?: number): void => {
    setShowYoutubeLinkField(false);
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';
    fileInput.addEventListener('change', (e) => handleFileInput(e, type, i));
    fileInput.click();
  };

  const deleteMedia = async (type: string, fileName: any, i?: number) => {
    const updatedValues = { ...values };
    const keys = fileName.split(/[[\].]+/).filter(Boolean);

    if (type === 'label') {
      const fieldData = {
        ...updatedValues.groups[parseInt(keys[1], 10)].fields[
          parseInt(keys[3], 10)
        ],
        label_url: '',
        label_url_type: '',
        group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
        group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
      };
      const res = await dispatch(updatefield({ fieldData, formId }));
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
    } else if (type === 'option') {
      if (fieldType.skelton.input_type === 'radio') {
        // setRadioElements(async (prevElements: any) => {
        const updatedRadioelements = radioElements.map(
          (radio: any, index: number) =>
            index === i ? { ...radio, url: '', url_type: '' } : radio
        );

        const fieldData = {
          ...updatedValues.groups[parseInt(keys[1], 10)].fields[
            parseInt(keys[3], 10)
          ],
          options: updatedRadioelements,
          group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
          group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
        };
        const res = await dispatch(updatefield({ fieldData, formId }));
        setLastUpdatedField(res.payload.data);
        dispatch(updateFieldActive(res.payload.data));
        setRadioElements(updatedRadioelements);
        // });
      } else if (fieldType.skelton.input_type === 'checkbox') {
        // setCheckboxElements(async (prevElements: any) => {
        const updatedCheckboxelements = checkboxElements.map(
          (checkbox: any, index: number) =>
            index === i ? { ...checkbox, url: '', url_type: '' } : checkbox
        );

        const fieldData = {
          ...updatedValues.groups[parseInt(keys[1], 10)].fields[
            parseInt(keys[3], 10)
          ],
          options: updatedCheckboxelements,
          group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
          group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
        };
        const res = await dispatch(updatefield({ fieldData, formId }));
        setLastUpdatedField(res.payload.data);
        dispatch(updateFieldActive(res.payload.data));

        setCheckboxElements(updatedCheckboxelements);
        // });
      }
    }
  };

  // Debounced function for paragraph text updates
  const debouncedUpdateParagraphText = useDebounceCallback(
    async (value: any) => {
      const updatedValues = { ...values };
      const keys = fielddescription.split(/[[\].]+/).filter(Boolean);

      const fieldData = {
        ...updatedValues.groups[parseInt(keys[1], 10)].fields[
          parseInt(keys[3], 10)
        ],
        description: value,
        group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
        group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
      };
      const res = await dispatch(updatefield({ fieldData, formId }));
      setLastUpdatedField(res.payload.data);
      dispatch(updateFieldActive(res.payload.data));
    },
    800
  );

  const handlePragraphText = async (value: any) => {
    setParagraphText(value);
    debouncedUpdateParagraphText(value);
  };

  const handleInputChange = async (e: any, inputValues: any) => {
    e.preventDefault();
    const { name: inputName, value } = e.target;

    const keys = inputName.split(/[[\].]+/).filter(Boolean); // Split name to get keys
    const updatedValues = { ...inputValues };

    let obj = updatedValues;

    keys.forEach((key: any, index: number) => {
      if (index === keys.length - 1) {
        obj[key] = value;
      } else {
        obj[key] = { ...obj[key] }; // Ensure immutability
        obj = obj[key];
      }
    });

    if (value === 'on') {
      let fieldData = null;
      if (keys.includes('required')) {
        fieldData = {
          ...form?.groups[parseInt(keys[1], 10)]?.fields[parseInt(keys[3], 10)],
          is_iterative_or_not:
            form?.groups[parseInt(keys[1], 10)].fields[parseInt(keys[3], 10)]
              .is_iterative_or_not,
          validation_schema: {
            required:
              !form?.groups[parseInt(keys[1], 10)]?.fields[
                parseInt(keys[3], 10)
              ]?.validation_schema?.required
          },
          group_title: form?.groups[parseInt(keys[1], 10)]?.group_title,
          group_key: form?.groups[parseInt(keys[1], 10)]?.group_key
        };
      } else {
        fieldData = {
          ...form?.groups[parseInt(keys[1], 10)].fields[parseInt(keys[3], 10)],
          is_iterative_or_not: e.target.checked,
          iteration_max_length: parseInt(
            form?.groups[parseInt(keys[1], 10)].fields[parseInt(keys[3], 10)]
              .iteration_max_length < 2
              ? 2
              : form?.groups[parseInt(keys[1], 10)].fields[
                  parseInt(keys[3], 10)
                ].iteration_max_length,
            10
          ),
          group_title: form?.groups[parseInt(keys[1], 10)].group_title,
          group_key: form?.groups[parseInt(keys[1], 10)].group_key
        };
      }

      await dispatch(updatefield({ fieldData, formId })).then(
        async (res: any) => {
          setLastUpdatedField(res.payload.data);
          dispatch(updateFieldActive(res.payload.data));
          dispatch(refetchform(formId));
        }
      );
    } else {
      const newField =
        updatedValues.groups?.[parseInt(keys[1], 10)]?.fields?.[
          parseInt(keys[3], 10)
        ] || {};

      const fieldData = {
        ...newField,
        is_iterative_or_not: newField.is_iterative_or_not,
        points: parseInt(newField?.points || 0, 10),
        iteration_max_length:
          parseInt(newField.iteration_max_length, 10) < 2
            ? 2
            : parseInt(newField.iteration_max_length, 10) || 2,
        group_title:
          updatedValues.groups?.[parseInt(keys[1], 10)]?.group_title || '',
        group_key:
          updatedValues.groups?.[parseInt(keys[1], 10)]?.group_key || ''
      };

      await dispatch(updatefield({ fieldData, formId })).then(
        async (res: any) => {
          setLastUpdatedField(res.payload.data);
          dispatch(updateFieldActive(res.payload.data));
          dispatch(refetchform(formId));
        }
      );
    }
  };
  // const updateFieldValue = async () => {
  //   const updatedValues = { ...values };
  //   const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

  //   const fieldData = {
  //     ...updatedValues.groups[parseInt(keys[1])]["fields"][parseInt(keys[3])],
  //     group_title: updatedValues.groups[parseInt(keys[1])].group_title,
  //     group_key: updatedValues.groups[parseInt(keys[1])].group_key,
  //   };

  //   await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
  //     dispatch(updateFieldActive(res.payload.data));
  //   });
  // };
  const isStateFieldInSection = () => {
    const currentSection = form?.groups?.[sectionIndex];
    return currentSection?.fields?.some(
      (fieldData: any) => fieldData.input_type === 'state'
    );
  };
  const isCountryFieldInSection = () => {
    const currentSection = form?.groups?.[sectionIndex];
    return currentSection?.fields?.some(
      (fieldData: any) => fieldData.input_type === 'country'
    );
  };

  const deleteDocument = async () => {
    const updatedValues = { ...values };
    const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

    const fieldData = {
      ...updatedValues.groups[parseInt(keys[1], 10)].fields[
        parseInt(keys[3], 10)
      ],
      value: '',
      options:
        updatedValues.groups[parseInt(keys[1], 10)].fields[
          parseInt(keys[3], 10)
        ].options,

      group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
      group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
    };
    const res = await dispatch(updatefield({ fieldData, formId }));
    setLastUpdatedField(res.payload.data);
    dispatch(updateFieldActive(res.payload.data));
  };

  const validateUrl = async (e?: any, urlValues?: any) => {
    e.preventDefault();
    const { name: urlName, value } = e.target;
    const keys = urlName.split(/[[\].]+/).filter(Boolean); // Split name to get keys
    const updatedValues = { ...urlValues };
    if (value !== '') {
      const regExp =
        /^((https?|ftp|smtp):\/\/)?(www.)?[a-z0-9]+(\.[a-z]{2,}){1,3}(#?\/?[a-zA-Z0-9#]+)*\/?(\?[a-zA-Z0-9-_]+=[a-zA-Z0-9-%]+&?)?$/;
      const match =
        updatedValues.groups[parseInt(keys[1], 10)].fields[
          parseInt(keys[3], 10)
        ].value.match(regExp);
      if (match) {
        handleInputChange(e, values);
        setUrlErrMessage(false);
      } else {
        handleInputChange(e, values);
        setUrlErrMessage(true);
      }
    } else {
      handleInputChange(e, values);
      setUrlErrMessage(true);
    }
  };

  return (
    <Box className="position-relative mb-70">
      <Box
        className="bg-white radius-4 d-flex flex-column gap-10 mt-10"
        sx={{
          padding: '30px 30px 10px 30px',
          boxShadow: '0px 1px 0px 2px #24242410',
          borderLeft: '4px solid #36C0ED',
          ...style
        }}
      >
        <Box
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          className="flex-center cursor-pointer"
        >
          <Icon
            name="OpenWith"
            sx={{
              fontSize: '18px'
            }}
          />
        </Box>

        <Box
          className="flex-space-betweeen bg-white pt-10"
          sx={{
            gap: isQuizForm ? '20px' : '10px'
          }}
        >
          <Box className="d-flex flex-1">
            <FormInput
              className="section-options"
              name={fieldLabelId}
              label=""
              // type={field?.type}
              placeholder="Title"
              handleInputChange={handleInputChange}
              containerStyles={{
                width: '100%',
                height: '45px',
                margin: '0px',
                '& .section-options': {
                  marginTop: '0px',
                  height: '45px',
                  '& input': {
                    height: '45px',
                    padding: '0px 10px'
                  }
                }
              }}
            />
          </Box>
          {field?.is_quiz_field && (
            <>
              <IconButton onClick={() => setOpenUploadDialog(true)}>
                <Icon name="Image" sx={{ color: 'black' }} fontSize="medium" />
              </IconButton>
              <Dialog
                open={openUploadDialog}
                onClose={handleCloseUploadDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
              >
                <DialogContent className="bg-white p-20">
                  <Box className="bg-FAF9F8 d-flex flex-column p-60">
                    <Button
                      onClick={() => insertImage('label')}
                      className="font-size-18 p-10-20"
                    >
                      Insert Image
                    </Button>
                    <Button
                      onClick={() => setShowYoutubeLinkField(true)}
                      className="font-size-18 p-10-20"
                    >
                      Insert Video
                    </Button>
                    {showYoutubeLinkField && (
                      <Box>
                        <h1>Paste Youtube URL</h1>
                        <VideoUploader
                          handleVideoUrlInput={handleVideoUrlInput}
                        />
                      </Box>
                    )}
                  </Box>
                </DialogContent>
              </Dialog>
            </>
          )}

          <Box className="w-300 h-45 d-flex align-items-center">
            <FormSelect
              name={fieldOriginalId}
              data={formFieldsData}
              onChange={changeOption}
              containerStyles={{
                width: '100%',
                height: '45px',
                marginBottom: '0px',
                position: 'relative',
                top: '0px',
                padding: '0px',
                '& .MuiInputBase-root': {
                  height: '45px',
                  margin: '0px',
                  paddingTop: '0px',
                  paddingBottom: '0px',
                  display: 'flex',
                  alignItems: 'center',
                  '& .MuiSelect-select': {
                    height: '45px',
                    margin: '0px',
                    paddingTop: '0px',
                    paddingBottom: '0px',
                    display: 'flex',
                    alignItems: 'center'
                  }
                }
              }}
              iconStyles={{
                height: '45px',
                marginRight: '10px',
                fontSize: '24px'
              }}
              menuStyles={{
                height: '45px',
                fontSize: '16px'
              }}
            />
          </Box>
        </Box>

        {field?.label_url_type === 'video' ? (
          <Box>
            <iframe
              width="260"
              height="180"
              src={field?.label_url}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              style={{ marginLeft: '8px' }}
              title="Label related video"
            />
            <IconButton onClick={() => deleteMedia('label', fieldLabelId)}>
              <Icon name="Delete" />
            </IconButton>
          </Box>
        ) : (
          field?.label_url_type === 'image' && (
            <Box>
              <img
                src={field?.label_url}
                alt="Label related"
                style={{ marginLeft: '8px', width: '260px', height: '180px' }}
              />
              <IconButton onClick={() => deleteMedia('label', fieldLabelId)}>
                <Icon name="Delete" />
              </IconButton>
            </Box>
          )
        )}

        <Box className="mt-10">
          {fieldType?.skelton?.type === 'download' && (
            <>
              {!field?.value && (
                <Box>
                  <p>Upload a file for the user to download</p>
                  <Button
                    variant="outlined"
                    sx={{
                      p: '7px 30px',
                      display: 'flex',
                      alignItems: 'center',
                      borderRadius: '50px',
                      boxShadow: '0px 0px 2px 0px',
                      color: '#000'
                    }}
                    onClick={() => insertImage('download')}
                  >
                    Upload
                  </Button>
                  <p style={{ color: 'red' }}>Upload document is mandatory</p>
                </Box>
              )}
              {field?.value && (
                <Box sx={{ display: 'flex' }}>
                  <p>Document uploaded successfully</p>
                  <Tooltip title="Delete Document" arrow>
                    <IconButton onClick={() => deleteDocument()}>
                      <Icon name="Delete" />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
            </>
          )}
          {fieldType?.skelton?.input_type === 'url' && (
            <Box>
              <p>
                Add a url for the user to see{' '}
                <span style={{ color: 'red' }}>*</span>
              </p>
              <FormInput
                className="section-options"
                name={fieldValue}
                label=""
                placeholder="Url"
                handleInputChange={validateUrl}
                containerStyles={{
                  width: '100%',
                  height: '45px',
                  margin: '0px',
                  '& .section-options': {
                    marginTop: '0px',
                    height: '45px',
                    '& input': {
                      height: '45px',
                      padding: '0px 10px'
                    }
                  }
                }}
              />
              {field?.value === '' && (
                <p style={{ color: 'red' }}>Url is mandatory</p>
              )}
              {urlErrMessage && field?.value !== '' && (
                <p style={{ color: 'red' }}>Invalid Url</p>
              )}
            </Box>
          )}
          {fieldType?.skelton?.type === 'select' && (
            <Box>
              {selectElements.map((select: any, index: number) => {
                const key = `${index}-${index * 3}-select-key`;
                return (
                  <Box key={key}>
                    <TextField
                      id="input-with-icon-textfield"
                      // label="TextField"
                      value={select?.label}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        handleOptionLabelChange(e, 'select', index);
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Circle />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="start">
                            {selectElements?.length > 1 && (
                              <Tooltip title="Delete Option" arrow>
                                <IconButton
                                  onClick={() => deleteElement('select', index)}
                                >
                                  <Close />
                                </IconButton>
                              </Tooltip>
                            )}
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      className="w-full h-50"
                    />
                  </Box>
                );
              })}

              <Box
                onClick={() => addElement('select')}
                className="d-flex align-items-center pb-4 cursor-pointer"
                sx={{
                  borderBottom: '1px solid rgba(0,0,0,0.42)'
                }}
              >
                <Icon name="Circle" sx={{ color: 'rgba(0,0,0,0.5)' }} />
                <Typography className="pl-8">Add Option</Typography>
              </Box>
            </Box>
          )}

          {fieldType?.skelton?.input_type === 'radio' && (
            <Box>
              {radioElements?.map((radio: any, index: number) => {
                const key = `${index}-${index * 3}-radio-key`;
                return (
                  <Box key={key}>
                    <TextField
                      id="input-with-icon-textfield"
                      value={radio?.label}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        handleOptionLabelChange(e, 'radio', index)
                      }
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Radio
                              checked={radio?.status || false}
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => updateOptionStatus(e, 'radio', index)}
                              sx={{
                                padding: 0,
                                '& .MuiSvgIcon-root': {
                                  fontSize: 24
                                }
                              }}
                            />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="start">
                            {isQuizForm && (
                              <>
                                <IconButton
                                  onClick={() => {
                                    setOpenDialogInOptions(true);
                                    setDialogIndex(index);
                                  }}
                                >
                                  <Icon
                                    name="Image"
                                    sx={{ color: 'black' }}
                                    fontSize="medium"
                                  />
                                </IconButton>

                                <Dialog
                                  open={openDialogInOptions}
                                  onClose={() => setOpenDialogInOptions(false)}
                                  aria-labelledby="alert-dialog-title"
                                  aria-describedby="alert-dialog-description"
                                >
                                  <DialogContent className="bg-white p-20">
                                    <Box className="bg-FAF9F8 d-flex flex-column p-60">
                                      <Button
                                        onClick={() =>
                                          insertImage('option', dialogIndex)
                                        }
                                        className="font-size-18 p-10-20"
                                      >
                                        Insert Image
                                      </Button>
                                    </Box>
                                  </DialogContent>
                                </Dialog>
                              </>
                            )}
                            {radioElements.length > 1 && (
                              <Tooltip title="Delete Option" arrow>
                                <IconButton
                                  onClick={() => deleteElement('radio', index)}
                                >
                                  <Close />
                                </IconButton>
                              </Tooltip>
                            )}
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      className="w-full h-50"
                      sx={{
                        '& input': {
                          borderBottomWidth: 0
                        }
                      }}
                    />
                    {radio?.url_type === 'image' && (
                      <Box>
                        <img
                          src={radio?.url}
                          alt="Label related"
                          style={{
                            marginLeft: '8px',
                            width: '260px',
                            height: '180px'
                          }}
                        />
                        <IconButton
                          onClick={() =>
                            deleteMedia('option', fieldLabelId, index)
                          }
                        >
                          <Icon name="Delete" />
                        </IconButton>
                      </Box>
                    )}
                    {radio?.url_type === 'video' && (
                      <Box>
                        <iframe
                          width="260"
                          height="180"
                          src={radio?.url}
                          frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                          style={{ marginLeft: '8px' }}
                          title="Label related video"
                        />
                        <IconButton
                          onClick={() =>
                            deleteMedia('option', fieldLabelId, index)
                          }
                        >
                          <Icon name="Delete" />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                );
              })}

              <Box
                onClick={() => addElement('radio')}
                className="d-flex align-items-center pb-4 cursor-pointer"
                sx={{
                  borderBottom: '1px solid rgba(0,0,0,0.42)'
                }}
              >
                <Icon name="Circle" sx={{ color: 'rgba(0,0,0,0.5)' }} />
                <Typography
                  sx={{
                    paddingLeft: '8px'
                  }}
                >
                  Add Option
                </Typography>
              </Box>
            </Box>
            // <Options type='radio' elements={radioElements} field={field} fieldLabelId={fieldLabelId} fieldType={fieldType} />
          )}

          {fieldType?.skelton?.input_type === 'checkbox' && (
            <Box>
              {checkboxElements?.map((checkbox: any, index: number) => {
                const key = `${index}-${index * 3}-checkbox-key`;
                return (
                  <Box
                    key={key}
                    display="flex"
                    alignItems="center"
                    gap={1}
                    sx={{ width: '100%' }}
                  >
                    <TextField
                      id="input-with-icon-textfield"
                      value={checkbox?.label}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        handleOptionLabelChange(e, 'checkbox', index)
                      }
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            {/* <Icon name="CheckBoxOutlineBlankOutlined" /> */}
                            <Checkbox
                              checked={checkbox?.status || false}
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => updateOptionStatus(e, 'checkbox', index)}
                              sx={{
                                padding: 0,
                                '& .MuiSvgIcon-root': {
                                  fontSize: 24
                                }
                              }}
                            />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="start">
                            {isQuizForm && (
                              <>
                                <IconButton
                                  onClick={() => setOpenDialogInOptions(true)}
                                >
                                  <Icon
                                    name="Image"
                                    sx={{ color: 'black' }}
                                    fontSize="medium"
                                  />
                                </IconButton>
                                <Dialog
                                  open={openDialogInOptions}
                                  onClose={() => setOpenDialogInOptions(false)}
                                  aria-labelledby="alert-dialog-title"
                                  aria-describedby="alert-dialog-description"
                                >
                                  <DialogContent className="bg-white p-20">
                                    <Box className="bg-FAF9F8 p-60 d-flex flex-column">
                                      <Button
                                        onClick={() =>
                                          insertImage('option', index)
                                        }
                                        className="font-size-18 p-10-20"
                                      >
                                        Insert Image
                                      </Button>
                                    </Box>
                                  </DialogContent>
                                </Dialog>
                              </>
                            )}
                            {checkboxElements?.length > 1 && (
                              <Tooltip title="Delete Option" arrow>
                                <IconButton
                                  onClick={() =>
                                    deleteElement('checkbox', index)
                                  }
                                >
                                  <Close
                                    sx={{
                                      cursor: 'pointer'
                                    }}
                                  />
                                </IconButton>
                              </Tooltip>
                            )}
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      className="w-full h-50"
                      sx={{
                        '& input': {
                          borderBottomWidth: 0
                        }
                      }}
                    />
                    {checkbox?.url_type === 'image' && (
                      <Box>
                        <img
                          src={checkbox?.url}
                          alt="Label related"
                          style={{
                            marginLeft: '8px',
                            width: '260px',
                            height: '180px'
                          }}
                        />
                        <IconButton
                          onClick={() =>
                            deleteMedia('option', fieldLabelId, index)
                          }
                        >
                          <Icon name="Delete" />
                        </IconButton>
                      </Box>
                    )}
                    {checkbox?.url_type === 'video' && (
                      <Box>
                        <iframe
                          width="260"
                          height="180"
                          src={checkbox?.url}
                          frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                          style={{ marginLeft: '8px' }}
                          title="Label related video"
                        />
                        <IconButton
                          onClick={() =>
                            deleteMedia('option', fieldLabelId, index)
                          }
                        >
                          <Icon name="Delete" />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                );
              })}

              <Box
                onClick={() => addElement('checkbox')}
                className="d-flex align-items-center pb-4 cursor-pointer"
                sx={{
                  borderBottom: '1px solid rgba(0,0,0,0.42)'
                }}
              >
                <Icon
                  name="CheckBoxOutlineBlankOutlined"
                  sx={{ color: 'rgba(0,0,0,0.5)' }}
                />
                <Typography className="pl-8">Add Option</Typography>
              </Box>
            </Box>
          )}

          {fieldType?.skelton?.type === 'toggle' && (
            <Box>
              {toggleElements.map((toggle: any, index: number) => {
                const key = `${index}-${index * 3}-toggle-key`;
                return (
                  <Box key={key}>
                    <TextField
                      id="input-with-icon-textfield"
                      defaultValue={toggle?.label}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        handleOptionLabelChange(e, 'toggle', index)
                      }
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Radio
                              checked={toggle?.status || false}
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => updateOptionStatus(e, 'toggle', index)}
                              sx={{
                                padding: 0,
                                '& .MuiSvgIcon-root': {
                                  fontSize: 24
                                }
                              }}
                            />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="start">
                            {/* <Close /> */}
                          </InputAdornment>
                        )
                      }}
                      variant="standard"
                      className="w-full h-50"
                    />
                  </Box>
                );
              })}
            </Box>
          )}

          {fieldType?.skelton?.input_type === 'state' && (
            <Box>
              {!isCountryFieldInSection() && (
                <Select
                  value={selectedCountry || 'default'}
                  onChange={handleCountrySelect}
                  placeholder="Select Country"
                  className="w-full"
                >
                  <MenuItem value="" disabled>
                    Select Default Country
                  </MenuItem>
                  {countriesData.map((country: any) => (
                    <MenuItem key={country?.value} value={country?.value}>
                      {country?.label}
                    </MenuItem>
                  ))}
                </Select>
              )}
            </Box>
          )}

          {fieldType?.skelton?.input_type === 'city' && (
            <Box className="d-flex flex-column gap-10">
              {!isCountryFieldInSection() && !isStateFieldInSection() && (
                <Select
                  value={selectedCountry || 'default'}
                  onChange={handleCountrySelect}
                  placeholder="Select Country"
                  className="w-full"
                >
                  <MenuItem value="" disabled>
                    Select Default Country
                  </MenuItem>
                  {countriesData?.map((country: any) => (
                    <MenuItem key={country?.value} value={country?.value}>
                      {country?.label}
                    </MenuItem>
                  ))}
                </Select>
              )}
              {!isStateFieldInSection() && (
                <Select
                  value={selectedState || 'default'}
                  onChange={handleStateSelect}
                  placeholder="Select State"
                  className="w-full"
                >
                  <MenuItem value="" disabled>
                    Select Default State
                  </MenuItem>
                  {statesData?.map((state: any) => (
                    <MenuItem key={state?.value} value={state?.value}>
                      {state?.label}
                    </MenuItem>
                  ))}
                </Select>
              )}
              {/* <FormSelect
                name={''}
                label={''}
                placeholder="Select State"
                description={field?.description_status ? field?.description : ''}
                data={statesData}
                containerStyles={{
                  width: "100%",
                  color: "#000000",
                }}
                onChange={handleStateSelect}
              /> */}
            </Box>
          )}

          {fieldType?.skelton?.input_type === 'currency' && (
            <div>
              <p>
                Select Currency Type <span style={{ color: 'red' }}>*</span>
              </p>
              <Autocomplete
                multiple
                options={currencyOptions}
                getOptionLabel={(option) =>
                  `${option.name} | ${option.code} | ${option.symbol}`
                }
                value={Array.isArray(currency) ? currency : []}
                onChange={handleCurrency}
                isOptionEqualToValue={(option, value) =>
                  option.code === value.code
                }
                renderTags={(value: CurrencyOption[], getTagProps) =>
                  value.map((option: CurrencyOption, index: number) => (
                    <Chip
                      {...getTagProps({ index })}
                      label={`${option.name} | ${option.code} | ${option.symbol}`}
                      key={`${index + 1}`}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Choose currencies..."
                    error={!currency || currency.length === 0}
                    helperText={
                      !currency || currency.length === 0
                        ? 'At least one currency is required'
                        : ''
                    }
                  />
                )}
              />
            </div>
          )}

          {(field?.original_field_id ===
            'ad014e76-af87-4aca-8514-46b278cff9f0' ||
            '605644f6-1c18-4d57-86dd-a3e6b9018030') &&
            fieldType?.skelton?.type === 'paragraph' && (
              <Box>
                <ReactQuill
                  theme="snow"
                  value={paragraphText}
                  onChange={handlePragraphText}
                />
              </Box>
            )}
        </Box>

        {/* {showCustomValidations && <CustomValidations selectedGroupData={selectedGroupData} />} */}
        {/* Conditional Input */}
        <Dialog
          open={showFormControl}
          onClose={handleConditionalInputClick}
          sx={{ padding: '20px 40px' }}
        >
          <DialogTitle
            sx={{
              textAlign: 'center',
              fontWeight: '600',
              color: '#4382cb',
              padding: '20px 0px'
            }}
          >
            Conditional Input
          </DialogTitle>
          <Box sx={{ padding: '20px 40px', background: '#ffffff' }}>
            <Box
              display="flex"
              flexDirection="column"
              gap={3}
              sx={{
                padding: '20px',
                background: '#faf9f8',
                borderRadius: '10px'
              }}
            >
              <Box flex={1}>
                <FormControl sx={{ width: '340px' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Select Field
                  </Typography>
                  <Select
                    value={selectedValue}
                    onChange={handleSelect}
                    displayEmpty
                    sx={{ background: '#ffffff' }}
                  >
                    <MenuItem value="">
                      <em>Select</em>
                    </MenuItem>
                    {conditionalFields?.map((fieldData: any) => (
                      <MenuItem key={fieldData?.value} value={fieldData?.value}>
                        {fieldData?.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <Box flex={1}>
                <FormControl sx={{ width: '340px' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Select Option
                  </Typography>
                  <Select
                    onChange={handleFieldOptionSelect}
                    displayEmpty
                    sx={{ background: '#ffffff' }}
                  >
                    <MenuItem value="">
                      <em>Select</em>
                    </MenuItem>
                    {selectedFieldOptions?.map((fieldData: any) => (
                      <MenuItem key={fieldData?.value} value={fieldData?.value}>
                        {fieldData?.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', mt: 2, justifyContent: 'center' }}>
            <Button
              variant="contained"
              onClick={handleConditionalInputClick}
              sx={{
                mr: 1,
                backgroundColor: 'white2.main',
                color: '#24242480'
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleConditionalInputSaveButton}
              sx={{
                backgroundColor: 'primaryBlue.main',
                color: 'white2.main'
              }}
            >
              Save
            </Button>
          </Box>
          <DialogContent />
        </Dialog>

        {isQuizForm && fieldType?.skelton?.input_type === 'checkbox' && (
          <Box>
            <Box className="d-flex align-items-center gap-10 ">
              <Typography>Select Total Options :</Typography>
              <FormSelect
                name={fieldCheckboxAnswerType}
                onChange={handleInputChange}
                label=""
                data={checkBoxFieldData}
                className="d-flex align-items-center mt-10 "
              />
              {field?.checkbox_answer_type !== 'No limit' &&
                field?.checkbox_answer_type !== '' && (
                  <FormSelect
                    name={fieldCheckboxAnswerLimit}
                    onChange={handleInputChange}
                    label=""
                    data={Array.from(
                      { length: checkboxElements.length },
                      (_, index) => ({
                        label: `${index + 1}`,
                        value: index + 1
                      })
                    )}
                    className="d-flex align-items-center mt-10 "
                  />
                )}
            </Box>
          </Box>
        )}
        {field?.description_status &&
          fieldType?.skelton?.type !== 'paragraph' && (
            <Box>
              <ReactQuill
                theme="snow"
                value={paragraphText}
                onChange={handlePragraphText}
              />
            </Box>
          )}

        {showAutoFill && (
          <Modal open={showAutoFill} onClose={() => setShowAutoFill(false)}>
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                boxShadow: 24,
                p: 4,
                borderRadius: '10px',
                overflow: 'auto',
                backgroundColor: '#FFFFFF'
              }}
            >
              <AutoFill />
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: 2,
                  marginTop: '20px'
                }}
              >
                <Button
                  onClick={() => setShowAutoFill(false)} // Close modal on cancel
                  sx={{
                    backgroundColor: 'white2.main',
                    color: 'primaryBlue.main',
                    padding: '10px 30px',
                    boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                    textTransform: 'capitalize'
                  }}
                >
                  CANCEL
                </Button>
                <Button
                  type="submit"
                  sx={{
                    backgroundColor: 'primaryBlue.main',
                    color: 'white2.main',
                    padding: '10px 35px',
                    boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                    '&:hover': {
                      color: 'white2.main',
                      backgroundColor: '#08366b',
                      boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                    }
                  }}
                >
                  SAVE
                </Button>
              </Box>
            </Box>
          </Modal>
        )}

        {showCustomValidations && (
          <Modal
            open={showCustomValidations}
            onClose={() => setShowCustomValidations(false)}
          >
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                boxShadow: 24,
                p: 4,
                borderRadius: '10px',
                overflow: 'auto',
                backgroundColor: '#FFFFFF'
              }}
            >
              <CustomValidations lastUpdatedField={lastUpdatedField} />
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: 2,
                  marginTop: '20px'
                }}
              >
                <Button
                  onClick={() => setShowCustomValidations(false)} // Close modal on cancel
                  sx={{
                    backgroundColor: 'white2.main',
                    color: 'primaryBlue.main',
                    padding: '10px 30px',
                    boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                    textTransform: 'capitalize'
                  }}
                >
                  CANCEL
                </Button>
                <Button
                  type="submit"
                  sx={{
                    backgroundColor: 'primaryBlue.main',
                    color: 'white2.main',
                    padding: '10px 35px',
                    boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                    '&:hover': {
                      color: 'white2.main',
                      backgroundColor: '#08366b',
                      boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                    }
                  }}
                >
                  SAVE
                </Button>
              </Box>
            </Box>
          </Modal>
        )}

        <Divider sx={{ padding: '5px' }} />
        <Box className="d-flex align-items-center content-end">
          {isQuizForm && (
            <Box className="d-flex align-items-center flex-1">
              {isQuizForm && (
                <Box className="w-full d-flex align-items-center gap-10 flex-1 min-width-150">
                  <Typography>Points :</Typography>
                  <FormInput
                    name={fieldpoints}
                    label=""
                    type="number"
                    inputMode="numeric"
                    handleInputChange={handleInputChange}
                    containerStyles={{
                      margin: '0px',
                      height: '45px',
                      minWidth: '100px',
                      '& .MuiInputBase-formControl': {
                        marginTop: '0px'
                      },
                      '& input': {
                        marginTop: '0px',
                        height: '45px'
                      }
                    }}
                  />
                </Box>
              )}
            </Box>
          )}
          {showRepeatField &&
            lastUpdatedField?.is_iterative_or_not === true && (
              <Box className="w-full">
                <FormInput
                  name={fieldMaxIterationLength}
                  label=""
                  type="number"
                  inputMode="numeric"
                  placeholder="Enter Max Iteration Length"
                  defaultValue={field.iteration_max_length || 2}
                  handleInputChange={handleInputChange}
                  containerStyles={{
                    margin: '0px',
                    height: '45px',
                    '& .MuiInputBase-formControl': {
                      marginTop: '0px'
                    },
                    '& input': {
                      marginTop: '0px',
                      height: '45px'
                    }
                  }}
                />
              </Box>
            )}
          {showRepeatField && (
            <Box className="d-flex align-items-center">
              <FormControlLabel
                name={fieldIteration}
                control={<Switch defaultChecked={field?.is_iterative_or_not} />}
                onChange={handleInputChange}
                label="Repeat Field"
                labelPlacement="start"
              />
            </Box>
          )}

          {isQuizForm && (
            <Box>
              <FormControlLabel
                control={
                  <AntSwitch
                    checked={fieldType?.skelton?.input_type === 'checkbox'}
                    inputProps={{ 'aria-label': 'ant design' }}
                    sx={{ marginLeft: '6px' }}
                    onChange={updateMultipleAnswer}
                  />
                }
                label="Multiple Answers:"
                labelPlacement="start"
              />
            </Box>
          )}

          {fieldType?.skelton?.type !== 'toggle' &&
            fieldType?.skelton?.type !== 'paragraph' && (
              <Box>
                <FormControlLabel
                  name={fieldrequired}
                  control={
                    <AntSwitch
                      defaultChecked={field?.validation_schema?.required}
                      inputProps={{ 'aria-label': 'ant design' }}
                      sx={{ marginLeft: '6px' }}
                      onChange={handleInputChange}
                    />
                  }
                  label="Required"
                  labelPlacement="start"
                />
              </Box>
            )}

          <Divider
            orientation="vertical"
            variant="middle"
            flexItem
            sx={{ padding: '15px 10px' }}
          />
          <Tooltip title="Copy/Duplicate Field" arrow>
            <IconButton onClick={addDuplicateField}>
              <Icon
                name="FileCopyOutlined"
                fontSize="medium"
                sx={{ color: '#36C0ED' }}
              />
            </IconButton>
          </Tooltip>

          {values.groups[sectionIndex].fields.length > 1 && (
            <Tooltip title="Delete Field" arrow>
              <IconButton onClick={() => deleteField(fieldLabelId)}>
                <Icon
                  name="DeleteOutline"
                  fontSize="medium"
                  sx={{ color: '#36C0ED' }}
                />
              </IconButton>
            </Tooltip>
          )}
          <Box>
            <IconButton onClick={handleClick}>
              <Icon name="MoreVert" sx={{ cursor: 'pointer' }} />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              {fieldType?.skelton?.type !== 'paragraph' && (
                <MenuItem
                  // onClick={() => {
                  //   setShowDescription(!showDescription);
                  //   handleClose();
                  // }}
                  onClick={handleDecriptionStatus}
                >
                  <ListItemText primary="Description" />

                  <ListItemIcon>
                    {field?.description_status && (
                      <Box
                        className="flex-center border-radius-50 h-w-24 ml-auto"
                        sx={{
                          backgroundColor: 'rgba(76, 175, 80, 0.2)'
                        }}
                      >
                        <Icon
                          name="Check"
                          fontSize="small"
                          sx={{ color: '#4caf50' }}
                        />
                      </Box>
                    )}
                  </ListItemIcon>
                </MenuItem>
              )}

              {/* {!isQuizForm && (
                <MenuItem
                  onClick={() => {
                    handleConditionalInputClick();
                    getFields();
                  }}
                >
                  <ListItemText primary="Conditional Input" />

                  <ListItemIcon>
                    {showFormControl && (
                      <Box
                        className="flex-center border-radius-50 h-w-24 ml-auto"
                        sx={{
                          backgroundColor: 'rgba(76, 175, 80, 0.2)'
                        }}
                      >
                        <Icon
                          name="Check"
                          fontSize="small"
                          sx={{ color: '#4caf50' }}
                        />
                      </Box>
                    )}
                  </ListItemIcon>
                </MenuItem>
              )} */}

              {/* {!isQuizForm && (
                <MenuItem
                  onClick={() => {
                    setShowCustomValidations(!showCustomValidations);
                    handleClose();
                  }}
                >
                  <ListItemText primary="Custom Validations" />
                  <ListItemIcon>
                    {showCustomValidations && (
                      <Box
                        className="flex-center border-radius-50 h-w-24 ml-auto"
                        sx={{
                          backgroundColor: 'rgba(76, 175, 80, 0.2)'
                        }}
                      >
                        <Icon
                          name="Check"
                          fontSize="small"
                          sx={{ color: '#4caf50' }}
                        />
                      </Box>
                    )}
                  </ListItemIcon>
                </MenuItem>
              )} */}

              {/* {!isQuizForm && (
                <MenuItem
                  onClick={() => {
                    setShowAutoFill(!showAutoFill);
                    handleClose();
                  }}
                >
                  <ListItemText primary="Auto Fill" />
                  <ListItemIcon>
                    {showAutoFill && (
                      <Box
                        className="flex-center border-radius-50 h-w-24 ml-auto"
                        sx={{
                          backgroundColor: 'rgba(76, 175, 80, 0.2)'
                        }}
                      >
                        <Icon
                          name="Check"
                          fontSize="small"
                          sx={{ color: '#4caf50' }}
                        />
                      </Box>
                    )}
                  </ListItemIcon>
                </MenuItem>
              )} */}

              <MenuItem
                onClick={() => {
                  setShowRepeatField(!showRepeatField);
                  handleClose();
                }}
              >
                <ListItemText primary="Repeat Field" />
                <ListItemIcon>
                  {showRepeatField && (
                    <Box
                      className="flex-center border-radius-50 h-w-24 ml-auto"
                      sx={{
                        backgroundColor: 'rgba(76, 175, 80, 0.2)'
                      }}
                    >
                      <Icon
                        name="Check"
                        fontSize="small"
                        sx={{ color: '#4caf50' }}
                      />
                    </Box>
                  )}
                </ListItemIcon>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>

      <Box className="justify-content-end mt-10 mb-20 h-50 w-full d-flex position-absolute float-right">
        <Box
          className="d-flex align-items-center gap-6 radius-4 p-3"
          sx={{
            boxShadow: '0px 2px 4px 0px rgba(0,0,0,0.3)',
            width: 'fit-content'
          }}
        >
          <LoadingButton
            loading={fieldSpinner}
            startIcon={<Icon name="AddCircleOutline" color="primary" />}
            onClick={addField}
            className="h-45 color-616161 p-0-20"
            sx={{
              '&:hover': {
                backgroundColor: '#FFF'
              }
            }}
          >
            Add Input
          </LoadingButton>
          <Divider
            orientation="vertical"
            flexItem
            sx={{
              border: '1px solid #616161',
              height: '28px',
              width: '2px',
              marginTop: '6px'
            }}
          />
          <LoadingButton
            loading={sectionSpinner}
            startIcon={<CalendarViewDayOutlinedIcon color="primary" />}
            onClick={addSection}
            className="color-616161 h-45 p-0-20"
            sx={{
              '&:hover': {
                backgroundColor: '#FFF'
              }
            }}
          >
            Add Section
          </LoadingButton>
        </Box>
      </Box>
    </Box>
  );
};
export default ActiveField;
