import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Input,
  InputBase,
  Paper,
  Typography,
  useMediaQuery,
  useTheme,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { SubMenu } from '../form.elements';

const AppList: React.FC<{ appData: any; id: string }> = ({ appData, id }) => {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [openQuizDialog, setOpenQuizDialog] = useState(false);
  const [openFormDialog, setOpenFormDialog] = useState(false);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const userType = localStorage.getItem('user_type');

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setOpenQuizDialog(false);
    setOpenFormDialog(false);
  };

  const handleQuizDialogOpen = () => {
    setOpen(false);
    setOpenQuizDialog(true);
  };

  const handleFormDialogOpen = () => {
    setOpen(false);
    setOpenFormDialog(true);
  };

  return (
    <>
      <SubMenu
        search
        backNavigation
        roundedButton={{
          status: true,
          title: 'Create New App',
          icon: 'AddCircleOutline',
          redirectUrl: '/apps/app-registration'
        }}
        roundedButtonStyles={{
          height: '45px'
        }}
        searchStyles={{
          width: '600px',
          height: '45px'
        }}
      />

      <Box sx={{ background: '#EBEBEB', padding: '0px 100px 200px 100px' }}>
        <Box
          sx={{
            padding: '60px',
            background: '#ffffff'
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              marginTop: '50px'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <Typography
                sx={{
                  fontSize: '24px',
                  color: '#595959'
                }}
              >
                Forms List
              </Typography>
              <Typography
                sx={{
                  width: '32px',
                  height: '32px',
                  color: '#616161',
                  background: '#EBEBEB',
                  borderRadius: '50%',
                  fontSize: '18px',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {appData.forms.length * 3}
              </Typography>
              <Paper
                elevation={0}
                component="form"
                sx={{
                  p: '2px 4px',
                  display: 'flex',
                  width: 600,
                  borderRadius: '50px',
                  background: '#FAF9F8'
                }}
              >
                <IconButton
                  type="button"
                  sx={{ p: '10px', color: '#0483BA' }}
                  aria-label="search"
                >
                  <SearchIcon />
                </IconButton>
                <Divider sx={{ height: 44 }} orientation="vertical" />
                <InputBase
                  sx={{ ml: 1, flex: 1 }}
                  placeholder=""
                  inputProps={{ 'aria-label': '.....' }}
                />
                <Divider sx={{ height: 44 }} orientation="vertical" />
                <IconButton
                  sx={{ p: '10px', color: '#0483BA' }}
                  aria-label="menu"
                >
                  <FilterListIcon sx={{ color: '#0483BA' }} />
                </IconButton>
              </Paper>
            </Box>
            <Box sx={{ padding: '0px' }}>
              <Button
                size="medium"
                sx={{
                  p: '10px 40px',
                  border: '2px solid',
                  backgroundColor: '#08366B',
                  color: 'white',
                  '&:hover': { backgroundColor: '#08363B' },
                  borderRadius: '50px',
                  padding: '10px 30px'
                }}
                onClick={handleClickOpen}
              >
                <AddCircleOutlineIcon
                  fontSize="small"
                  sx={{ color: '#F9F9F9', margin: '0px 5px' }}
                />
                Create
              </Button>
              <Dialog
                fullScreen={fullScreen}
                open={open}
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                sx={{
                  '& .MuiDialog-paper': {
                    borderRadius: '20px',
                    padding: '20px',
                    backgroundImage:
                      'linear-gradient(to right, #ece9e6, #ffffff)'
                  }
                }}
              >
                <DialogTitle
                  id="customized-dialog-title"
                  sx={{
                    fontSize: '24px',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    color: '#08366B'
                  }}
                >
                  Create New Item
                </DialogTitle>
                <DialogContent dividers>
                  <DialogContentText
                    sx={{
                      fontSize: '18px',
                      textAlign: 'center',
                      color: '#616161',
                      marginBottom: '20px'
                    }}
                  >
                    Select whether to create a new Quiz or Form.
                  </DialogContentText>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      gap: '20px'
                    }}
                  >
                    <Button
                      onClick={handleQuizDialogOpen}
                      sx={{
                        color: '#fff',
                        backgroundColor: '#0483BA',
                        '&:hover': {
                          backgroundColor: '#036a9d'
                        },
                        borderRadius: '50px',
                        padding: '10px 30px'
                      }}
                    >
                      New Quiz
                    </Button>
                    <Button
                      onClick={handleFormDialogOpen}
                      sx={{
                        color: '#fff',
                        backgroundColor: '#08366B',
                        '&:hover': {
                          backgroundColor: '#064078'
                        },
                        borderRadius: '50px',
                        padding: '10px 30px'
                      }}
                    >
                      New Form
                    </Button>
                  </Box>
                </DialogContent>
                <DialogActions sx={{ justifyContent: 'center' }}>
                  <Button
                    onClick={handleClose}
                    sx={{
                      color: '#fff',
                      backgroundColor: '#FF000090',
                      '&:hover': {
                        backgroundColor: '#FF000060'
                      },
                      borderRadius: '50px',
                      padding: '10px 30px'
                    }}
                  >
                    Cancel
                  </Button>
                </DialogActions>
              </Dialog>

              <Dialog
                fullScreen={fullScreen}
                open={openQuizDialog}
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                sx={{
                  '& .MuiDialog-paper': {
                    borderRadius: '20px',
                    padding: '20px',
                    backgroundImage:
                      'linear-gradient(to right, #ece9e6, #ffffff)'
                  }
                }}
              >
                <DialogTitle
                  id="customized-dialog-title"
                  sx={{
                    fontSize: '24px',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    color: '#08366B'
                  }}
                >
                  Create New Quiz
                </DialogTitle>
                <DialogContent dividers>
                  <DialogContentText
                    sx={{
                      fontSize: '18px',
                      textAlign: 'center',
                      color: '#616161',
                      marginBottom: '20px'
                    }}
                  >
                    Fill out the form details below to create a new quiz.
                  </DialogContentText>
                  <Input sx={{ width: '100%' }} />
                </DialogContent>
                <DialogActions sx={{ justifyContent: 'center' }}>
                  <Button
                    onClick={handleClose}
                    sx={{
                      color: '#fff',
                      backgroundColor: '#08366B',
                      '&:hover': {
                        backgroundColor: '#064078'
                      },
                      borderRadius: '50px',
                      padding: '10px 30px'
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleClose}
                    sx={{
                      color: '#fff',
                      backgroundColor: '#0483BA',
                      '&:hover': {
                        backgroundColor: '#036a9d'
                      },
                      borderRadius: '50px',
                      padding: '10px 30px'
                    }}
                  >
                    Save
                  </Button>
                </DialogActions>
              </Dialog>

              <Dialog
                fullScreen={fullScreen}
                open={openFormDialog}
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                sx={{
                  '& .MuiDialog-paper': {
                    borderRadius: '20px',
                    padding: '20px',
                    backgroundImage:
                      'linear-gradient(to right, #ece9e6, #ffffff)'
                  }
                }}
              >
                <DialogTitle
                  id="customized-dialog-title"
                  sx={{
                    fontSize: '24px',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    color: '#08366B'
                  }}
                >
                  Create New Form
                </DialogTitle>
                <DialogContent dividers>
                  <DialogContentText
                    sx={{
                      fontSize: '18px',
                      textAlign: 'center',
                      color: '#616161',
                      marginBottom: '20px'
                    }}
                  >
                    Fill out the form details below to create a new form.
                  </DialogContentText>
                  <Input sx={{ width: '100%' }} />
                  <FormControlLabel control={<Checkbox />} label="Sub Form" />
                </DialogContent>
                <DialogActions sx={{ justifyContent: 'center' }}>
                  <Button
                    onClick={handleClose}
                    sx={{
                      color: '#fff',
                      backgroundColor: '#08366B',
                      '&:hover': {
                        backgroundColor: '#064078'
                      },
                      borderRadius: '50px',
                      padding: '10px 30px'
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleClose}
                    sx={{
                      color: '#fff',
                      backgroundColor: '#0483BA',
                      '&:hover': {
                        backgroundColor: '#036a9d'
                      },
                      borderRadius: '50px',
                      padding: '10px 30px'
                    }}
                  >
                    Save
                  </Button>
                </DialogActions>
              </Dialog>
            </Box>
          </Box>

          <Box sx={{ paddingTop: '40px' }}>
            {userType === 'organization' ? (
              <>
                <Accordion>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      background: '#f9f9f9',
                      borderRadius: '5px'
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: '21px',
                        fontWeight: '500'
                      }}
                    >
                      Care App_Forms {appData?.forms?.length}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid
                      container
                      rowSpacing={3}
                      columnSpacing={4}
                      sx={{
                        background: '#f9f9f9',
                        padding: '20px 40px'
                      }}
                    >
                      {appData?.forms?.map((form: any, index: number) => {
                        const words = form.name.split(' ');
                        const letters = words
                          .map((word: string) => word.charAt(0))
                          .join('')
                          .slice(0, 3);
                        const key = `${index}-${index * 2}-form-key`;
                        return (
                          <Grid item xs={4} key={key}>
                            <Box
                              sx={{
                                width: 'auto',
                                height: 'auto',
                                color: '#616161',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '20px',
                                cursor: 'pointer'
                              }}
                              onClick={() => {
                                navigate(
                                  `/apps/form-builder/edit-form/${form.form_id}`
                                );
                                localStorage.setItem('app_id', id);
                              }}
                            >
                              <Box
                                sx={{
                                  width: '44px',
                                  height: '44px',
                                  background: '#ffffff',
                                  color: '#616161',
                                  borderRadius: '50%',
                                  fontSize: '24px',
                                  fontWeight: '500',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  boxShadow: '2px 2px 5px 0px'
                                }}
                              >
                                <Typography>{letters.toUpperCase()}</Typography>
                              </Box>
                              <Typography
                                sx={{
                                  width: 'auto',
                                  color: '#616161',
                                  fontSize: '18px',
                                  fontWeight: '500'
                                }}
                              >
                                {form.name}
                              </Typography>
                            </Box>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
                <Accordion>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      background: '#f9f9f9',
                      borderRadius: '5px'
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: '21px',
                        fontWeight: '500'
                      }}
                    >
                      Modified_Forms {appData?.forms?.length}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid
                      container
                      rowSpacing={3}
                      columnSpacing={4}
                      sx={{
                        background: '#f9f9f9',
                        padding: '20px 40px'
                      }}
                    >
                      {appData?.forms?.map((form: any, index: number) => {
                        const words = form.name.split(' ');
                        const letters = words
                          .map((word: string) => word.charAt(0))
                          .join('')
                          .slice(0, 3);
                        const key = `${index}-${index * 2}-form-key`;
                        return (
                          <Grid item xs={4} key={key}>
                            <Box
                              sx={{
                                width: 'auto',
                                height: 'auto',
                                color: '#616161',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '20px',
                                cursor: 'pointer'
                              }}
                              onClick={() => {
                                navigate(
                                  `/apps/form-builder/edit-form/${form.form_id}`
                                );
                                localStorage.setItem('app_id', id);
                              }}
                            >
                              <Box
                                sx={{
                                  width: '44px',
                                  height: '44px',
                                  background: '#ffffff',
                                  color: '#616161',
                                  borderRadius: '50%',
                                  fontSize: '24px',
                                  fontWeight: '500',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  boxShadow: '2px 2px 5px 0px'
                                }}
                              >
                                <Typography>{letters.toUpperCase()}</Typography>
                              </Box>
                              <Typography
                                sx={{
                                  width: 'auto',
                                  color: '#616161',
                                  fontSize: '18px',
                                  fontWeight: '500'
                                }}
                              >
                                {form.name}
                              </Typography>
                            </Box>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
                <Accordion>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      background: '#f9f9f9',
                      borderRadius: '5px'
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: '21px',
                        fontWeight: '500'
                      }}
                    >
                      My Forms {appData?.forms?.length}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid
                      container
                      rowSpacing={3}
                      columnSpacing={4}
                      sx={{
                        background: '#f9f9f9',
                        padding: '20px 40px'
                      }}
                    >
                      {appData?.forms?.map((form: any, index: number) => {
                        const words = form.name.split(' ');
                        const letters = words
                          .map((word: string) => word.charAt(0))
                          .join('')
                          .slice(0, 3);
                        const key = `${index}-${index * 2}-form-key`;
                        return (
                          <Grid item xs={4} key={key}>
                            <Box
                              sx={{
                                width: 'auto',
                                height: 'auto',
                                color: '#616161',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '20px',
                                cursor: 'pointer'
                              }}
                              onClick={() => {
                                navigate(
                                  `/apps/form-builder/edit-form/${form.form_id}`
                                );
                                localStorage.setItem('app_id', id);
                              }}
                            >
                              <Box
                                sx={{
                                  width: '44px',
                                  height: '44px',
                                  background: '#ffffff',
                                  color: '#616161',
                                  borderRadius: '50%',
                                  fontSize: '24px',
                                  fontWeight: '500',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  boxShadow: '2px 2px 5px 0px'
                                }}
                              >
                                <Typography>{letters.toUpperCase()}</Typography>
                              </Box>
                              <Typography
                                sx={{
                                  width: 'auto',
                                  color: '#616161',
                                  fontSize: '18px',
                                  fontWeight: '500'
                                }}
                              >
                                {form.name}
                              </Typography>
                            </Box>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </>
            ) : (
              <Grid
                container
                rowSpacing={3}
                columnSpacing={4}
                sx={{
                  marginTop: '40px',
                  background: '#f9f9f9',
                  padding: '20px 40px'
                }}
              >
                {appData?.forms?.map((form: any, index: number) => {
                  const words = form.name.split(' ');
                  const letters = words
                    .map((word: string) => word.charAt(0))
                    .join('')
                    .slice(0, 3);
                  const key = `${index}-${index * 2}-form-key`;
                  return (
                    <Grid item xs={4} key={key}>
                      <Box
                        sx={{
                          width: 'auto',
                          height: 'auto',
                          color: '#616161',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '20px',
                          cursor: 'pointer'
                        }}
                        onClick={() => {
                          navigate(
                            `/apps/form-builder/edit-form/${form.form_id}`
                          );
                          localStorage.setItem('app_id', id);
                        }}
                      >
                        <Box
                          sx={{
                            width: '44px',
                            height: '44px',
                            background: '#ffffff',
                            color: '#616161',
                            borderRadius: '50%',
                            fontSize: '24px',
                            fontWeight: '500',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: '2px 2px 5px 0px'
                          }}
                        >
                          <Typography>{letters.toUpperCase()}</Typography>
                        </Box>
                        <Typography
                          sx={{
                            width: 'auto',
                            color: '#616161',
                            fontSize: '18px',
                            fontWeight: '500'
                          }}
                        >
                          {form.name}
                        </Typography>
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
};
export default AppList;
