import { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { FormSelect } from '../form.elements';
import '../../css/auto-fill-styles.scss';
import '../../css/index.scss';

const SelectInputRound = ({
  changeEvent,
  render,
  name,
  value,
  groupedOptions
}: any) => {
  return (
    <div className="custom-selectstyles">
      <select
        id={name}
        className="inputSelectRound-styles form-control override-fc"
        name={name}
        value={value}
        onChange={changeEvent}
      >
        {Object.keys(groupedOptions).map((group, index) => {
          const keyIndex = `key-card${index}-${index * 3}`;
          return (
            <optgroup key={keyIndex} label={group}>
              {render(groupedOptions[group])}
            </optgroup>
          );
        })}
      </select>
    </div>
  );
};

const AutoFill: React.FC = () => {
  const [selectedForm, setSelectedForm] = useState('');
  const [selectedField, setSelectedField] = useState('');
  const [firstField, setFirstField] = useState();

  const handleFormChange = (event: any) => {
    setSelectedForm(event.target.value);
  };

  const handleFieldChange = (event: any) => {
    setSelectedField(event.target.value);
    const { name, value } = event.target;
    if (name === 'first_field') {
      setFirstField(value);
      window.console.log(selectedForm, selectedField);
    }
  };

  const formOptions = [
    { value: 'profileinformation', label: 'Profile Information' },
    { value: 'contactinformation', label: 'Contact Information' },
    { value: 'aggrements', label: 'Aggrements' }
  ];

  const groupedOptions = {
    'Section Name 1': [
      { value: 'fullname', label: 'Full Name' },
      { value: 'email', label: 'Email' },
      { value: 'address', label: 'Address' }
    ],
    'Section Name 2': [
      { value: 'mobileno', label: 'Mobile No' },
      { value: 'alternativemobileno', label: 'Alternative Mobile No' }
    ]
  };

  const renderOptions = (fields: any) => {
    return fields.map((field: any) => (
      <option key={field.value} value={field.value}>
        {field.label}
      </option>
    ));
  };

  return (
    <Box sx={{ marginTop: '10px', maxWidth: '900px' }}>
      <Typography
        sx={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#0483BA',
          marginBottom: '16px',
          textAlign: 'center'
        }}
      >
        Auto Fill/Pre Fill
      </Typography>
      <Box
        sx={{
          marginTop: '30px',
          border: '1px solid #0483BA',
          borderRadius: '10px',
          backgroundColor: '#FAF9F8',
          width: '450px',
          padding: '20px'
        }}
      >
        {/* Container for Select Form */}
        <Box className="w-full mr-2">
          <Typography
            variant="subtitle1"
            color="textSecondary"
            className="font-weight-400 font-size-15 color-808080 pb-8"
          >
            Select Form
          </Typography>
          <FormSelect
            name="form-select"
            data={formOptions}
            onChange={handleFormChange}
            containerStyles={{
              width: '100%',
              height: '45px',
              marginBottom: '16px',
              backgroundColor: '#FFFFFF'
            }}
          />
        </Box>

        {/* Container for Select Field */}
        <Box className="custom-select">
          <Typography
            variant="subtitle2"
            color="textSecondary"
            className="font-weight-400 font-size-15 color-808080 pb-8"
          >
            Select Field
          </Typography>
          <SelectInputRound
            changeEvent={handleFieldChange}
            render={renderOptions}
            name="first_field"
            value={firstField}
            groupedOptions={groupedOptions}
          />
        </Box>
      </Box>
    </Box>
  );
};
export default AutoFill;
