// import React from 'react';
// import { IconButton } from '@mui/material';
// import FormatColorFillIcon from '@mui/icons-material/FormatColorFill';
// import FormatColorTextIcon from '@mui/icons-material/FormatColorText';

// interface ColorPickerButtonProps {
//   currentColor: string;
//   onColorChange: (color: string) => void;
//   isFontColor?: boolean; // Flag to determine which icon to show
// }

// const ColorPickerButton: React.FC<ColorPickerButtonProps> = ({
//   currentColor,
//   onColorChange,
//   isFontColor = false // Default is background color
// }) => {
//   const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     onColorChange(event.target.value);
//   };

//   return (
//     <IconButton component="label">
//       {isFontColor ? (
//         <FormatColorTextIcon style={{ color: currentColor }} /> // Font color icon
//       ) : (
//         <FormatColorFillIcon style={{ color: currentColor }} /> // Background color icon
//       )}
//       <input
//         type="color"
//         value={currentColor}
//         onChange={handleChange}
//         style={{
//           position: 'absolute',
//           width: 0,
//           height: 0,
//           opacity: 0,
//           pointerEvents: 'none'
//         }}
//       />
//     </IconButton>
//   );
// };

// export default ColorPickerButton;

import React from 'react';
import { IconButton, Tooltip, Box } from '@mui/material';
import FormatColorFillIcon from '@mui/icons-material/FormatColorFill';
import FormatColorTextIcon from '@mui/icons-material/FormatColorText';

interface ColorPickerButtonProps {
  currentColor: string;
  onColorChange: (color: string) => void;
  isFontColor?: boolean; // Flag to determine which icon to show
}

const ColorPickerButton: React.FC<ColorPickerButtonProps> = ({
  currentColor,
  onColorChange,
  isFontColor = false // Default is background color
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onColorChange(event.target.value);
  };

  return (
    <Tooltip title={isFontColor ? 'Text color' : 'Background color'}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <IconButton component="label">
          {isFontColor ? (
            <FormatColorTextIcon style={{ color: currentColor }} /> // Font color icon
          ) : (
            <FormatColorFillIcon /> // Background color icon
          )}
          <input
            type="color"
            value={currentColor}
            onChange={handleChange}
            style={{
              position: 'absolute',
              width: 0,
              height: 0,
              opacity: 0,
              pointerEvents: 'none'
            }}
          />
        </IconButton>

        {/* Color Indicator Circle */}
        <Box
          sx={{
            width: 18,
            height: 18,
            borderRadius: '50%',
            backgroundColor: currentColor,
            border: '1px solid #ccc'
          }}
        />
      </Box>
    </Tooltip>
  );
};

export default ColorPickerButton;
