import {
  Badge,
  Box,
  IconButton,
  ListItemText,
  MenuItem,
  MenuList,
  Typography,
  Select
} from '@mui/material';
import { useState } from 'react';

import { useSelector } from 'react-redux';
import { RootState } from '../../redux/reducers';
import { Icon } from './Icon';
import '../../css/index.scss';
import '../../css/conditional-input-styles.scss';

export const ConditionalInput = () => {
  const { formFields } = useSelector((state: RootState) => state.form);
  const [selectedFields, setSelectedFields] = useState<any[]>([]);
  const [fieldComponents, setFieldComponents] = useState<{
    [key: string]: { id: number, value: any, dropdownValue: string }[]
  }>({});

  // Handle dropdown value change for each component
  const handleDropdownChange = (
    field: string,
    id: number,
    newValue: string
  ) => {
    setFieldComponents((prevComponents) => ({
      ...prevComponents,
      [field]: prevComponents[field].map((component) =>
        component.id === id
          ? { ...component, dropdownValue: newValue }
          : component
      )
    }));
  };

  const handleFieldClick = (field: string) => {
    if (!selectedFields.includes(field)) {
      setSelectedFields((prevSelectedFields) => [...prevSelectedFields, field]);
      setFieldComponents((prevComponents) => ({
        ...prevComponents,
        [field]: [{ id: 1, value: '', dropdownValue: 'Text' }]
      }));
    }
  };

  const handleAddComponent = (field: string) => {
    setFieldComponents((prevComponents) => {
      const fieldArray = prevComponents[field];
      const newId = fieldArray.length
        ? fieldArray[fieldArray.length - 1].id + 1
        : 1;
      return {
        ...prevComponents,
        [field]: [
          ...fieldArray,
          { id: newId, value: '', dropdownValue: 'Text' }
        ]
      };
    });
  };

  const handleRemoveComponent = (field: string, id: number) => {
    setFieldComponents((prevComponents) => ({
      ...prevComponents,
      [field]: prevComponents[field].filter((component) => component.id !== id)
    }));
  };

  const handleRemoveField = (field: string) => {
    setSelectedFields((prevSelectedFields) =>
      prevSelectedFields.filter((item) => item !== field)
    );
    setFieldComponents((prevComponents) => {
      const updatedComponents = { ...prevComponents };
      delete updatedComponents[field];
      return updatedComponents;
    });
  };

  const handleInputChange = (field: string, id: number, value: string) => {
    setFieldComponents((prevComponents) => ({
      ...prevComponents,
      [field]: prevComponents[field].map((component) =>
        component.id === id ? { ...component, value } : component
      )
    }));
  };

  return (
    <Box className="d-flex w-full h-full flex-column">
      <Box className="d-flex w-full align-items-center f-1">
        <Box className="w-20">
          <MenuList>
            {['Option 1', 'Option 2', 'Option 3', 'Option 4'].map((field) => (
              <MenuItem
                key={field}
                onClick={() => handleFieldClick(field)}
                disabled={selectedFields.includes(field)}
                className="d-flex justify-content-between"
                sx={{
                  opacity: 1,
                  '&:hover .MuiBadge-badge': {
                    backgroundColor: 'transparent'
                  }
                }}
              >
                <ListItemText>{field}</ListItemText>
                {selectedFields.includes(field) ? (
                  <Badge
                    color="primary"
                    badgeContent={
                      <Icon
                        name="CheckCircle"
                        sx={{
                          color: 'green',
                          fontSize: '20px',
                          transition: 'color 0.3s'
                        }}
                      />
                    }
                    sx={{
                      '.MuiBadge-badge': {
                        width: 'auto',
                        padding: '0 4px',
                        backgroundColor: 'transparent',
                        transition: 'background-color 0.3s'
                      }
                    }}
                  />
                ) : (
                  <Badge
                    color="primary"
                    badgeContent={
                      <Icon
                        name="CheckCircle"
                        sx={{
                          color: '#0483BA',
                          fontSize: '20px',
                          transition: 'color 0.3s'
                        }}
                      />
                    }
                    sx={{
                      '.MuiBadge-badge': {
                        width: 'auto',
                        padding: '0 4px',
                        backgroundColor: 'transparent',
                        transition: 'background-color 0.3s'
                      }
                    }}
                  />
                )}
              </MenuItem>
            ))}
          </MenuList>
        </Box>
      </Box>

      {/* Render selected fields */}
      {selectedFields.map((selectedField) => (
        <Box
          key={selectedField}
          className="d-flex flex-column bg-white gap-2 position-relative pt-16 p-16 border-radius-8"
          sx={{
            border: '1px solid #ccc',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}
        >
          {/* Remove field button */}
          <IconButton
            size="small"
            onClick={() => handleRemoveField(selectedField)}
            className="position-absolute top-8 right-8 color-lightcoral"
          >
            <Icon name="Delete" />
          </IconButton>

          <Typography variant="h6" sx={{ paddingBottom: '5px' }}>
            {selectedField}
          </Typography>

          {/* Display components within the field */}
          {fieldComponents[selectedField].map((component) => (
            <Box
              key={component.id}
              className="d-flex align-items-center gap-2 position-relative mb-8"
            >
              {/* The input field */}
              <Box
                component="input"
                className="w-full p-8 border-1-solid-ccc border-radius-4"
                value={component.value}
                onChange={(e: any) =>
                  handleInputChange(selectedField, component.id, e.target.value)
                }
                placeholder="Text"
              />

              {/* Dropdown beside input */}
              <Select
                value={component.dropdownValue}
                onChange={(e) =>
                  handleDropdownChange(
                    selectedField,
                    component.id,
                    e.target.value
                  )
                }
                sx={{
                  marginLeft: '8px',
                  minWidth: '120px' // Set a fixed width for the dropdown
                }}
              >
                {formFields?.map((field: any) => {
                  const icon = field?.skelton?.icon
                    .split('_')
                    .map(
                      (word: any) =>
                        word.charAt(0).toUpperCase() +
                        word.slice(1).toLowerCase()
                    )
                    .join('');
                  return (
                    <MenuItem key={field.id} value={field.value}>
                      <Icon name={icon} />
                      {field.skelton.name}
                    </MenuItem>
                  );
                })}
              </Select>

              {/* Remove component button */}
              <IconButton
                size="small"
                onClick={() =>
                  handleRemoveComponent(selectedField, component.id)
                }
                sx={{
                  color: 'lightcoral'
                }}
              >
                <Icon name="Remove" />
              </IconButton>

              {/* Add component button */}
              <IconButton
                size="small"
                onClick={() => handleAddComponent(selectedField)}
                sx={{
                  color: 'green'
                }}
              >
                <Icon name="Add" />
              </IconButton>
            </Box>
          ))}
        </Box>
      ))}
    </Box>
  );
};
export default ConditionalInput;
