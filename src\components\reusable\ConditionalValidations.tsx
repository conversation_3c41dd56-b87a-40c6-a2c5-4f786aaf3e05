import { Box, Checkbox, FormControlLabel, FormHelperText } from '@mui/material';
import { useState } from 'react';
import { useFormikContext } from 'formik';

import { useSelector } from 'react-redux';
import { RootState } from '../../redux/reducers';
import { toPascalCase } from '../../utils/functions';
import { FormInput, FormSelect } from '../form.elements';
import '../../css/index.scss';
import '../../css/conditional-validations-styles.scss';

const ConditionalValidations: React.FC = () => {
  const { formFields } = useSelector((state: RootState) => state.form);

  const limitedFields = [
    'DateTime',
    'Phone',
    'Fax',
    'City',
    'State',
    'Country',
    'Text',
    'Large Text',
    'Multiple Choice'
  ];

  const formFieldsData = formFields
    ?.filter((s: any) => limitedFields.includes(s.name))
    .map((s: any) => {
      return {
        label: s.name,
        value: s.field_id,
        icon: s?.skelton?.icon ? toPascalCase(s?.skelton?.icon) : null
      };
    });

  const [isRequired, setIsRequired] = useState(false);
  const [selectedCondition, setSelectedCondition] = useState('');
  const [dob, setDob] = useState('');

  const handleCheckboxChange = (event: any) => {
    setIsRequired(event.target.checked);
  };
  const handleDobChange = (event: any) => {
    setDob(event.target.value);
  };
  const handleConditionChange = (event: any) => {
    setSelectedCondition(event.target.value);
  };

  const [error, setError] = useState('');

  const handleValidation = () => {
    if (dob.trim() === '') {
      setError('Date of birth is required.');
    } else {
      setError('');
    }
  };

  const conditionOptions = [
    { value: 'less_than', label: 'Less Than' },
    { value: 'greater_than', label: 'Greater Than' },
    { value: 'equal', label: 'Equal' }
  ];
  const { values } = useFormikContext<any>();

  return (
    <Box>
      <Box className="d-flex align-items-center">
        <Box>
          <FormControlLabel
            control={
              <Checkbox
                checked={isRequired}
                onChange={handleCheckboxChange}
                name="required"
                color="primary"
              />
            }
            label="Required"
          />
        </Box>

        <Box className="w-400 d-flex align-items-center ml-10 h-45">
          <FormSelect
            name="anyInput"
            data={formFieldsData}
            containerStyles={{
              width: '100%',
              height: '45px',
              marginBottom: '0px',
              position: 'relative',
              top: '0px',
              padding: '0px',
              '& .MuiInputBase-root': {
                height: '45px',
                margin: '0px',
                paddingTop: '0px',
                paddingBottom: '0px',
                '& .MuiSelect-select': {
                  height: '45px',
                  margin: '0px',
                  paddingTop: '0px',
                  paddingBottom: '0px'
                }
              }
            }}
            iconStyles={{
              height: '45px',
              marginRight: '10px',
              fontSize: '24px'
            }}
            menuStyles={{
              height: '45px',
              fontSize: '16px'
            }}
          />
        </Box>

        {values.anyInput === '041cae83-7e48-4dd8-baa6-e3e5848f5f86' && (
          <Box className="w-full ml-10">
            <FormSelect
              name="condition"
              data={conditionOptions}
              value={selectedCondition}
              onChange={handleConditionChange}
              containerStyles={{
                width: '100%',
                height: '45px',
                marginBottom: '0px',
                position: 'relative',
                top: '0px',
                padding: '0px',
                '& .MuiInputBase-root': {
                  height: '45px',
                  margin: '0px',
                  paddingTop: '0px',
                  paddingBottom: '0px',
                  '& .MuiSelect-select': {
                    height: '45px',
                    margin: '0px',
                    paddingTop: '0px',
                    paddingBottom: '0px'
                  }
                }
              }}
              iconStyles={{
                height: '45px',
                marginRight: '10px',
                fontSize: '24px'
              }}
              menuStyles={{
                height: '45px',
                fontSize: '16px'
              }}
            />
          </Box>
        )}
      </Box>
      {values.anyInput === '041cae83-7e48-4dd8-baa6-e3e5848f5f86' && (
        <Box sx={{ width: '100%', marginLeft: '10px' }}>
          <FormInput
            name="dob"
            label=""
            type="date"
            placeholder="Enter Date of Birth"
            value={dob}
            onChange={handleDobChange}
            onBlur={handleValidation}
            fullWidth
            style={{ marginTop: 16 }}
            containerStyles={{
              width: {
                xs: '600px'
              }
            }}
          />
          <FormHelperText
            sx={{
              textTransform: 'capitalize',
              marginLeft: 0,
              marginRight: 0,
              color: 'error.main'
            }}
          >
            {isRequired && error}
          </FormHelperText>
        </Box>
      )}
    </Box>
  );
};
export default ConditionalValidations;
