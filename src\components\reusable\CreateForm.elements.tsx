// Package Imports
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import {
  closestCorners,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import {
  Box,
  FormControlLabel,
  IconButton,
  Menu,
  MenuItem,
  Switch,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'react-toastify';

// Locale Imports
import { RenderGroupTypes } from '../../types';
import { RootState } from '../../redux/reducers';
import { FormInput } from '../form.elements';
import { Icon } from './Icon';
import { AppDispatch } from '../../redux/app.store';
import {
  deletesection,
  duplicatesection,
  loadSpinner,
  refetchform,
  updateColumnIndex,
  updateFieldIndexes,
  updateSectionIndex,
  updateSectionIndexes,
  updateSnackbar
} from '../../redux/reducers/form.reducer';
import { RenderField } from './RenderField';

import '../../css/index.scss';

export const RenderGroup: React.FC<RenderGroupTypes> = ({
  formData,
  group,
  groupsCount,
  groupTitle,
  groupDescription,
  groupIteration,
  groupMaxIterationLength,
  handleInputChange,
  secIndex
}) => {
  const { sectionIndex, formId, activeTheme, loadingSpinner } = useSelector(
    (state: RootState) => state.form
  );
  const [fieldsList, setFieldsList] = useState<any>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const [showRepeatSection, setShowRepeatSection] = useState<any>(
    group.is_iterative_or_not
  );

  const [showRepeatSectionField, setShowRepeatSectionField] = useState<any>(
    group.is_iterative_or_not
  );

  useEffect(() => {
    const items = group?.fields?.map((field: any) => {
      return {
        id: field?.field_id,
        field
      };
    });
    setFieldsList(items);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [group]);

  const getItemPosition = (id: any) =>
    fieldsList.findIndex((field: any) => field.id === id);

  const handleDragEnd = async (event: any) => {
    const { active, over } = event;

    if (!over || active.id === over.id) return;

    const originalPosition = getItemPosition(active.id);
    const newPosition = getItemPosition(over.id);

    const newList = arrayMove(fieldsList, originalPosition, newPosition);
    setFieldsList(newList);

    const indexes = newList.map((fd: any, index: number) => {
      return {
        field_id: fd?.field?.field_id,
        field_index: index
      };
    });

    const fieldsData = {
      group_key: group?.group_key,
      group_index: group?.group_index,
      fields: indexes
    };

    await dispatch(updateFieldIndexes({ fieldsData, formId })).then(
      async () => {
        await dispatch(refetchform(formId)).then(() => {
          const fieldIndex = newList.findIndex(
            (fd: any) => fd?.id === active.id
          );
          dispatch(updateColumnIndex(fieldIndex));
        });
      }
    );
    // setFieldsList((fieldsList: any) => {
    //   const originalPosition = getItemPosition(active.id);
    //   const newPosition = getItemPosition(over.id);

    //   return arrayMove(fieldsList, originalPosition, newPosition);
    // });
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleOpenDeleteDialog = () => {
    setOpenDeleteDialog(true); // Open the dialog
    dispatch(loadSpinner(false));
    handleClose();
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false); // Close the dialog
  };

  const confirmDeleteSection = async () => {
    if (groupsCount > 1) {
      const data = {
        group_key: group.group_key
      };
      dispatch(loadSpinner(true));

      await dispatch(deletesection({ formId, data })).then(async (res: any) => {
        if (res.payload.status) {
          // toast.success('Section is deleted successfully.');
          const filteredGroups = formData?.groups.filter(
            (gp: any) => gp?.group_key !== group?.group_key
          );
          const indexes = filteredGroups?.map(
            (filterGroup: any, index: number) => ({
              group_key: filterGroup?.group_key,
              group_index: index
            })
          );
          const grpData = {
            groups: indexes
          };
          await dispatch(updateSectionIndexes({ data: grpData, formId })).then(
            async (response: any) => {
              if (response.payload.status) {
                await dispatch(refetchform(formId)).then((r: any) => {
                  if (sectionIndex === 0) {
                    dispatch(updateSectionIndex(0));
                    dispatch(
                      updateColumnIndex(
                        r.payload.data.groups[0].fields.length - 1
                      )
                    );
                  } else {
                    setShowRepeatSection(
                      formData?.groups[secIndex + 1]?.is_iterative_or_not
                    );
                    dispatch(updateSectionIndex(sectionIndex - 1));
                    dispatch(
                      updateColumnIndex(
                        r.payload.data.groups[sectionIndex - 1].fields.length -
                          1
                      )
                    );
                  }
                  const snackbarmsg = {
                    snackbarMessage: 'Section Deleted successfully.',
                    snackbarSeverity: 'success',
                    snackbarOpen: true,
                    setSnackbarOpen: null
                  };
                  dispatch(updateSnackbar(snackbarmsg));
                  setTimeout(() => {
                    snackbarmsg.snackbarMessage = '';
                    snackbarmsg.snackbarSeverity = 'success';
                    snackbarmsg.snackbarOpen = false;
                    dispatch(updateSnackbar(snackbarmsg));
                  }, 3000);
                  dispatch(loadSpinner(false));
                });
              } else if (response.payload.error) {
                dispatch(loadSpinner(false));
              }
            }
          );
        }
      });
    } else {
      dispatch(loadSpinner(false));
    }
    handleCloseDeleteDialog(); // Close the dialog after action
  };

  const addDuplicateSection = async () => {
    const data = {
      group_index: group?.group_index,
      group_key: group?.group_key
    };

    await dispatch(duplicatesection({ formId, data })).then(
      async (res: any) => {
        if (res.payload.status) {
          // toast.success(
          //   res?.payload?.message || 'Section Duplicated Successfully.'
          // );
          const copiedSectionKey = Object.keys(res.payload.data.fields);
          const lastKey = copiedSectionKey[copiedSectionKey.length - 1];
          const copiedSection = res.payload.data.fields[lastKey];
          const formGroups = [...Object.keys(res.payload.data.fields)];

          const correctGroupIndex = copiedSection.group_index + 1;
          const updatedSectionKeys = [
            ...formGroups.slice(0, correctGroupIndex),
            copiedSection?.group_key,
            ...formGroups.slice(correctGroupIndex)
          ];
          const uniqueSectionKeys = Array.from(new Set(updatedSectionKeys));

          const indexes = uniqueSectionKeys?.map((key: any, index: number) => ({
            group_key: key,
            group_index: index
          }));
          const grpData = {
            groups: indexes
          };
          await dispatch(updateSectionIndexes({ data: grpData, formId })).then(
            async (response: any) => {
              if (response.payload.status) {
                await dispatch(refetchform(formId)).then((r: any) => {
                  if (sectionIndex === 0) {
                    dispatch(updateSectionIndex(0 + 1));
                    dispatch(
                      updateColumnIndex(
                        r.payload.grpData.groups[0 + 1].fields.length - 1
                      )
                    );
                  } else {
                    dispatch(updateSectionIndex(sectionIndex + 1));
                    dispatch(
                      updateColumnIndex(
                        r.payload.data.groups[sectionIndex + 1].fields.length -
                          1
                      )
                    );
                  }
                  const snackbarmsg = {
                    snackbarMessage: 'Section Duplicated',
                    snackbarSeverity: 'success',
                    snackbarOpen: true,
                    setSnackbarOpen: null
                  };
                  dispatch(updateSnackbar(snackbarmsg));
                  setTimeout(() => {
                    snackbarmsg.snackbarMessage = '';
                    snackbarmsg.snackbarSeverity = 'success';
                    snackbarmsg.snackbarOpen = false;
                    dispatch(updateSnackbar(snackbarmsg));
                  }, 3000);
                });
              }
            }
          );
        }
      }
    );
  };

  return (
    <DndContext
      sensors={sensors}
      onDragEnd={handleDragEnd}
      collisionDetection={closestCorners}
    >
      <Box className="mt-20">
        <Box
          className="position-relative"
          sx={{
            backgroundColor: 'blu.main',
            width: 'fit-content',
            padding: '14px 20px',
            borderTopLeftRadius: '20px',
            borderTopRightRadius: '20px',
            top: '4px',
            zIndex: 1
          }}
        >
          <Typography
            className="font-weight-500"
            sx={{
              color: 'blk.main'
            }}
          >
            Section {secIndex + 1} of {formData?.groups?.length}
          </Typography>
        </Box>
        <Box className="section-container w-full">
          <Box
            id="form-section-card"
            className={`form-section-card ${
              secIndex === sectionIndex ? 'activeSection' : ''
            }`}
          >
            <Box className="d-flex align-items-center justify-content-end positon-absolute top-20 right-20">
              <IconButton onClick={handleClick}>
                <Icon
                  name="MoreVert"
                  sx={{
                    cursor: 'pointer'
                  }}
                />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleClose}
              >
                <MenuItem
                  onClick={() => {
                    addDuplicateSection();
                    handleClose();
                  }}
                >
                  <Typography>Duplicate</Typography>
                </MenuItem>
                <MenuItem
                  onClick={() => {
                    setShowRepeatSection(!showRepeatSection);
                    handleClose();
                  }}
                >
                  <Box className="d-flex align-items-center gap-10 justify-content-between">
                    <Typography>Repeat Section</Typography>
                    {showRepeatSection && (
                      <Box
                        className="d-flex align-items-center justify-content-center border-radius-50 w-24 h-24 ml-auto"
                        sx={{
                          backgroundColor: 'rgba(76, 175, 80, 0.2)'
                        }}
                      >
                        <Icon
                          name="Check"
                          fontSize="small"
                          sx={{ color: '#4caf50' }}
                        />
                      </Box>
                    )}
                  </Box>
                </MenuItem>
                {groupsCount > 1 && (
                  <MenuItem onClick={handleOpenDeleteDialog}>
                    <Typography>Delete</Typography>
                  </MenuItem>
                )}
              </Menu>
            </Box>
            <FormInput
              name={groupTitle}
              className="section-title-field"
              label=""
              autoComplete="off"
              placeholder="Section Title"
              isCreateForm
              handleInputChange={handleInputChange}
              containerStyles={{
                marginBottom: '0px',
                marginTop: '0px'
              }}
            />
            <FormInput
              name={groupDescription}
              label=""
              placeholder="Description"
              autoComplete="off"
              className="section-description"
              isCreateForm
              handleInputChange={handleInputChange}
              containerStyles={{
                marginBottom: '0px'
              }}
            />
            <Box className="d-flex align-items-center justify-content-end">
              {showRepeatSection && (
                <Box className="d-flex align-items-center">
                  <Box className="w-180">
                    {showRepeatSectionField && (
                      <FormInput
                        name={groupMaxIterationLength}
                        label=""
                        type="number"
                        inputMode="numeric"
                        handleInputChange={(e: any, values: any) => {
                          const value = parseInt(e.target.value, 10);

                          // Ensure the value is either empty (for clearing the input) or greater than or equal to 2
                          if (value >= 2 || e.target.value === '') {
                            handleInputChange(e, values); // Call the global handler for valid values
                          } else {
                            toast.warning(
                              'Value must be greater than or equal to 2'
                            );
                          }
                        }}
                        containerStyles={{
                          margin: '0px',
                          height: '45px',
                          '& .MuiInputBase-formControl': {
                            marginTop: '0px'
                          },
                          '& input': {
                            marginTop: '0px',
                            height: '45px'
                          }
                        }}
                      />
                    )}
                  </Box>

                  <FormControlLabel
                    name={groupIteration}
                    control={
                      <Switch defaultChecked={group?.is_iterative_or_not} />
                    }
                    onChange={(e: any) => {
                      setShowRepeatSectionField(!showRepeatSectionField);
                      handleInputChange(e);
                    }}
                    label="Repeat Section"
                    labelPlacement="start"
                  />
                </Box>
              )}
            </Box>
          </Box>
        </Box>

        <Box sx={[{ marginVertical: 20 }]}>
          <SortableContext
            strategy={verticalListSortingStrategy}
            items={fieldsList}
          >
            {fieldsList?.map((item: any, index: number) => {
              return (
                <RenderField
                  id={item.id}
                  field={item.field}
                  key={item.id}
                  groupKey={group.group_key}
                  style={{}}
                  isCreateForm
                  secIndex={secIndex}
                  colIndex={index}
                  activeTheme={activeTheme}
                />
              );
            })}
          </SortableContext>
        </Box>
      </Box>

      <Dialog
        open={openDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <Box sx={{ padding: '20px', background: '#ffffff' }}>
          <Box sx={{ padding: '20px', background: '#f6f6f6' }}>
            <DialogTitle id="delete-dialog-title">Delete Section?</DialogTitle>
            <DialogContent>
              <DialogContentText id="delete-dialog-description">
                Are you sure you want to delete this section? This action cannot
                be undone.
              </DialogContentText>
            </DialogContent>
          </Box>
          <DialogActions>
            <Button
              onClick={handleCloseDeleteDialog}
              sx={{
                backgroundColor: 'white2.main',
                color: 'primaryBlue.main',
                padding: '10px 30px',
                boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                textTransform: 'capitalize'
              }}
            >
              CANCEL
            </Button>
            <LoadingButton
              onClick={confirmDeleteSection}
              loading={loadingSpinner}
              sx={{
                backgroundColor: 'primaryBlue.main',
                color: 'white2.main',
                padding: '10px 35px',
                boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                '&:hover': {
                  color: 'white2.main',
                  backgroundColor: 'primaryBlue.main'
                }
              }}
            >
              DELETE
            </LoadingButton>
          </DialogActions>
        </Box>
      </Dialog>
    </DndContext>
  );
};
export default RenderGroup;
