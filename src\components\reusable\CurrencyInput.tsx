import { FormSelect } from '../form.elements';

const CurrencyInput = (handleCurrency: any) => {
  return (
    <div style={{ width: '100%' }}>
      {/* Select Currency Field */}
      <div style={{ marginBottom: '16px', width: '100%' }}>
        <FormSelect
          label="Select Currency Type"
          name="value"
          onChange={handleCurrency}
          fullWidth
          data={[
            { value: 'usd', label: 'US Dollar' },
            { value: 'eur', label: 'Euro' },
            { value: 'inr', label: 'Indian Rupee' },
            { value: 'gbp', label: 'British Pound' },
            { value: 'aud', label: 'Australian Dollar' }
            // Add more currencies as needed
          ]}
          placeholder="Choose a currency"
          containerStyles={{
            width: '100%',
            backgroundColor: '#FFFFFF'
          }}
        />
      </div>
    </div>
  );
};
export default CurrencyInput;
