import React, { useState } from 'react';
import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Radio,
  RadioGroup,
  Typography
} from '@mui/material';
import { useFormikContext } from 'formik';

import { FormInput, FormSelect } from '../form.elements';
import { ValidationInputTypes } from '../../types';

interface ValidationOption {
  value: string;
  label: string;
  type: string;
  style: string;
}

const validationOptions: ValidationOption[] = [
  { value: 'minimum', label: 'Min Length', type: 'number', style: 'checkbox' },
  { value: 'maximum', label: 'Max Length', type: 'number', style: 'checkbox' },
  {
    value: 'decimals',
    label: 'Allow only Numeric',
    type: 'number',
    style: 'radio'
  },
  {
    value: 'alphabets',
    label: 'Allow only Alphabets',
    type: 'text',
    style: 'radio'
  },
  {
    value: 'alphanumeric',
    label: 'Allow only AlphaNumeric',
    type: 'text',
    style: 'radio'
  },
  { value: 'text', label: 'Allow only Text', type: 'text', style: 'radio' },
  { value: 'regx', label: 'Regular Expression', type: 'text', style: 'radio' }
];

const validationTypes = [
  { value: 'custom', label: 'Custom Validations' },
  { value: 'conditional', label: 'Conditional Validations' }
];

const ValidationInput: React.FC<ValidationInputTypes> = ({
  type,
  value,
  onChange,
  onBlur,
  error
}) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      width: '50%',
      marginLeft: '10px'
    }}
  >
    <FormControl fullWidth>
      <FormInput
        name="validationInputField"
        label=""
        type={type}
        placeholder="Enter Value..."
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        containerStyles={{
          width: '100%',
          margin: '0px',
          height: '45px'
        }}
        sx={{
          '& .MuiInputBase-formControl': {
            margin: '0px',
            height: '45px'
          }
        }}
      />
    </FormControl>
    <FormHelperText sx={{ color: 'error.main' }}>{error}</FormHelperText>
  </Box>
);

interface ValidationControlProps {
  optionValue: string;
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  style: string; // 'checkbox' or 'radio'
}

const ValidationControl: React.FC<ValidationControlProps> = ({
  optionValue,
  checked,
  onChange,
  style
}) => {
  if (style === 'checkbox') {
    return (
      <Checkbox value={optionValue} checked={checked} onChange={onChange} />
    );
  }

  if (style === 'radio') {
    return <Radio value={optionValue} checked={checked} onChange={onChange} />;
  }

  return null;
};

interface CustomValidationsProps {
  lastUpdatedField: any;
}

export const CustomValidations: React.FC<CustomValidationsProps> = ({
  lastUpdatedField
}) => {
  const [validationType, setValidationType] = useState<string>('custom');
  const [selectedValidations, setSelectedValidations] = useState<string[]>([]);
  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValidationType(event.target.value);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = event.target;
    const selectedOption = validationOptions.find(
      (option) => option.value === value
    );

    setSelectedValidations((prevSelected) => {
      if (selectedOption?.style === 'checkbox') {
        // If it's a checkbox, add or remove from the selected validations
        return checked
          ? [...prevSelected, value]
          : prevSelected.filter((val) => val !== value);
      }
      if (selectedOption?.style === 'radio') {
        // If it's a radio button, remove all other radio options and set the new one
        return [
          ...prevSelected.filter(
            (val) =>
              !validationOptions
                .filter((option) => option.style === 'radio')
                .map((option) => option.value)
                .includes(val)
          ),
          value
        ];
      }
      return prevSelected;
    });
  };
  window.console.log(selectedValidations);
  window.console.log(inputValues);

  const handleValidation = (validationKey: string) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [validationKey]:
        inputValues[validationKey]?.trim() === ''
          ? 'This field is required.'
          : ''
    }));
  };

  const handleInputChange = (validationKey: string, value: string) => {
    setInputValues((prevInputValues) => ({
      ...prevInputValues,
      [validationKey]: value
    }));
  };

  // Only show input for minimum, maximum, and regx
  const showInputFields = ['minimum', 'maximum', 'regx'];

  const [conditionalValidationValue, setConditionalValidationValue] =
    useState('');
  const handleConditionalChange = (event: any) => {
    setConditionalValidationValue(event.target.value);
  };

  const { values } = useFormikContext<any>();
  window.console.log(values);
  // console.log(props?.lastUpdatedField);
  window.console.log(
    'Current Conditional Validation Value:',
    conditionalValidationValue
  );
  window.console.log('Groups:', values.groups);

  return (
    <Box sx={{ marginTop: '10px', maxWidth: '900px' }}>
      <Typography
        sx={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#0483BA',
          marginBottom: '16px',
          textAlign: 'center'
        }}
      >
        {' '}
        Validations{' '}
      </Typography>
      <Box
        sx={{
          width: '100%',
          marginTop: '5px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <FormControl component="fieldset">
          <RadioGroup
            name="validations"
            value={validationType}
            onChange={handleRadioChange}
            row
          >
            {validationTypes.map(({ value, label }) => (
              <FormControlLabel
                key={value}
                value={value}
                control={<Radio />}
                label={label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {validationType === 'custom' && (
        <Box
          sx={{
            marginTop: '30px',
            border: '1px solid #0483BA',
            borderRadius: '10px',
            backgroundColor: '#FAF9F8',
            width: '450px',
            padding: '20px'
          }}
        >
          {lastUpdatedField?.input_type === 'text' && (
            <FormGroup>
              {validationOptions.map((option) => (
                <Box
                  key={option.value}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '10px'
                  }}
                >
                  <FormControlLabel
                    control={
                      <ValidationControl
                        optionValue={option.value}
                        checked={selectedValidations.includes(option.value)}
                        onChange={handleCheckboxChange}
                        style={option.style} // Pass the style here
                      />
                    }
                    label={option.label}
                  />
                  {selectedValidations.includes(option.value) &&
                    showInputFields.includes(option.value) && (
                      <ValidationInput
                        key={option.value}
                        type={option.type}
                        value={inputValues[option.value] || ''}
                        onChange={(e) =>
                          handleInputChange(option.value, e.target.value)
                        }
                        onBlur={() => handleValidation(option.value)}
                        error={errors[option.value] || ''}
                      />
                    )}
                </Box>
              ))}
            </FormGroup>
          )}
          {lastUpdatedField?.input_type === 'fax' && (
            <div style={{ width: '100%' }}>
              {/* Select Country Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormSelect
                  label="Select Country"
                  name="country"
                  fullWidth
                  data={[
                    { value: 'us', label: 'United States' },
                    { value: 'ca', label: 'Canada' },
                    { value: 'in', label: 'India' },
                    { value: 'gb', label: 'United Kingdom' },
                    { value: 'au', label: 'Australia' }
                    // Add more countries as needed
                  ]}
                  placeholder="Choose a country"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
          {lastUpdatedField?.input_type === 'time' && (
            <div style={{ width: '100%' }}>
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Start Time"
                  name="startTime"
                  type="time"
                  fullWidth
                  // onChange={handleInputChange}
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
              <div style={{ width: '100%' }}>
                <FormInput
                  label="End Time"
                  name="endTime"
                  type="time"
                  fullWidth
                  // onChange={handleInputChange}
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
          {lastUpdatedField?.input_type === 'date' && (
            <div style={{ width: '100%' }}>
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Start Date"
                  name="startDate"
                  type="date"
                  fullWidth
                  // onChange={handleInputChange}
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
              <div style={{ width: '100%' }}>
                <FormInput
                  label="End Date"
                  name="endDate"
                  type="date"
                  fullWidth
                  // onChange={handleInputChange}
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
          {lastUpdatedField?.input_type === 'file' && (
            <div style={{ width: '100%' }}>
              {/* Max No of Files Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Max No of Files"
                  name="maxFiles"
                  type="number"
                  fullWidth
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>

              {/* Max File Size Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Max File Size (MB)"
                  name="maxFileSize"
                  type="number"
                  fullWidth
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>

              {/* File Format Field */}
              <div style={{ width: '100%' }}>
                <FormInput
                  label="File Format"
                  name="fileFormat"
                  type="text"
                  fullWidth
                  placeholder="e.g., jpg, png, pdf"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
          {lastUpdatedField?.input_type === 'phone' && (
            <div style={{ width: '100%' }}>
              {/* Select Country Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormSelect
                  label="Select Country"
                  name="country"
                  fullWidth
                  data={[
                    { value: 'us', label: 'United States' },
                    { value: 'ca', label: 'Canada' },
                    { value: 'in', label: 'India' },
                    { value: 'gb', label: 'United Kingdom' },
                    { value: 'au', label: 'Australia' }
                    // Add more countries as needed
                  ]}
                  placeholder="Choose a country"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
          {lastUpdatedField?.input_type === 'number' && (
            <div style={{ width: '100%' }}>
              {/* Min Value Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Min Value"
                  name="minValue"
                  type="number"
                  fullWidth
                  placeholder="Enter minimum value"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>

              {/* Max Value Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Max Value"
                  name="maxValue"
                  type="number"
                  fullWidth
                  placeholder="Enter maximum value"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
          {lastUpdatedField?.input_type === 'datetime-local' && (
            <div style={{ width: '100%' }}>
              {/* Start DateTime Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="Start DateTime"
                  name="startDateTime"
                  type="datetime-local"
                  fullWidth
                  placeholder="Select start date and time"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>

              {/* End DateTime Field */}
              <div style={{ marginBottom: '16px', width: '100%' }}>
                <FormInput
                  label="End DateTime"
                  name="endDateTime"
                  type="datetime-local"
                  fullWidth
                  placeholder="Select end date and time"
                  containerStyles={{
                    width: '100%',
                    backgroundColor: '#FFFFFF'
                  }}
                />
              </div>
            </div>
          )}
        </Box>
      )}
      {/* For conditional validation */}

      {validationType === 'conditional' && (
        <Box
          sx={{
            marginTop: '30px',
            border: '1px solid #0483BA',
            borderRadius: '10px',
            backgroundColor: '#FAF9F8',
            width: '450px',
            padding: '20px'
          }}
        >
          {/* Dropdown for conditional validation */}
          <Box
            sx={{
              marginBottom: '20px'
            }}
          >
            <FormSelect
              name="conditionalValidation"
              data={values.groups.flatMap((group: any) =>
                group.fields.map((field: any) => ({
                  value: field.field_id,
                  label: field.label
                }))
              )}
              onChange={handleConditionalChange}
              label="Select Input to Compare*"
              labelStyles={{
                color: '#3B5864',
                fontSize: '18px',
                fontWeight: 400
              }}
              placeholder="Select Conditional Validation"
              containerStyles={{
                width: '100%',
                backgroundColor: '#FFFFFF'
              }}
            />
          </Box>

          {/* Dynamically rendered input fields based on dropdown selection */}
          {values.groups.map((group: any) =>
            group.fields
              .filter(
                (field: any) => field.field_id === conditionalValidationValue
              )
              .map((field: any) => (
                <Box
                  key={field.field_id}
                  sx={{
                    marginTop: '10px'
                  }}
                >
                  {(() => {
                    // Render dropdown when field.label is 'choice'
                    if (field.label === 'choice') {
                      return (
                        <Box sx={{ marginBottom: '20px' }}>
                          <FormSelect
                            name={field.label}
                            data={[
                              { value: 'option1', label: 'Option 1' },
                              { value: 'option2', label: 'Option 2' },
                              { value: 'option3', label: 'Option 3' }
                            ]}
                            labelStyles={{
                              color: '#3B5864',
                              fontSize: '18px',
                              fontWeight: 400
                            }}
                            label="Value:"
                            placeholder="Select an Option"
                            containerStyles={{
                              width: '100%'
                            }}
                          />
                        </Box>
                      );
                    }

                    // Render switch for 'toggle' type
                    if (field.input_type === 'toggle') {
                      return (
                        <Box sx={{ marginBottom: '10px' }}>
                          <FormSelect
                            name="toggleOption"
                            data={[
                              { value: 'yes', label: 'Yes' },
                              { value: 'no', label: 'No' }
                            ]}
                            labelStyles={{
                              color: '#7E949D',
                              fontSize: '18px',
                              fontWeight: 400
                            }}
                            label="Select Option"
                            placeholder="Select Yes or No"
                            containerStyles={{
                              width: '100%'
                            }}
                          />
                        </Box>
                      );
                    }

                    // Render date comparison dropdown and input for 'date' type
                    if (field.input_type === 'date') {
                      return (
                        <Box
                          sx={{
                            display: 'block',
                            gap: '10px'
                          }}
                        >
                          {/* Comparison dropdown */}
                          <Box sx={{ marginBottom: '10px' }}>
                            <FormSelect
                              name="comparisonOperator"
                              data={[
                                { value: '<', label: 'Is LessThan' },
                                { value: '>', label: 'Is GreaterThan' },
                                { value: '=', label: 'Is EqualTo' },
                                { value: '<=', label: 'Is LessThanOrEqualTo' },
                                {
                                  value: '>=',
                                  label: 'Is GreaterThanOrEqualTo'
                                }
                              ]}
                              labelStyles={{
                                color: '#7E949D',
                                fontSize: '18px',
                                fontWeight: 400
                              }}
                              label="Select Date Comparison"
                              placeholder="Select Date Comparison"
                              containerStyles={{
                                width: '100%'
                              }}
                            />
                          </Box>
                          {/* Date input */}
                          <Box>
                            <FormInput
                              placeholder={field.label}
                              type="date"
                              name={field.label}
                              label={undefined}
                              containerStyles={{
                                width: '100%'
                              }}
                            />
                          </Box>
                        </Box>
                      );
                    }

                    // Default input rendering for other field types
                    return (
                      <FormInput
                        placeholder={field.label}
                        type={field.input_type}
                        name={field.label}
                        label={undefined}
                        containerStyles={{
                          width: '100%'
                        }}
                      />
                    );
                  })()}
                </Box>
              ))
          )}
        </Box>
      )}
    </Box>
  );
};
export default CustomValidations;
