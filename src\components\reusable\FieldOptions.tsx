/* eslint-disable react-hooks/exhaustive-deps */
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField
} from '@mui/material';
import { useFormikContext } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import { RootState } from '../../redux/reducers';
import { Icon } from './Icon';

import {
  refetchform,
  updatefield,
  updateFieldActive
} from '../../redux/reducers/form.reducer';
import { AppDispatch } from '../../redux/app.store';
import '../../css/index.scss';

export const Options = ({
  type,
  elements,
  fieldLabelId,
  fieldType
}: {
  type: string,
  elements: any,
  fieldLabelId: any,
  fieldType: any
}) => {
  // Kranthi: I have removed key (field) from props due to not using in this page.
  const { formId, isQuizForm, sectionIndex, columnIndex } = useSelector(
    (state: RootState) => state.form
  );
  const { values } = useFormikContext<any>();
  const dispatch = useDispatch<AppDispatch>();
  const [fieldElements, setFieldElements] = useState<any>([]);
  const [openDialogInOptions, setOpenDialogInOptions] = useState(false);

  const updateFieldElements = async (updatedFieldElements: any) => {
    const updatedValues = { ...values };

    const fieldData = {
      ...updatedValues.groups[sectionIndex].fields[columnIndex],
      options: updatedFieldElements,
      group_title: updatedValues.groups[sectionIndex].group_title,
      group_key: updatedValues.groups[sectionIndex].group_key
    };
    await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
      if (res.payload.status) {
        dispatch(updateFieldActive(res.payload.data));
        setFieldElements(updatedFieldElements);
      }
    });
  };

  useEffect(() => {
    setFieldElements(elements);
  }, [elements]);

  const addElement = async () => {
    const updatedFieldElements = [
      ...fieldElements,
      {
        label: `Options ${fieldElements.length + 1}`,
        value: `Options ${fieldElements.length + 1}`,
        status: false,
        url: '',
        url_type: ''
      }
    ];
    updateFieldElements(updatedFieldElements);
  };

  const changeElement = async (e: any, i: number) => {
    const { value } = e.target;

    if (value !== '' || value !== null || value !== undefined) {
      const updatedFieldElements = fieldElements?.map(
        (element: any, index: number) =>
          index === i ? { label: value, value } : element
      );
      updateFieldElements(updatedFieldElements);
    }
  };

  const deleteElement = async (elementType: string, name: any, i: number) => {
    const updatedValues = { ...values };
    const keys = name.split(/[[\].]+/).filter(Boolean);

    if (elementType === 'radio') {
      const updatedElements =
        fieldElements.length > 1
          ? [...fieldElements.slice(0, i), ...fieldElements.slice(i + 1)]
          : fieldElements;

      const fieldData = {
        ...updatedValues.groups[sectionIndex].fields[columnIndex],
        options: updatedElements,
        group_title: updatedValues.groups[sectionIndex].group_title,
        group_key: updatedValues.groups[sectionIndex].group_key
      };
      await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
        if (res.payload.status) {
          dispatch(refetchform(formId));
        }
      });
    } else if (elementType === 'checkbox') {
      setFieldElements(async (prevElements: any) => {
        const updatedFieldelements =
          prevElements.length > 1 ? prevElements.splice(i, 1) : prevElements;

        const fieldData = {
          ...updatedValues.groups[parseInt(keys[1], 10)].fields[
            parseInt(keys[3], 10)
          ],
          options: updatedFieldelements,
          group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
          group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
        };
        await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
          dispatch(updateFieldActive(res.payload.data));
          window.location.reload();
        });

        return updatedFieldelements;
      });
    }
  };

  const handleFileInput = (event: any, inputType: any, i?: any): void => {
    const file = event.target.files?.[0];

    if (file) {
      const reader = new FileReader();

      reader.onload = async () => {
        const imageSrc = reader.result as string;
        const updatedValues = { ...values };
        const keys = fieldLabelId.split(/[[\].]+/).filter(Boolean);

        if (imageSrc && inputType === 'label') {
          const fieldData = {
            ...updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ],
            label_url: imageSrc,
            label_url_type: 'image',
            options:
              updatedValues.groups[parseInt(keys[1], 10)].fields[
                parseInt(keys[3], 10)
              ].options,

            group_title:
              updatedValues.groups[parseInt(keys[1], 10)].group_title,
            group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
          };
          await dispatch(updatefield({ fieldData, formId })).then(
            (res: any) => {
              dispatch(updateFieldActive(res.payload.data));
            }
          );
        } else if (inputType === 'option') {
          if (fieldType.skelton.input_type === 'radio') {
            setFieldElements(async (prevElements: any) => {
              const updatedRadioelements = prevElements.map(
                (radio: any, index: number) =>
                  index === i
                    ? { ...radio, url: imageSrc, url_type: 'image' }
                    : radio
              );
              const fieldData = {
                ...updatedValues.groups[parseInt(keys[1], 10)].fields[
                  parseInt(keys[3], 10)
                ],
                options: updatedRadioelements,

                group_title:
                  updatedValues.groups[parseInt(keys[1], 10)].group_title,
                group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
              };
              await dispatch(updatefield({ fieldData, formId })).then(
                (res: any) => {
                  dispatch(updateFieldActive(res.payload.data));
                }
              );
              return updatedRadioelements;
            });
          } else if (fieldType.skelton.input_type === 'checkbox') {
            setFieldElements(async (prevElements: any) => {
              const updatedCheckboxelements = prevElements.map(
                (checkbox: any, index: number) =>
                  index === i
                    ? { ...checkbox, url: imageSrc, url_type: 'image' }
                    : checkbox
              );
              const fieldData = {
                ...updatedValues.groups[parseInt(keys[1], 10)].fields[
                  parseInt(keys[3], 10)
                ],
                options: updatedCheckboxelements,

                group_title:
                  updatedValues.groups[parseInt(keys[1], 10)].group_title,
                group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
              };
              await dispatch(updatefield({ fieldData, formId })).then(
                (res: any) => {
                  dispatch(updateFieldActive(res.payload.data));
                }
              );
              return updatedCheckboxelements;
            });
          }
          setOpenDialogInOptions(false);
        }
      };

      reader.readAsDataURL(file);
    }
  };

  const insertImage = (imgType: any, i?: any): void => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';
    fileInput.addEventListener('change', (e) => handleFileInput(e, imgType, i));
    fileInput.click();
  };

  const deleteMedia = async (mediaType: string, name: any, i?: number) => {
    const updatedValues = { ...values };
    const keys = name.split(/[[\].]+/).filter(Boolean);

    if (mediaType === 'label') {
      const fieldData = {
        ...updatedValues.groups[parseInt(keys[1], 10)].fields[
          parseInt(keys[3], 10)
        ],
        label_url: '',
        label_url_type: '',
        group_title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
        group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
      };
      await dispatch(updatefield({ fieldData, formId })).then((res: any) => {
        dispatch(updateFieldActive(res.payload.data));
      });
    } else if (mediaType === 'option') {
      if (fieldType.skelton.input_type === 'radio') {
        setFieldElements(async (prevElements: any) => {
          const updatedRadioelements = prevElements.map(
            (radio: any, index: number) =>
              index === i ? { ...radio, url: '', url_type: '' } : radio
          );

          const fieldData = {
            ...updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ],
            options: updatedRadioelements,
            group_title:
              updatedValues.groups[parseInt(keys[1], 10)].group_title,
            group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
          };
          await dispatch(updatefield({ fieldData, formId })).then(
            (res: any) => {
              dispatch(updateFieldActive(res.payload.data));
            }
          );

          return updatedRadioelements;
        });
      } else if (fieldType.skelton.input_type === 'checkbox') {
        setFieldElements(async (prevElements: any) => {
          const updatedCheckboxelements = prevElements.map(
            (checkbox: any, index: number) =>
              index === i ? { ...checkbox, url: '', url_type: '' } : checkbox
          );

          const fieldData = {
            ...updatedValues.groups[parseInt(keys[1], 10)].fields[
              parseInt(keys[3], 10)
            ],
            options: updatedCheckboxelements,
            group_title:
              updatedValues.groups[parseInt(keys[1], 10)].group_title,
            group_key: updatedValues.groups[parseInt(keys[1], 10)].group_key
          };
          await dispatch(updatefield({ fieldData, formId })).then(
            (res: any) => {
              dispatch(updateFieldActive(res.payload.data));
            }
          );

          return updatedCheckboxelements;
        });
      }
    }
  };

  return (
    <Box>
      {fieldElements?.length > 0 &&
        fieldElements?.map((element: any, index: number) => {
          const key = `${index}-${index * 2}-element-key`;
          return (
            <Box key={key}>
              <TextField
                id="input-with-icon-textfield"
                defaultValue={element?.label}
                onChange={(e) => changeElement(e, index)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Icon name="RadioButtonUncheckedOutlined" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="start">
                      {isQuizForm && (
                        <>
                          <IconButton
                            onClick={() => setOpenDialogInOptions(true)}
                          >
                            <Icon
                              name="Image"
                              sx={{ color: 'black' }}
                              fontSize="medium"
                            />
                          </IconButton>
                          <Dialog
                            open={openDialogInOptions}
                            onClose={() => setOpenDialogInOptions(false)}
                            aria-labelledby="alert-dialog-title"
                            aria-describedby="alert-dialog-description"
                          >
                            <DialogContent className="bg-white p-20">
                              <Box className="bg-FAF9F8 p-60 flex-column d-flex">
                                <Button
                                  onClick={() => insertImage('option', index)}
                                  className="font-size-18"
                                  sx={{
                                    padding: '10px 20px'
                                  }}
                                >
                                  Insert Image
                                </Button>
                              </Box>
                            </DialogContent>
                          </Dialog>
                        </>
                      )}
                      <IconButton
                        onClick={() => deleteElement(type, fieldLabelId, index)}
                      >
                        <Icon name="Delete" />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                variant="standard"
                className="w-full h-50"
                sx={{
                  '& input': {
                    borderBottomWidth: 0
                  }
                }}
              />
              {element?.url_type === 'image' && (
                <Box>
                  <img
                    src={element?.url}
                    alt="Label related"
                    style={{
                      marginLeft: '8px',
                      width: '260px',
                      height: '180px'
                    }}
                  />
                  <IconButton
                    onClick={() => deleteMedia('option', fieldLabelId, index)}
                  >
                    <Icon name="Delete" />
                  </IconButton>
                </Box>
              )}
              {element?.url_type === 'video' && (
                <Box>
                  <iframe
                    width="260"
                    height="180"
                    src={element?.url}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    style={{ marginLeft: '8px' }}
                    title="Label related video"
                  />
                  <IconButton
                    onClick={() => deleteMedia('option', fieldLabelId, index)}
                  >
                    <Icon name="Delete" />
                  </IconButton>
                </Box>
              )}
            </Box>
          );
        })}

      <Box>
        <TextField
          id="input-with-icon-textfield"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Icon name="RadioButtonUncheckedOutlined" />
              </InputAdornment>
            )
          }}
          variant="standard"
          // disabled
          onClick={() => addElement()}
          className="w-full"
          sx={{
            margin: '10px 0px'
          }}
          placeholder="Add Option..."
        />
      </Box>
    </Box>
  );
};
export default Options;
