import { useState } from 'react';
import { <PERSON><PERSON>, Dehaze } from '@mui/icons-material';
import { Box, Divider, Grid, IconButton, Tooltip } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { AppRoundedCard, SubMenu } from '../form.elements';

import '../../css/index.scss';
import '../../css/form-builder-styles.scss';

import { AppState } from '../../redux/app.store';
import LoaderUI from './loaderUI';

const FormBuilder: React.FC<{ appList: any }> = ({ appList }) => {
  const { isLoading }: any = useSelector((state: AppState) => state.form);
  const [listStyle, setListStyle] = useState<string>('grid');
  const navigate = useNavigate();

  return (
    <>
      <SubMenu
        search
        searchStyles={{
          width: '50%',
          height: '45px'
        }}
      />
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box
          sx={{
            padding: '0px 100px',
            height: 'calc(100% - 85px)'
          }}
        >
          <Box className="h-full d-flex flex-1 justify-content-between">
            <Box className="bg-white w-full h-full padding-0-60-60-60">
              <Box
                className="bg-FAF9F8 p-40 h-full"
                sx={{
                  boxShadow: '0px 0px 2px 0px rgba(0,0,0,0.24)'
                }}
              >
                <Box className="d-flex justify-content-between align-items-center p-40">
                  <Box className="d-flex align-items-center gap-10">
                    <Box className="font-weight-400 font-size-28">Apps :</Box>
                    <Box className="d-flex align-items-end gap-4">
                      <Box className="font-weight-400 font-size-28">
                        {appList?.length}
                      </Box>
                      <Box className="font-weight-400 font-size-17 pb-4">
                        Nos.
                      </Box>
                    </Box>
                  </Box>
                  <Box className="d-flex align-items-center">
                    <Tooltip title="Grid Style" arrow>
                      <IconButton onClick={() => setListStyle('grid')}>
                        <Apps
                          color="primary"
                          opacity={listStyle === 'grid' ? 1 : 0.4}
                        />
                      </IconButton>
                    </Tooltip>
                    <Divider
                      sx={{ height: 28, m: 0.5 }}
                      orientation="vertical"
                    />
                    <Tooltip title="List Style" arrow>
                      <IconButton onClick={() => setListStyle('list')}>
                        <Dehaze
                          color="primary"
                          opacity={listStyle === 'list' ? 1 : 0.4}
                        />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>

                <Grid container xs={12} columnSpacing={4} rowSpacing={4}>
                  {appList.map((data: any, index: any) => {
                    const key = `${index}-${index * 2}-data-key`;
                    return listStyle === 'list' ? (
                      <Grid item key={key} xs={4}>
                        <AppRoundedCard
                          icon="Widgets"
                          name={data?.name}
                          logoStyles={{
                            width: '50px',
                            height: '50px'
                          }}
                          containerStyles={{
                            justifyContent: 'flex-start'
                          }}
                          roundCardStyles={{
                            width: '60px',
                            height: '60px',
                            boxShadow: '0px 2px 6px 0px rgba(0,0,0,0.24)'
                          }}
                          nameStyles={{
                            fontWeight: '700'
                          }}
                          onClick={() =>
                            navigate(
                              `/form-builder/form-details/${data?.app_id}`
                            )
                          }
                          logo="/Alphateklogo1.png"
                          listStyle="list"
                          // versionsList={[
                          //   'V-10.00.01',
                          //   'V-10.00.02',
                          //   'V-10.00.03',
                          //   'V-10.00.04',
                          //   'V-10.00.05',
                          //   'V-10.00.06',
                          //   'V-10.00.07'
                          // ]}
                        />
                      </Grid>
                    ) : (
                      <Grid item key={key} xs={2.9}>
                        <AppRoundedCard
                          icon="Widgets"
                          name={
                            data?.orgAppConfiguration?.app_name
                              ? data?.orgAppConfiguration?.app_name
                              : data?.name
                          }
                          logoStyles={{
                            width: '60px',
                            height: '60px'
                          }}
                          containerStyles={{
                            justifyContent: 'flex-start'
                          }}
                          roundCardStyles={{
                            width: '140px',
                            height: '140px',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: '16px',
                            background: '#fff',
                            boxShadow: '0px 4px 8px rgba(0,0,0,0.15)',
                            transition: '0.3s ease-in-out',
                            cursor: 'pointer'
                          }}
                          nameStyles={{
                            fontWeight: '700'
                          }}
                          onClick={() =>
                            navigate(
                              `/form-builder/form-details/${data?.app_id}`
                            )
                          }
                          logo="/Alphateklogo1.png"
                          listStyle="grid"
                          // versionsList={[
                          //   'V-10.00.01',
                          //   'V-10.00.02',
                          //   'V-10.00.03',
                          //   'V-10.00.04',
                          //   'V-10.00.05',
                          //   'V-10.00.06',
                          //   'V-10.00.07'
                          // ]}
                        />
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </Box>
          </Box>
        </Box>
      )}
    </>
  );
};
export default FormBuilder;
