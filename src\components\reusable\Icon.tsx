import * as Icons from '@mui/icons-material';

import { IconProps } from '../../types';

export const Icon: React.FC<IconProps> = ({
  name,
  color,
  fontSize,
  style,
  sx,
  onClick
}) => {
  const IconComponent = Icons[name];

  if (!IconComponent) {
    return null;
  }

  return (
    <IconComponent
      color={color}
      fontSize={fontSize}
      style={style}
      sx={sx}
      onClick={onClick}
    />
  );
};
export default Icon;
