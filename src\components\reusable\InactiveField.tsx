import { Box } from '@mui/material';
import { useDispatch } from 'react-redux';
import { CSS } from '@dnd-kit/utilities';

import { AppDispatch } from '../../redux/app.store';
import {
  updateColumnIndex,
  updateSectionIndex
} from '../../redux/reducers/form.reducer';
import { Icon } from './Icon';
import '../../css/index.scss';

export const InactiveField: React.FC<{
  children: any,
  secIndex: any,
  colIndex: any,
  setNodeRef?: any,
  attributes?: any,
  listeners?: any,
  transform?: any,
  transition?: any
}> = ({
  children,
  secIndex,
  colIndex,
  setNodeRef,
  attributes,
  listeners,
  transform,
  transition
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const handleFieldsClick = () => {
    dispatch(updateSectionIndex(secIndex));
    dispatch(updateColumnIndex(colIndex));
  };

  const style = {
    transition,
    transform: CSS.Transform.toString(transform)
  };

  return (
    <Box
      className="bg-white d-flex flex-column mt-10"
      sx={{
        borderRadius: '4px',
        padding: '30px 30px 30px 30px',
        boxShadow: '0px 1px 0px 2px #24242410',
        ...style
      }}
      onClick={handleFieldsClick}
    >
      <Box
        ref={setNodeRef}
        {...{ attributes }}
        {...{ listeners }}
        className="flex-center cursor-pointer"
      >
        <Icon
          name="OpenWith"
          sx={{
            fontSize: '18px'
          }}
        />
      </Box>
      {children}
    </Box>
  );
};
export default InactiveField;
