import { Backdrop, CircularProgress } from '@mui/material';

export const Loader: React.FC<{ status: boolean; handleClose?: any }> = ({
  status,
  handleClose
}) => {
  return (
    <Backdrop
      sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
      open={status}
      onClick={handleClose}
    >
      <CircularProgress color="inherit" />
    </Backdrop>
  );
};
export default Loader;
