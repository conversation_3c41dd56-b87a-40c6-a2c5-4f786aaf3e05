import {
  Page,
  Text,
  View,
  Document,
  Image,
  Svg,
  Rect,
  Line,
  Font,
  StyleSheet
} from '@react-pdf/renderer';
// import { useLocation } from 'react-router-dom';
import SignatureRow from './Signature';

// Styles for the PDF
const styles = StyleSheet.create({
  body: {
    paddingTop: 35,
    paddingBottom: 65,
    paddingHorizontal: 35,
    backgroundColor: '#FFFFF0'
    // border: '1px solid black'
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    // marginVertical: 20,
    textDecoration: 'underline',
    marginLeft: 10
  },
  header: {
    // marginBottom: 20,
    textAlign: 'center',
    color: 'grey'
    // backgroundColor: "red",
  },
  logoContainer: {
    marginBottom: 10,
    alignItems: 'flex-end'
  },
  logo: {
    width: 100,
    height: 30,
    objectFit: 'contain',
    marginBottom: 12
  },
  row: {
    flexDirection: 'row',
    marginBottom: 10
  },
  // label: {
  //   fontWeight: "bold",
  //   marginRight: 10,
  //   textTransform: "capitalize",
  // },
  value: {
    // fontSize: 15,
    // marginRight: '30px'
    marginLeft: '15px'
  },
  footer: {
    // position: "absolute",
    // bottom: 10,
    // left: 35,
    // right: 35,
    textAlign: 'center',
    // backgroundColor: "#08366b",
    // padding: 10,
    alignItems: 'center',
    justifyContent: 'center'
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
    // color: "#c3cddd",
    color: 'black',
    fontWeight: 600
  },
  link: {
    color: '#007BFF', // Link color
    textDecoration: 'underline'
  },

  table: {
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bfbfbf',
    marginBottom: 80 // Added space for footer
  },
  tableRow: {
    flexDirection: 'row'
  },
  tableHeader: {
    backgroundColor: '#f2f2f2'
  },
  tableCell: {
    margin: 0,
    fontSize: 10,
    flex: 1,
    textAlign: 'center',
    padding: 4
  },
  headerCell: {
    fontWeight: 'bold'
  },
  bordered: {
    borderWidth: 1,
    borderColor: '#bfbfbf',
    borderStyle: 'solid'
  },
  evenRow: {
    backgroundColor: '#f9f9f9'
  },
  oddRow: {
    backgroundColor: '#ffffff'
  },

  // pageNumber: {
  //   fontSize: 10,
  //   textAlign: "center",
  //   color: "grey",
  //   marginTop: 7,
  // },
  page: {
    position: 'relative'
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  },
  text: {
    margin: 10,
    fontSize: 12
  },
  section: {
    marginBottom: 20
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    marginLeft: '10px'
    // backgroundColor: '#cee4f4',
    // height: '70px'
    // padding: 8
  },
  sectionTitleWithoutBG: {
    // fontSize: 18,
    fontWeight: 600,
    marginBottom: 10,
    // padding: 8,
    fontSize: 20,
    marginRight: '80px'
  },
  field: {
    marginBottom: 4,
    flexDirection: 'row',
    // alignItems: 'center',
    lineHeight: 0.7
  },
  label: {
    // color: '#333',
    fontWeight: 'bold',
    // marginRight: 10,
    width: 200,
    marginLeft: '35px'
    // fontSize: 17
  },
  container: {
    padding: 10
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 4,
    marginTop: 8
  },
  signature: {
    width: 150,
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    marginTop: 8
  },
  watermark: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%) rotate(-45deg)', // Center and rotate
    fontSize: 100,
    color: 'rgba(0, 0, 0, 0.1)', // Light transparency
    textAlign: 'center'
  },
  fieldRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  pageNumberStyles: {
    fontSize: 11,
    color: 'black'
    // width: "100%",
  },
  formName: {
    fontSize: 20,
    fontWeight: 600,
    marginBottom: 10,
    backgroundColor: '#cee4f4',
    height: '50px'
  },
  formNameText: {
    marginLeft: '10px',
    marginTop: '10px'
  },
  dateRowContainer: {
    display: 'flex',
    flexDirection: 'row', // horizontal side by side
    justifyContent: 'space-between', // space between the dates
    marginBottom: 10,
    marginRight: '130px'
  },
  dateText: {
    display: 'flex',
    fontSize: 16,
    fontWeight: 'bold'
  }
});
Font.registerEmojiSource({
  format: 'png',
  url: 'https://cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/72x72/%27'
});

const Checkbox = ({ checked }: { checked: boolean }) => (
  <Svg
    width="12"
    height="12"
    style={{
      marginLeft: 8,
      backgroundColor: checked ? '#0078d4' : '#fff',
      border: '1px solid black'
    }}
  >
    {/* Draw the checkbox square */}
    <Rect
      x="0"
      y="0"
      width="14"
      height="14"
      stroke="white"
      fill="none"
      strokeWidth="1"
    />
    {/* Add a checkmark if checked */}
    {checked && (
      <Line x1="2" y1="6" x2="5" y2="9" stroke="white" strokeWidth="1" />
    )}
    {checked && (
      <Line x1="5" y1="9" x2="10" y2="3" stroke="white" strokeWidth="1" />
    )}
  </Svg>
);

const MyNewPDFComponent = ({
  formResponses,
  orientationValue,
  sizeValue,
  breakValue,
  marginValue,
  logoAlignment,
  colorScheme,
  backgroundColor,
  watermarkText,
  // alignText,
  alignPageNumberText,
  insertLogo,
  headerTitle,
  fontFamily, // Add these new props
  fontSize,
  textColor,
  watermarkImage,
  headerHeight,
  // headerWidth,
  footerHeight,
  footerText
  // isDummyData
}: {
  formResponses: any;
  orientationValue: any;
  sizeValue: any;
  breakValue: string;
  marginValue: any;
  logoAlignment: any;
  colorScheme: any;
  backgroundColor: string;
  watermarkText: string;
  // alignText: any;
  alignPageNumberText: any;
  insertLogo: any;
  headerTitle: any;
  fontFamily: string;
  fontSize: string;
  textColor: string;
  watermarkImage: any;
  headerHeight: any;
  footerHeight: any;
  footerText: any;
  // isDummyData: any;
}) => {
  const marginStyles: any = {
    Normal: {
      marginTop: 0,
      marginBottom: 0,
      marginLeft: 0,
      marginRight: 0
    },
    Narrow: {
      marginTop: 20,
      marginBottom: 20,
      marginLeft: 20,
      marginRight: 20
    },
    Wide: { marginTop: 60, marginBottom: 60, marginLeft: 60, marginRight: 60 }
  };

  const alignmentStyles: any = {
    center: { alignItems: 'center' },
    start: { alignItems: 'flex-start' },
    end: { alignItems: 'flex-end' }
  };

  const alignPageTextStyles: any = {
    center: { textAlign: 'center' },
    left: { textAlign: 'left' },
    right: { textAlign: 'right' }
  };

  const colorSchemes: any = {
    White: { backgroundColor: '#FFFFFF', color: '#000000' },
    Black: { backgroundColor: '#000000', color: '#FFFFFF' },
    Green: { backgroundColor: 'green', color: '#000000' }
  };

  return (
    <Document>
      {breakValue === 'TextWrap' ? (
        formResponses?.map((formResponse: any, index: any) => {
          const formData = formResponse?.form;
          const formResponseValues = formResponse?.values;
          return (
            <Page
              key={formResponse?.form_value_id || index}
              style={{
                ...styles.body,
                ...colorSchemes[colorScheme],
                marginTop: marginStyles[marginValue].marginTop,
                marginBottom: marginStyles[marginValue].marginBottom,
                marginLeft: marginStyles[marginValue].marginLeft,
                marginRight: marginStyles[marginValue].marginRight,
                fontFamily,
                fontSize,
                color: textColor,
                padding: 10,
                width: '100%'
              }}
              orientation={orientationValue.toLowerCase()}
              size={sizeValue}
              // wrap={breakValue === 'TextWrap'}
              wrap={false}
            >
              <View
                style={{
                  ...styles.background,
                  backgroundColor: backgroundColor || '#FFFFFF'
                }}
              />
              <View
                style={[
                  styles.header,
                  alignmentStyles[logoAlignment],
                  {
                    height: headerHeight,
                    flexDirection: 'row',
                    alignItems: 'center', // Aligns items vertically in the center
                    paddingHorizontal: 10 // Adds spacing on both sides
                  }
                ]}
                fixed
              >
                <Image
                  src={insertLogo}
                  style={[styles.logo, { width: 50, height: 50 }]}
                />
                <Text
                  fixed
                  style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: 'black',
                    marginBottom: 10
                  }}
                >
                  {headerTitle}
                </Text>
              </View>

              {watermarkText && (
                <Text style={[styles.watermark]} fixed>
                  {watermarkText}
                </Text>
              )}
              {watermarkImage && (
                <Image style={styles.watermark} src={watermarkImage} />
              )}

              <View style={styles.container}>
                <View style={styles.formName}>
                  <Text style={styles.formNameText}>
                    {formResponse?.form?.name}
                  </Text>
                </View>

                {/* Iterate through form sections */}
                {Object.values(formData?.fields).map(
                  (sectionDetails: any, sectionIndex: any) => {
                    const signatures: any[] = [];

                    // Handle iterative sections (i.e., repeating groups of fields)
                    if (sectionDetails?.is_iterative_or_not) {
                      const sectionValues =
                        formResponseValues?.[sectionDetails.group_key];

                      // Check if data exists and is an array
                      if (
                        sectionValues &&
                        Array.isArray(sectionValues) &&
                        sectionValues.length > 0
                      ) {
                        return sectionValues.map(
                          (sectionValuesData: any, sectionValueIndex: any) => {
                            const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                            return (
                              <View
                                style={[styles.section, { marginBottom: 20 }]}
                                key={sectionKey}
                              >
                                {/* Section title with index number */}
                                <Text style={styles.sectionTitle}>
                                  {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
                                </Text>

                                {/* Render fields for each iteration */}
                                {sectionDetails.fields.map(
                                  (field: any, fieldIndex: any) => {
                                    const value =
                                      sectionValuesData?.[field.name] || 'N/A';
                                    const isBase64Image =
                                      typeof value === 'string' &&
                                      value.startsWith('data:image/');
                                    const displayValue =
                                      typeof value === 'string' &&
                                      !isBase64Image
                                        ? value
                                        : 'N/A';

                                    // Render signature fields in pairs
                                    if (field.type === 'signature') {
                                      signatures.push({
                                        label: field.label,
                                        value: value || null
                                      });

                                      if (signatures.length === 2) {
                                        const tempSignatures = [...signatures];
                                        signatures.length = 0;
                                        return (
                                          <SignatureRow
                                            key={`signature-row-${fieldIndex + 1}`}
                                            signatures={tempSignatures}
                                          />
                                        );
                                      }
                                      return null;
                                    }

                                    // Render checkbox options
                                    if (field.input_type === 'checkbox') {
                                      return (
                                        <View key={`${fieldIndex + 1}`}>
                                          <Text style={styles.label}>
                                            {field.label}
                                          </Text>
                                          <View
                                            // style={{
                                            //   flexDirection: field.label
                                            //     ? 'column'
                                            //     : 'row',
                                            //   flexWrap: field.label
                                            //     ? 'nowrap'
                                            //     : 'wrap',
                                            //   marginLeft: field.label
                                            //     ? '45%'
                                            //     : 0
                                            // }}
                                            style={{
                                              flexDirection: field.label
                                                ? 'column'
                                                : 'row',
                                              flexWrap: field.label
                                                ? 'nowrap'
                                                : 'wrap',
                                              marginLeft: field.label
                                                ? '43.5%'
                                                : 29,
                                              gap: 5
                                            }}
                                          >
                                            {field.options.map(
                                              (
                                                option: any,
                                                optionIndex: any
                                              ) => (
                                                <View
                                                  // style={[
                                                  //   styles.field,
                                                  //   {
                                                  //     marginBottom: 25,
                                                  //     flexDirection: 'row',
                                                  //     alignItems: 'center',
                                                  //     // width: '70%',
                                                  //     marginLeft: field.label
                                                  //       ? '45%'
                                                  //       : 0
                                                  //   }
                                                  // ]}
                                                  // style={[
                                                  //   styles.field,
                                                  //   {
                                                  //     flexDirection: 'row',
                                                  //     alignItems: 'center',
                                                  //     marginBottom: 10,
                                                  //     marginRight: field.label
                                                  //       ? 0
                                                  //       : 20 // spacing when side-by-side
                                                  //   }
                                                  // ]}
                                                  style={{
                                                    flexDirection: 'row',
                                                    alignItems: 'flex-start',
                                                    marginBottom: 18,
                                                    width: field.label
                                                      ? 'auto'
                                                      : '30%',
                                                    marginRight: field.label
                                                      ? 0
                                                      : 10
                                                  }}
                                                  key={`${optionIndex + 1}`}
                                                >
                                                  <Checkbox
                                                    checked={sectionValuesData?.[
                                                      field.name
                                                    ]?.includes(option.value)}
                                                  />
                                                  <Text style={styles.value}>
                                                    {option.label}
                                                  </Text>
                                                </View>
                                              )
                                            )}
                                          </View>
                                        </View>
                                      );
                                    }

                                    // Render other field types
                                    return (
                                      <View
                                        style={[
                                          styles.field,
                                          { marginBottom: 25 }
                                        ]}
                                        key={`${fieldIndex + 1}`}
                                      >
                                        <Text style={styles.label}>
                                          {field.label}
                                        </Text>
                                        {isBase64Image ? (
                                          <Image
                                            src={value}
                                            style={styles.image}
                                          />
                                        ) : (
                                          <Text style={styles.value}>
                                            {displayValue}
                                          </Text>
                                        )}
                                      </View>
                                    );
                                  }
                                )}

                                {/* Render any remaining unpaired signature */}
                                {signatures.length > 0 && (
                                  <SignatureRow signatures={signatures} />
                                )}
                              </View>
                            );
                          }
                        );
                      }

                      // No data available for iterative section
                      return null;
                    }

                    // Handle non-iterative (single instance) sections
                    return (
                      <View
                        style={[styles.section, { marginBottom: 20 }]}
                        key={`${sectionIndex + 1}`}
                      >
                        {/* Section title without index */}
                        <Text style={styles.sectionTitle}>
                          {sectionDetails.group_title}
                        </Text>

                        {/* Render fields */}
                        {sectionDetails.fields.map(
                          (field: any, fieldIndex: any) => {
                            const value =
                              formResponseValues?.[sectionDetails.group_key]?.[
                                field.name
                              ] || 'N/A';
                            const isBase64Image =
                              typeof value === 'string' &&
                              value.startsWith('data:image/');
                            const displayValue =
                              typeof value === 'string' && !isBase64Image
                                ? value
                                : 'N/A';

                            // Render signature fields in pairs
                            if (field.type === 'signature') {
                              signatures.push({
                                label: field.label,
                                value: value || null
                              });

                              if (signatures.length === 2) {
                                const tempSignatures = [...signatures];
                                signatures.length = 0;
                                return (
                                  <SignatureRow
                                    key={`signature-row-${fieldIndex + 1}`}
                                    signatures={tempSignatures}
                                  />
                                );
                              }
                              return null;
                            }

                            // Render checkbox options
                            if (field.input_type === 'checkbox') {
                              return (
                                <View key={`${fieldIndex + 1}`}>
                                  <Text style={styles.label}>
                                    {field.label}
                                  </Text>
                                  <View
                                    // style={{
                                    //   flexDirection: field.label
                                    //     ? 'column'
                                    //     : 'row',
                                    //   flexWrap: field.label ? 'nowrap' : 'wrap',
                                    //   marginLeft: field.label ? '45%' : 0
                                    // }}
                                    style={{
                                      flexDirection: field.label
                                        ? 'column'
                                        : 'row',
                                      flexWrap: field.label ? 'nowrap' : 'wrap',
                                      marginLeft: field.label ? '43.5%' : 29,
                                      gap: 5
                                    }}
                                  >
                                    {field.options.map(
                                      (option: any, optionIndex: any) => (
                                        <View
                                          // style={[
                                          //   styles.field,
                                          //   {
                                          //     marginBottom: 25,
                                          //     flexDirection: 'row',
                                          //     alignItems: 'center',
                                          //     // width: '70%',
                                          //     marginLeft: field.label
                                          //       ? '45%'
                                          //       : 0
                                          //   }
                                          // ]}
                                          // style={[
                                          //   styles.field,
                                          //   {
                                          //     flexDirection: 'row',
                                          //     alignItems: 'center',
                                          //     marginBottom: 10,
                                          //     marginRight: field.label ? 0 : 20 // spacing when side-by-side
                                          //   }
                                          // ]}
                                          style={{
                                            flexDirection: 'row',
                                            alignItems: 'flex-start',
                                            marginBottom: 18,
                                            width: field.label ? 'auto' : '30%',
                                            marginRight: field.label ? 0 : 10
                                          }}
                                          key={`${optionIndex + 1}`}
                                        >
                                          <Checkbox
                                            checked={formResponseValues?.[
                                              sectionDetails.group_key
                                            ]?.[field.name]?.includes(
                                              option.value
                                            )}
                                          />
                                          <Text style={styles.value}>
                                            {option.label}
                                          </Text>
                                        </View>
                                      )
                                    )}
                                  </View>
                                </View>
                              );
                            }

                            // Render other field types
                            return (
                              <View
                                style={[styles.field, { marginBottom: 25 }]}
                                key={`${fieldIndex + 1}`}
                              >
                                <Text style={styles.label}>{field.label}</Text>
                                {isBase64Image ? (
                                  <Image src={value} style={styles.image} />
                                ) : (
                                  <Text style={styles.value}>
                                    {displayValue}
                                  </Text>
                                )}
                              </View>
                            );
                          }
                        )}

                        {/* Render any remaining unpaired signature */}
                        {signatures.length > 0 && (
                          <SignatureRow signatures={signatures} />
                        )}
                      </View>
                    );
                  }
                )}
              </View>

              <View
                style={[
                  styles.footer,
                  {
                    height: footerHeight,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }
                ]}
                fixed
              >
                {footerText && (
                  <Text style={[styles.footerText, { marginRight: 0 }]}>
                    {typeof footerText === 'string' && footerText.length > 50
                      ? `${footerText.slice(0, 50)}`
                      : footerText}
                  </Text>
                )}
                <Text
                  style={[
                    styles.pageNumberStyles,
                    alignPageTextStyles[alignPageNumberText]
                  ]}
                  render={({ pageNumber, totalPages }) =>
                    `${footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
                  }
                />
              </View>
            </Page>
          );
        })
      ) : (
        <Page
          style={{
            ...styles.body,
            ...colorSchemes[colorScheme],
            marginTop: marginStyles[marginValue].marginTop,
            marginBottom: marginStyles[marginValue].marginBottom,
            marginLeft: marginStyles[marginValue].marginLeft,
            marginRight: marginStyles[marginValue].marginRight,
            fontFamily,
            fontSize,
            color: textColor,
            padding: 10,
            width: '100%'
          }}
          orientation={orientationValue.toLowerCase()}
          size={sizeValue}
          wrap={breakValue === 'TextWrap'}
        >
          <View
            style={{
              ...styles.background,
              backgroundColor: backgroundColor || '#FFFFFF'
            }}
          />

          <View
            style={[
              styles.header,
              alignmentStyles[logoAlignment],
              {
                height: headerHeight,
                flexDirection: 'row',
                alignItems: 'center', // Aligns items vertically in the center
                paddingHorizontal: 10 // Adds spacing on both sides
              }
            ]}
            fixed
          >
            <Image
              src={insertLogo}
              style={[styles.logo, { width: 60, height: 60 }]}
            />
            <Text
              fixed
              style={{
                fontSize: 25,
                fontWeight: 'bold',
                color: 'black',
                marginBottom: 10
              }}
            >
              {headerTitle}
            </Text>
          </View>
          <View
            style={{
              height: 2,
              backgroundColor: 'black',
              width: '100%',
              marginTop: 5,
              marginBottom: 10
            }}
          />

          {watermarkText && (
            <Text style={[styles.watermark]} fixed>
              {watermarkText}
            </Text>
          )}
          {watermarkImage && (
            <Image style={styles.watermark} src={watermarkImage} />
          )}

          {
            /* working lables */
            <View style={styles.container}>
              {formResponses?.map((formResponse: any, index: any) => {
                const formData = formResponse?.form;
                const formResponseValues = formResponse?.values;
                return (
                  <View key={formData.id || index}>
                    <View style={styles.formName}>
                      <Text style={styles.formNameText}>
                        {formResponse?.form?.name}
                      </Text>
                    </View>

                    {/* Iterate through form sections */}
                    {Object.values(formData?.fields).map(
                      (sectionDetails: any, sectionIndex: any) => {
                        const signatures: any[] = [];

                        // Handle iterative sections (i.e., repeating groups of fields)
                        if (sectionDetails?.is_iterative_or_not) {
                          const sectionValues =
                            formResponseValues?.[sectionDetails.group_key];

                          // Check if data exists and is an array
                          if (
                            sectionValues &&
                            Array.isArray(sectionValues) &&
                            sectionValues.length > 0
                          ) {
                            return sectionValues.map(
                              (
                                sectionValuesData: any,
                                sectionValueIndex: any
                              ) => {
                                const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                                return (
                                  <View
                                    style={[
                                      styles.section,
                                      { marginBottom: 20 }
                                    ]}
                                    key={sectionKey}
                                  >
                                    {/* Section title with index number */}
                                    <Text style={styles.sectionTitle}>
                                      {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
                                    </Text>

                                    {/* Render fields for each iteration */}
                                    {sectionDetails.fields.map(
                                      (field: any, fieldIndex: any) => {
                                        const value =
                                          sectionValuesData?.[field.name] ||
                                          'N/A';
                                        const isBase64Image =
                                          typeof value === 'string' &&
                                          value.startsWith('data:image/');
                                        const displayValue =
                                          typeof value === 'string' &&
                                          !isBase64Image
                                            ? value
                                            : 'N/A';

                                        // Render signature fields in pairs
                                        if (field.type === 'signature') {
                                          signatures.push({
                                            label: field.label,
                                            value: value || null
                                          });

                                          if (signatures.length === 2) {
                                            const tempSignatures = [
                                              ...signatures
                                            ];
                                            signatures.length = 0;
                                            return (
                                              <SignatureRow
                                                key={`signature-row-${fieldIndex + 1}`}
                                                signatures={tempSignatures}
                                              />
                                            );
                                          }
                                          return null;
                                        }
                                        // Render checkbox options
                                        if (field.input_type === 'checkbox') {
                                          return (
                                            <View key={`${fieldIndex + 1}`}>
                                              <Text style={styles.label}>
                                                {field.label}
                                              </Text>
                                              <View
                                                style={{
                                                  flexDirection: field.label
                                                    ? 'column'
                                                    : 'row',
                                                  flexWrap: field.label
                                                    ? 'nowrap'
                                                    : 'wrap',
                                                  marginLeft: field.label
                                                    ? '43.5%'
                                                    : 29,
                                                  gap: 5
                                                }}
                                              >
                                                {field.options.map(
                                                  (
                                                    option: any,
                                                    optionIndex: any
                                                  ) => (
                                                    <View
                                                      style={{
                                                        flexDirection: 'row',
                                                        alignItems:
                                                          'flex-start',
                                                        marginBottom: 18,
                                                        width: field.label
                                                          ? 'auto'
                                                          : '30%',
                                                        marginRight: field.label
                                                          ? 0
                                                          : 10
                                                      }}
                                                      key={`${optionIndex + 1}`}
                                                    >
                                                      <Checkbox
                                                        checked={sectionValuesData?.[
                                                          field.name
                                                        ]?.includes(
                                                          option.value
                                                        )}
                                                      />
                                                      <Text
                                                        style={styles.value}
                                                      >
                                                        {option.label}
                                                      </Text>
                                                    </View>
                                                  )
                                                )}
                                              </View>
                                            </View>
                                          );
                                        }

                                        // Render other field types
                                        return (
                                          <View
                                            style={[
                                              styles.field,
                                              { marginBottom: 25 }
                                            ]}
                                            key={`${fieldIndex + 1}`}
                                          >
                                            <Text style={styles.label}>
                                              {field.label}
                                            </Text>
                                            {isBase64Image ? (
                                              <Image
                                                src={value}
                                                style={styles.image}
                                              />
                                            ) : (
                                              <Text style={styles.value}>
                                                {displayValue}
                                              </Text>
                                            )}
                                          </View>
                                        );
                                      }
                                    )}

                                    {/* Render any remaining unpaired signature */}
                                    {signatures.length > 0 && (
                                      <SignatureRow signatures={signatures} />
                                    )}
                                    {/* Render extra _date fields */}
                                    <View style={styles.dateRowContainer}>
                                      {Object.entries(
                                        sectionValuesData || {}
                                      ).map(([key, val]: any) => {
                                        const isDateField =
                                          key.endsWith('_date');
                                        const dateOnly =
                                          typeof val === 'string'
                                            ? val.slice(0, 10)
                                            : val;
                                        const isAlreadyRendered =
                                          sectionDetails.fields.some(
                                            (field: any) => field.name === key
                                          );

                                        if (isDateField && !isAlreadyRendered) {
                                          return (
                                            <View
                                              style={styles.dateText}
                                              key={key}
                                            >
                                              <Text>Date:{dateOnly}</Text>
                                            </View>
                                          );
                                        }
                                        return null;
                                      })}
                                    </View>
                                  </View>
                                );
                              }
                            );
                          }

                          // No data available for iterative section
                          return null;
                        }

                        // Handle non-iterative (single instance) sections
                        return (
                          <View
                            style={[styles.section, { marginBottom: 20 }]}
                            key={`${sectionIndex + 1}`}
                          >
                            {/* Section title without index */}
                            <Text style={styles.sectionTitle}>
                              {sectionDetails.group_title}
                            </Text>

                            {/* Render fields */}
                            {sectionDetails.fields.map(
                              (field: any, fieldIndex: any) => {
                                const value =
                                  formResponseValues?.[
                                    sectionDetails.group_key
                                  ]?.[field.name] || 'N/A';
                                const isBase64Image =
                                  typeof value === 'string' &&
                                  value.startsWith('data:image/');
                                const displayValue =
                                  typeof value === 'string' && !isBase64Image
                                    ? value
                                    : 'N/A';

                                // Render signature fields in pairs
                                if (field.type === 'signature') {
                                  signatures.push({
                                    label: field.label,
                                    value: value || null
                                  });

                                  if (signatures.length === 2) {
                                    const tempSignatures = [...signatures];
                                    signatures.length = 0;
                                    return (
                                      <SignatureRow
                                        key={`signature-row-${fieldIndex + 1}`}
                                        signatures={tempSignatures}
                                      />
                                    );
                                  }
                                  return null;
                                }

                                // Render checkbox options
                                if (field.input_type === 'checkbox') {
                                  return (
                                    <View key={`${fieldIndex + 1}`}>
                                      <Text style={styles.label}>
                                        {field.label}
                                      </Text>
                                      <View
                                        style={{
                                          flexDirection: field.label
                                            ? 'column'
                                            : 'row',
                                          flexWrap: field.label
                                            ? 'nowrap'
                                            : 'wrap',
                                          marginLeft: field.label
                                            ? '43.5%'
                                            : 29,
                                          gap: 5
                                        }}
                                      >
                                        {field.options.map(
                                          (option: any, optionIndex: any) => (
                                            <View
                                              style={{
                                                flexDirection: 'row',
                                                alignItems: 'flex-start',
                                                marginBottom: 18,
                                                width: field.label
                                                  ? 'auto'
                                                  : '30%',
                                                marginRight: field.label
                                                  ? 0
                                                  : 10
                                              }}
                                              key={`${optionIndex + 1}`}
                                            >
                                              <Checkbox
                                                checked={formResponseValues?.[
                                                  sectionDetails.group_key
                                                ]?.[field.name]?.includes(
                                                  option.value
                                                )}
                                              />
                                              <Text style={styles.value}>
                                                {option.label}
                                              </Text>
                                            </View>
                                          )
                                        )}
                                      </View>
                                    </View>
                                  );
                                }

                                // Render other field types
                                return (
                                  <View
                                    style={[styles.field, { marginBottom: 25 }]}
                                    key={`${fieldIndex + 1}`}
                                  >
                                    <Text style={styles.label}>
                                      {field.label}
                                    </Text>
                                    {isBase64Image ? (
                                      <Image src={value} style={styles.image} />
                                    ) : (
                                      <Text style={styles.value}>
                                        {displayValue}
                                      </Text>
                                    )}
                                  </View>
                                );
                              }
                            )}

                            {/* Render any remaining unpaired signature */}
                            {signatures.length > 0 && (
                              <SignatureRow signatures={signatures} />
                            )}
                            {/* Render extra _date fields */}
                            <View style={styles.dateRowContainer}>
                              {Object.entries(
                                formResponseValues?.[
                                  sectionDetails.group_key
                                ] || {}
                              ).map(([key, val]: any) => {
                                // Only render if key ends with _date and is not part of defined fields
                                const isDateField = key.endsWith('_date');
                                const dateOnly =
                                  typeof val === 'string'
                                    ? val.slice(0, 10)
                                    : val;
                                const isAlreadyRendered =
                                  sectionDetails.fields.some(
                                    (field: any) => field.name === key
                                  );

                                if (isDateField && !isAlreadyRendered) {
                                  return (
                                    <View style={styles.dateText} key={key}>
                                      <Text>Date:{dateOnly}</Text>
                                    </View>
                                  );
                                }
                                return null;
                              })}
                            </View>
                          </View>
                        );
                      }
                    )}
                  </View>
                );
              })}
            </View>
          }
          <View
            style={[
              styles.footer,
              {
                height: footerHeight,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center'
              }
            ]}
            fixed
          >
            {footerText && (
              <Text style={[styles.footerText, { marginRight: 0 }]}>
                {footerText}
              </Text>
            )}
            <Text
              style={[
                styles.pageNumberStyles,
                alignPageTextStyles[alignPageNumberText]
              ]}
              render={({ pageNumber, totalPages }) =>
                `${footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
              }
            />
          </View>
        </Page>
      )}
    </Document>
  );
};

export default MyNewPDFComponent;
