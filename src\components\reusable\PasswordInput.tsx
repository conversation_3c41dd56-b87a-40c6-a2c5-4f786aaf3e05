import { Box, IconButton } from '@mui/material';
import { useState } from 'react';
import { FormInput } from '../form.elements';

import { Icon } from './Icon';
import '../../css/index.scss';

export const PasswordInput = ({
  name,
  label,
  containerStyles,
  required
}: {
  name: string,
  label: string,
  containerStyles?: any,
  required: boolean
}) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);
  return (
    <Box className="position-relative">
      <FormInput
        id={name}
        name={name}
        label={label}
        required={required}
        type={showPassword ? 'text' : 'password'}
        placeholder="Enter Your Password"
        containerStyles={{
          width: '100%',
          height: '45px',
          marginTop: '0px',
          '& .MuiInputBase-formControl': {
            marginTop: '4px'
          },
          ...containerStyles
        }}
        autoComplete="off"
      />
      {showPassword ? (
        <IconButton
          onClick={() => setShowPassword(false)}
          className="position-absolute"
          sx={{
            right: '10px',
            top: '32px'
          }}
        >
          <Icon name="Visibility" color="primary" />
        </IconButton>
      ) : (
        <IconButton
          onClick={() => setShowPassword(true)}
          className="position-absolute"
          sx={{
            right: '10px',
            top: '32px'
          }}
        >
          <Icon name="VisibilityOff" color="primary" />
        </IconButton>
      )}
    </Box>
  );
};
export default PasswordInput;
