/* eslint-disable react/destructuring-assignment */
import { toast } from 'react-toastify';
import LoadingButton from '@mui/lab/LoadingButton';

import { useSelector, useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { PDFViewer, Font } from '@react-pdf/renderer';
import { Box } from '@mui/material';
import MyNewPDFComponent from './MyNewPdfComponent';
import { RootState } from '../../redux/reducers';
import FormResponseData from './form_response.json';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  postpdfconfig,
  updatepdfconfig
} from '../../redux/reducers/addressconfig.reducer';

// import { MyPDFDocument } from './MyPDFComponent';

Font.registerEmojiSource({
  format: 'png',
  url: 'https://cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/72x72/%27'
});

export default function PdfFormResponse(props: {
  orientationValue: string;
  sizeValue: string;
  breakValue: string;
  marginValue: string;
  logoAlignment: any;
  currentColor: any;
  colorScheme: any;
  textColor: any;
  fontFamily: string;
  fontSize: string;
  // alignText: string;
  insertLogo: any;
  headerTitle: any;
  watermarkText: any;
  watermarkImage: any;
  alignPageNumberText: string;
  headerHeight: any;
  // headerWidth: any;
  footerHeight: any;
  footerText: string;
}) {
  // const [rows, setRows] = useState<any>([]);
  // const [numberofUsers, setNumberofUsers] = React.useState('');
  // const [selectedUser, setSelectedUser] = React.useState<any>(null);

  const { clientForms } = useSelector((state: RootState) => state.clients);
  const formResponseData: any = FormResponseData;
  const { pdfConfig }: any = useSelector(
    (state: AppState) => state.addressconfig
  );
  // const { organization } = useSelector((state: RootState) => state.org);
  // console.log('user', user);
  // console.log(orgData.name);

  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();

  // const isDummyData = location.pathname?.includes('/pdf-configuration');
  // Fetch all user data initially
  // React.useEffect(() => {
  //   // const fetchUsers = async () => {
  //   //   const response = await fetch("https://dummyjson.com/users");
  //   //   const data = await response.json();
  //   //   setRows(data.users);
  //   // };.
  //   // fetchUsers();
  //   //   const response = await fetch(`https://dummyjson.com/users/${userId}`);
  //   const fetchUsers = async () => {
  //     const response = await fetch(
  //       `https://klezaformsapiv3.klezaclients.net/form-value?client_id=e20f1a02-f173-446f-8545-ddf08e5d8d7e&app_id=7b0a3b32-8ce6-407e-821d-90bbafa967a9&organization_id=2132004a-3e29-486e-87fc-1aae30584e8b`
  //     );

  //     //   const response = await fetch(
  //     //     'https://klezaformsapiv3.klezaclients.net/form-value?client_id=971ee32e-19e6-4552-98cd-3952f14aaa01&app_id=1731ae98-ac3f-41a9-bdce-0fd34afbf864'
  //     //   );
  //     const data = await response.json();
  //     setNumberofUsers('singleuserdetails');
  //     setSelectedUser(data);
  //   };
  //   fetchUsers();
  // }, []);

  const handleSave = async () => {
    const data = {
      configuration_id: pdfConfig ? pdfConfig?.configuration_id : '',
      name: 'print', // Mention Name "google or open_weather"
      type: 'pdf',
      details: {
        orientation: props.orientationValue,
        size: props.sizeValue,
        pageBreaks: props.breakValue,
        margin: props.marginValue,
        // uploadLogo: props.insertLogo,
        backgroundColor: props.currentColor,
        textColor: props.textColor,
        headerHeight: props.headerHeight,
        footerHeight: props.footerHeight,
        fontFamily: props.fontFamily,
        fontSize: props.fontSize,
        watermarkText: props.watermarkText
        // headerTitle: props.headerTitle,
        // footerText: props.footerText
      }
    };

    if (pdfConfig?.configuration_id) {
      try {
        const response = await dispatch(
          updatepdfconfig({
            id: pdfConfig?.configuration_id,
            data
          })
        );
        if (response.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.error,
          //   type: 'error'
          // });
          toast.error(response.payload?.error);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    } else {
      try {
        const response = await dispatch(postpdfconfig(data));
        if (response.payload.status) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response.payload?.message,
          //   type: 'success'
          // });
          toast.success(response.payload?.message);
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message: 'Something went wrong!',
          //   type: 'error'
          // });
          toast.error(response.payload?.message || 'Something went wrong!');
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  };
  return (
    // <Paper sx={{ width: '100%', overflow: 'hidden', overflowX: 'hidden' }}>
    <Box sx={{ width: '100%', overflow: 'hidden', overflowX: 'hidden' }}>
      <PDFViewer
        style={{
          width: '100%', // Take full container width
          height: '100vh',
          overflow: 'hidden'
        }}
      >
        <MyNewPDFComponent
          formResponses={
            location.pathname?.includes('/view-pdf')
              ? clientForms
              : formResponseData.data
          }
          // user={clientForms}
          // isDummyData={isDummyData}
          orientationValue={props.orientationValue}
          sizeValue={props.sizeValue}
          breakValue={props.breakValue}
          marginValue={props.marginValue}
          logoAlignment={props.logoAlignment}
          colorScheme={props.colorScheme}
          backgroundColor={props.currentColor}
          fontFamily={props.fontFamily}
          fontSize={props.fontSize}
          watermarkText={props.watermarkText}
          // alignText={props.alignText}
          alignPageNumberText={props.alignPageNumberText}
          insertLogo={props.insertLogo}
          headerTitle={props.headerTitle}
          textColor={props.textColor}
          watermarkImage={props.watermarkImage}
          headerHeight={props.headerHeight}
          // headerWidth={props.headerWidth}
          footerHeight={props.footerHeight}
          footerText={props.footerText}
        />
      </PDFViewer>
      {location.pathname?.includes('/pdf-configuration') && (
        <LoadingButton
          variant="contained"
          color="primary"
          title={pdfConfig?.configuration_id ? 'Update' : 'Save'}
          onClick={handleSave}
          sx={{
            backgroundColor: 'primaryBlue.main',
            color: 'white2.main',
            padding: '10px 30px',
            boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
            '&:hover': {
              color: 'white2.main',
              backgroundColor: 'primaryBlue.main'
            },
            alignItems: 'flex-end'
          }}
        >
          {pdfConfig?.configuration_id ? 'Update' : 'Save'}
        </LoadingButton>
      )}
    </Box>
    // </Paper>
  );
}
