import { Divider, IconButton, InputBase, Paper } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import '../../css/index.scss';

export const SearchWithFilters = () => {
  return (
    <Paper
      elevation={0}
      component="form"
      className="d-flex bg-FAF9F8"
      sx={{
        p: '2px 4px',
        width: 600,
        borderRadius: '50px',
        border: '1px solid #f1f1f1'
      }}
    >
      <IconButton
        type="button"
        sx={{ p: '10px', color: '#0483BA' }}
        aria-label="search"
      >
        <SearchIcon />
      </IconButton>
      <Divider sx={{ height: 44 }} orientation="vertical" />
      <InputBase
        sx={{ ml: 1, flex: 1 }}
        placeholder=""
        inputProps={{ 'aria-label': '.....' }}
      />
      <Divider sx={{ height: 44 }} orientation="vertical" />
      <IconButton sx={{ p: '10px', color: '#0483BA' }} aria-label="menu">
        <FilterListIcon sx={{ color: '#0483BA' }} />
      </IconButton>
    </Paper>
  );
};
export default SearchWithFilters;
