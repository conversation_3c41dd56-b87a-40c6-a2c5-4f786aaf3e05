import { View, Text, Image, StyleSheet } from '@react-pdf/renderer';

// Styles for the SignatureRow
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  signatureContainer: {
    width: '45%'
    // textAlign: 'center'
  },
  label: {
    fontSize: 12,
    // marginBottom: 5
    marginTop: 5
  },
  signature: {
    width: '100%',
    height: 50,
    objectFit: 'contain',
    border: '1px solid black'
  },
  noSignature: {
    fontSize: 8,
    color: 'gray'
  }
});

// Reusable SignatureRow Component
const SignatureRow = (signatureProps: any) => (
  <View style={styles.row}>
    {signatureProps?.signatures?.map((signature: any, index: number) => (
      <View style={styles.signatureContainer} key={`${index + 1}`}>
        {signature.value ? (
          <Image style={styles.signature} src={signature.value} />
        ) : (
          <Text style={styles.noSignature}>No Signature</Text>
        )}
        <Text style={styles.label}>{signature.label}</Text>
      </View>
    ))}
    {/* Fill empty space if only one signature is provided */}
    {signatureProps?.signatures?.length === 1 && (
      <View style={styles.signatureContainer} />
    )}
  </View>
);

export default SignatureRow;
