import { Alert, Snackbar } from '@mui/material';

export const SnackbarElement = ({
  snackbarOpen,
  setSnackbarOpen,
  message,
  statusType
}: {
  snackbarOpen: any,
  setSnackbarOpen: any,
  message: any,
  statusType: 'success' | 'error' | 'warning' | 'info'
}) => {
  return (
    <Snackbar
      open={snackbarOpen}
      autoHideDuration={3000}
      onClose={setSnackbarOpen}
      // onClose={() => setSnackbarOpen(false)}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
    >
      <Alert
        // onClose={setSnackbarOpen}
        // onClose={() => setSnackbarOpen(false)}
        severity={statusType}
      >
        {message}
      </Alert>
    </Snackbar>
  );
};
export default SnackbarElement;
