import { Box, Button } from '@mui/material';
import React, { useState } from 'react';

interface VideoUploaderProps {
  handleVideoUrlInput: (url: string) => void;
}

const VideoUploader: React.FC<VideoUploaderProps> = ({
  handleVideoUrlInput
}) => {
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [videoUrlErrorMessage, setVideoUrlErrorMessage] = useState<string>('');

  const addVideo = (): void => {
    if (videoUrl) {
      const regExp =
        /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=|\?v=)([^#&?]*).*/;
      const match = videoUrl.match(regExp);

      if (match && match[2].length === 11) {
        setVideoUrlErrorMessage('');
        handleVideoUrlInput(
          `https://www.youtube.com/embed/${match[2]}?autoplay=1&enablejsapi=1`
        );
      } else {
        setVideoUrlErrorMessage('Please Enter a valid YouTube URL');
      }
    }
  };

  return (
    <Box>
      <input
        type="text"
        value={videoUrl}
        onChange={(e) => setVideoUrl(e.target.value)}
        placeholder="Paste YouTube URL Here"
        style={{
          height: '35px',
          marginRight: '10px',
          padding: '0px 10px'
        }}
      />
      <Button variant="contained" onClick={addVideo}>
        Add Video
      </Button>
      {videoUrlErrorMessage && (
        <p style={{ color: 'red' }}>{videoUrlErrorMessage}</p>
      )}
    </Box>
  );
};

export default VideoUploader;
