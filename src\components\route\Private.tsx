import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { getStorageItem } from '../../utils';

const Private: React.FC<{ children: JSX.Element }> = ({ children }) => {
  const isAuthenticated = !!getStorageItem('access_token');
  const location = useLocation();
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  return children;
};

export default Private;
