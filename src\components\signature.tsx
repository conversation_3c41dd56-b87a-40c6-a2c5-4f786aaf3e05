/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { Close, Check } from '@mui/icons-material';
import { Stack, Button, useTheme } from '@mui/material';
import SignatureCanvas from 'react-signature-canvas';

import { SignatureType } from '../types';

const Signature: React.FC<SignatureType> = ({ onClear, onDone, ...props }) => {
  const theme = useTheme();
  const signatureRef = React.useRef<SignatureCanvas>(null);

  const clearSignature = () => {
    signatureRef.current?.clear();
    onClear();
  };

  const done = () => {
    onDone(signatureRef.current?.toDataURL(), signatureRef.current?.isEmpty());
  };

  return (
    <div>
      <SignatureCanvas ref={signatureRef} {...props} />
      <Stack
        direction="row"
        spacing={2}
        sx={{
          padding: 1,
          borderTop: `2px solid ${theme.palette.divider}`
        }}
        justifyContent="flex-end"
      >
        <Button
          variant="outlined"
          sx={{ textTransform: 'capitalize' }}
          endIcon={<Close />}
          onClick={clearSignature}
        >
          Clear
        </Button>
        <Button
          variant="outlined"
          sx={{ textTransform: 'capitalize' }}
          endIcon={<Check />}
          onClick={done}
        >
          Save
        </Button>
      </Stack>
    </div>
  );
};

export default Signature;
