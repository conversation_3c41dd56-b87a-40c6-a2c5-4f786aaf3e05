// Global Imports
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  IconButton,
  Tooltip,
  Typography
} from '@mui/material';
import * as Yup from 'yup';
import { useNavigate, useParams } from 'react-router-dom';
import {
  // useEffect
  useMemo,
  useState
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
// Local Imports
import {
  AppForm,
  FormInput,
  FormPhoneInput,
  FormSelect,
  SubMenu,
  SubmitButton
} from '../form.elements';

import { AppDispatch, AppState } from '../../redux/app.store';
import {
  createuser,
  getorgapps,
  updateuser
} from '../../redux/reducers/user.reducer';
import '../../css/user-registration-styles.scss';
import { PasswordInput } from '../reusable/PasswordInput';
// import { SnackbarElement } from '../reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';
import Shell from '../layout/Shell';

const UserRegistration = () => {
  const navigate = useNavigate();
  const userType = localStorage.getItem('user_type');
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { user, orgList, orgApps, selectedOrg }: any = useSelector(
    (state: AppState) => state.user
  );
  const [selectedApps, setSelectedApps]: any = useState([]);
  const [appsErrorMessage, setAppsErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false); // Add loading state
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });

  const validatePassword = () => {
    if (!id) {
      return Yup.string().required('Password is required');
    }
    return Yup.string().optional();
  };

  const validationSchema = Yup.object().shape({
    organization: Yup.string()
      .trim()
      .strict(true)
      .required('Organisation is required'),
    name: Yup.string().trim().strict(true).required('Name is required'),
    email: Yup.string()
      .matches(/^[\w-\\.]+@([\w-]+\.)+[\w-]{2,4}$/, 'Enter a valid email')
      .required('Email is required'),
    password: validatePassword(),
    mobile_number: Yup.string().required('Mobile Number is required')
  });

  const handelSubmit = async (event: any) => {
    if (selectedApps.length > 0) {
      setAppsErrorMessage('');
      setIsLoading(true);
      const data =
        event.password !== ''
          ? {
              organization: event.organization,
              email: event.email,
              password: event.password,
              name: event.name,
              mobile_number: `${event.mobile_number}`,
              role: event.role,
              apps: selectedApps
            }
          : {
              organization: event.organization,
              email: event.email,
              name: event.name,
              mobile_number: `${event.mobile_number}`,
              role: event.role,
              apps: selectedApps
            };
      try {
        let response: any;

        if (id) {
          response = await dispatch(updateuser({ id, data }));
        } else {
          response = await dispatch(createuser(data));
        }

        if (response?.payload?.status) {
          // setSnackbarOpen({
          //   status: true,
          //   message: response?.payload?.message || 'User Updated Successfully.',
          //   type: 'success'
          // });
          // toast.success(
          //   response?.payload?.message || 'User Updated Successfully.'
          // );
          setIsLoading(false);
          setTimeout(() => {
            navigate('/users');
          }, 1000);
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message:
          //     response?.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.',
          //   type: 'error'
          // });

          const errorMessage = response?.payload?.message;
          if (Array.isArray(errorMessage) && errorMessage.length > 0) {
            errorMessage.forEach((error: any) => {
              toast.error(error);
            });
          } else {
            toast.error(errorMessage || 'An error occurred. Please try again.');
          }
        }

        setIsLoading(false);
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    } else {
      setIsLoading(false); // Stop loader
      setAppsErrorMessage('Please select at least one app!');

      // setSnackbarOpen({
      //   status: true,
      //   message: 'Select any app!',
      //   type: 'error'
      // });
      toast.error('Select any app!');
    }
  };

  const getOrgApps = async (org_app_id: string) => {
    try {
      const response = await dispatch(getorgapps(org_app_id));
      if (response.payload.status) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     response?.payload?.message || 'Organization selected successfully.',
        //   type: 'success'
        // });
        // toast.success(
        //   response?.payload?.message || 'Organization selected successfully.'
        // );
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     response?.payload?.error ||
        //     'Something Went Wrong, Please Try Again Later.',
        //   type: 'error'
        // });
        toast.error(
          response?.payload?.error ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.',
      //   type: 'error'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };
  const handleSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    getOrgApps(value);
  };

  // function to close snackbar
  // const handleSnackbarClose = () => {
  //   setSnackbarOpen({
  //     status: false,
  //     message: '',
  //     type: 'success'
  //   });
  // };

  // useEffect to automatically close the snackbar
  // useEffect(() => {
  //   if (snackbarOpen.status) {
  //     const timer = setTimeout(() => {
  //       handleSnackbarClose();
  //     }, 3000); // 3 seconds

  //     return () => clearTimeout(timer); // Cleanup the timer
  //   }
  //   return () => {};
  // }, [snackbarOpen]);

  const selectApp = async (appId: string) => {
    if (selectedApps.includes(appId)) {
      setSelectedApps(
        selectedApps.filter((existedAppId: any) => existedAppId !== appId)
      );
    } else {
      setSelectedApps([...selectedApps, appId]);
    }
  };

  // Filter apps to show only HC_CARE apps for caregiver assignment
  const filteredOrgApps = useMemo(() => {
    return orgApps.filter(
      (app: any) => app?.industry_app_process?.process_code !== 'HC_CARE'
    );
  }, [orgApps]);

  useMemo(() => {
    if (id) {
      setSelectedApps(user?.apps);
    }
  }, [id, user]);

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      <Box
        sx={{
          padding: '20px 100px'
        }}
      >
        <Box className="main-conatiner">
          <Typography
            variant="h5"
            component="div"
            className="title"
            sx={{ color: '#595959' }}
          >
            User Registration Form
          </Typography>
          <AppForm
            initialValues={user}
            validationSchema={validationSchema}
            onSubmit={handelSubmit}
          >
            <Box sx={{ padding: '0px 30px' }}>
              <Grid container columnSpacing={4} className="form-grid">
                <Grid item xs={6}>
                  <Box className="field-input">
                    {selectedOrg?.name ? (
                      <FormInput
                        name=""
                        disabled
                        label="Selected Organization"
                        value={selectedOrg?.name}
                        containerStyles={{
                          width: {
                            xs: '100%'
                          }
                        }}
                      />
                    ) : (
                      <FormSelect
                        disabled={Boolean(
                          userType !== 'super_admin' ||
                            (userType === 'super_admin' && id)
                        )}
                        name="organization"
                        data={
                          userType === 'super_admin'
                            ? orgList?.map((org: any) => {
                                return {
                                  value: org?.organization_id,
                                  label: org?.name
                                };
                              })
                            : [
                                {
                                  value: selectedOrg?.organization_id,
                                  label: selectedOrg?.name
                                }
                              ]
                        }
                        onChange={handleSelect}
                        label="Select Organization"
                        required={userType === 'super_admin'}
                        labelStyles={{
                          color:
                            userType === 'super_admin' ? '#000' : '#7E949D',
                          fontSize: '18px',
                          fontWeight: 400
                        }}
                        placeholder="Select Organization"
                        containerStyles={{
                          width: '100%'
                        }}
                      />
                    )}
                  </Box>
                </Grid>
                <Grid item xs={6} />
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormInput
                      name="name"
                      label="Name"
                      required
                      placeholder="Enter User Name"
                      autoComplete="off"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormPhoneInput
                      name="mobile_number"
                      label="Mobile Number"
                      required
                      containerStyles={{
                        width: '100%'
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormInput
                      name="email"
                      label="Email"
                      required={!id}
                      // disabled={!!id}
                      type="email"
                      placeholder="Enter User Email"
                      autoComplete="off"
                      containerStyles={{
                        width: '100%',
                        marginTop: '4px'
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <PasswordInput
                      name="password"
                      label="Password"
                      required={!id}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Box className="errorMessage">{appsErrorMessage}</Box>
                  {filteredOrgApps.length > 0 && (
                    <Box className="apps-box">
                      <Typography className="apps-typo">Apps</Typography>
                      <Grid
                        container
                        columnSpacing={4}
                        rowSpacing={4}
                        sx={{
                          maxHeight: '400px'
                        }}
                      >
                        {filteredOrgApps?.map((app: any) => {
                          const selected = selectedApps?.find(
                            (s: any) => s === app?.app_id
                          );
                          return (
                            <Grid
                              item
                              xs={12}
                              sm={12}
                              md={6}
                              lg={4}
                              key={app.app_id}
                            >
                              <Card
                                onClick={() => selectApp(app?.app_id)}
                                sx={{
                                  padding: 0,
                                  width: '100%',
                                  height: '80px',
                                  cursor: 'pointer',
                                  background: '#F9F9F9',
                                  border: selected
                                    ? '1px solid #36C0ED'
                                    : '1px solid transparent',
                                  transition: 'all 0.3s ease-in-out',
                                  '&:hover': {
                                    backgroundColor: 'background.default',
                                    border: '1px solid',
                                    borderColor: 'primary.main',
                                    transform: 'scale(1.05)',
                                    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
                                  }
                                }}
                              >
                                <CardContent
                                  sx={{
                                    display: 'grid',
                                    gridTemplateColumns: '2fr 8fr 2fr',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    // flexWrap: 'wrap',
                                    // width: '100%',
                                    py: 2 // vertical padding
                                  }}
                                >
                                  {/* Left Portion: Icon and Text */}

                                  <Box
                                    component="img"
                                    src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
                                      app?.name || 'App Name'
                                    )}&size=128&rounded=true`}
                                    alt={app?.name}
                                    sx={{
                                      width: '50px',
                                      height: '50px',
                                      paddingRight: '8px'
                                    }}
                                  />

                                  <Tooltip
                                    title={
                                      selected
                                        ? app?.orgAppConfiguration?.app_name ||
                                          app?.name
                                        : app?.name
                                    }
                                    arrow
                                  >
                                    <Typography
                                      sx={{
                                        fontSize: '16px',
                                        fontWeight: '500',
                                        color: '#595959',
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis'
                                        // width: '120px'
                                      }}
                                    >
                                      {app?.orgAppConfiguration?.app_name ||
                                        app?.name}
                                    </Typography>
                                  </Tooltip>
                                  {/* Right Portion: Selection Checkmark */}
                                  <IconButton
                                    sx={{
                                      color: selected ? '#395e88' : '#E0E0E0',
                                      p: 1 // increase click area
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      selectApp(app?.app_id);
                                    }}
                                  >
                                    {selected ? (
                                      <CheckCircleIcon
                                        sx={{ fontSize: '2rem' }}
                                      />
                                    ) : (
                                      <RadioButtonUncheckedIcon
                                        sx={{ fontSize: '2rem' }}
                                      />
                                    )}
                                  </IconButton>
                                </CardContent>
                              </Card>
                            </Grid>
                          );
                        })}
                      </Grid>
                    </Box>
                  )}
                </Grid>
              </Grid>
              <Box className="button-container">
                <span>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/users')}
                    sx={{
                      backgroundColor: 'white2.main',
                      color: 'primaryBlue.main',
                      padding: '10px 20px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      textTransform: 'capitalize',
                      '&:hover': {
                        color: 'primaryBlue.main',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        backgroundColor: 'rgba(4, 131, 186, 0.04)'
                      }
                    }}
                  >
                    CANCEL
                  </Button>
                </span>
                <span style={{ marginLeft: '15px', marginBottom: '-3px' }}>
                  <SubmitButton
                    title={id ? 'Update' : 'Save'}
                    isLoading={isLoading}
                    sx={{
                      backgroundColor: '#37C0EC',
                      color: 'white2.main',
                      padding: '10px 30px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      '&:hover': {
                        color: 'white2.main',
                        backgroundColor: '#75E7DB',
                        boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                      }
                    }}
                  />
                </span>
              </Box>
            </Box>
          </AppForm>
          {/* <SnackbarElement
            message={snackbarOpen.message}
            statusType={snackbarOpen.type || 'success'}
            snackbarOpen={snackbarOpen.status}
            setSnackbarOpen={undefined}
          /> */}
        </Box>
      </Box>
    </Shell>
  );
};

export default UserRegistration;
