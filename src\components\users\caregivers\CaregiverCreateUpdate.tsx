// Global Imports
import { useEffect, useState } from 'react';
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  Grid,
  TextField,
  Typography
} from '@mui/material';
import * as Yup from 'yup';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import {
  AppForm,
  FormInput,
  FormPhoneInput,
  SubMenu,
  SubmitButton
} from '../../form.elements';

import { AppDispatch, AppState } from '../../../redux/app.store';
import '../../../css/user-registration-styles.scss';
import { PasswordInput } from '../../reusable/PasswordInput';
import Shell from '../../layout/Shell';
import {
  createCaregiver,
  updateCaregiver,
  resetCaregiverDetails
} from '../../../redux/reducers/caregivers.reducer';

export interface AssessmentClients {
  client_id: string;
  mobile_number: string;
  name: string;
}

const CaregiverCreateUpdate = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const { caregiverDetails, isLoading }: any = useSelector(
    (state: AppState) => state.caregivers
  );

  const { assessmentClients }: any = useSelector(
    (state: AppState) => state.clients
  );

  const [client, setClient] = useState<AssessmentClients[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const validatePassword = () => {
    if (!id) {
      return Yup.string().required('Password is required');
    }
    return Yup.string().optional();
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().strict(true).required('Name is required'),
    email: Yup.string()
      .matches(/^[\w-\\.]+@([\w-]+\.)+[\w-]{2,4}$/, 'Enter a valid email')
      .required('Email is required'),
    password: validatePassword(),
    mobile_number: Yup.string().required('Mobile Number is required')
  });

  const handelSubmit = async (event: any) => {
    // Prevent duplicate submissions
    if (isSubmitting || isLoading) {
      return;
    }

    setIsSubmitting(true);

    const selectedClientIds = client.map((c) => c.client_id);

    const data =
      event.password !== ''
        ? {
            organization: event.organization,
            email: event.email,
            password: event.password,
            name: event.name,
            mobile_number: `${event.mobile_number}`,
            clients: selectedClientIds
          }
        : {
            organization: event.organization,
            email: event.email,
            name: event.name,
            mobile_number: `${event.mobile_number}`,
            clients: selectedClientIds
          };

    try {
      let response: any;

      if (id) {
        response = await dispatch(updateCaregiver({ id, data }));
      } else {
        response = await dispatch(createCaregiver(data));
      }

      if (response?.payload?.status) {
        setTimeout(() => {
          navigate('/users/caregivers');
        }, 1000);
      } else {
        const errorMessage = response?.payload?.message;
        if (Array.isArray(errorMessage) && errorMessage.length > 0) {
          errorMessage.forEach((error: any) => {
            toast.error(error);
          });
        } else {
          toast.error(errorMessage || 'An error occurred. Please try again.');
        }
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  const handleClients = (e: any, value: AssessmentClients[]) => {
    console.log(e?.target?.value);
    setClient(value);
  };

  useEffect(() => {
    if (
      id &&
      caregiverDetails?.clients &&
      Array.isArray(caregiverDetails.clients)
    ) {
      const selectedClients = assessmentClients.filter(
        (mapClient: AssessmentClients) =>
          caregiverDetails.clients.includes(mapClient?.client_id)
      );
      setClient(selectedClients);
    }

    if (!id) {
      dispatch(resetCaregiverDetails());
    }
  }, [assessmentClients, caregiverDetails, id]);

  return (
    <Shell subMenu={getSubMenu()}>
      <Box
        sx={{
          padding: '20px 100px'
        }}
      >
        <Box className="main-conatiner">
          <Typography
            variant="h5"
            component="div"
            className="title"
            sx={{ color: '#595959' }}
          >
            Caregiver Registration Form
          </Typography>
          <AppForm
            initialValues={caregiverDetails}
            validationSchema={validationSchema}
            onSubmit={handelSubmit}
          >
            <Box sx={{ padding: '0px 30px' }}>
              <Grid container columnSpacing={4} className="form-grid">
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormInput
                      name="name"
                      label="Name"
                      required
                      placeholder="Enter User Name"
                      autoComplete="off"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormPhoneInput
                      name="mobile_number"
                      label="Mobile Number"
                      required
                      containerStyles={{
                        width: '100%'
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormInput
                      name="email"
                      label="Email"
                      required={!id}
                      // disabled={!!id}
                      type="email"
                      placeholder="Enter User Email"
                      autoComplete="off"
                      containerStyles={{
                        width: '100%',
                        marginTop: '4px'
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <PasswordInput
                      name="password"
                      label="Password"
                      required={!id}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  Select Clients
                  <Autocomplete
                    multiple
                    options={assessmentClients}
                    getOptionLabel={(option) =>
                      `${option.name} | ${option.mobile_number} `
                    }
                    value={client}
                    onChange={handleClients}
                    isOptionEqualToValue={(option, value) =>
                      option.client_id === value.client_id
                    }
                    renderTags={(value: AssessmentClients[], getTagProps) =>
                      value.map((option: AssessmentClients, index: number) => (
                        <Chip
                          {...getTagProps({ index })}
                          // variant="outlined"
                          label={`${option.name} | ${option.mobile_number}`}
                          key={`${index + 1}`}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        // variant="outlined"
                        // label="Select Currencies"
                        placeholder="Choose..."
                      />
                    )}
                  />
                </Grid>
              </Grid>
              <Box className="button-container">
                <span>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/users')}
                    sx={{
                      backgroundColor: 'white2.main',
                      color: 'primaryBlue.main',
                      padding: '10px 20px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      textTransform: 'capitalize',
                      '&:hover': {
                        color: 'primaryBlue.main',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        backgroundColor: 'rgba(4, 131, 186, 0.04)'
                      }
                    }}
                  >
                    CANCEL
                  </Button>
                </span>
                <span style={{ marginLeft: '15px', marginBottom: '-3px' }}>
                  <SubmitButton
                    title={id ? 'Update' : 'Save'}
                    isLoading={isLoading || isSubmitting}
                    sx={{
                      backgroundColor: '#37C0EC',
                      color: 'white2.main',
                      padding: '10px 30px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      '&:hover': {
                        color: 'white2.main',
                        backgroundColor: '#75E7DB',
                        boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                      }
                    }}
                  />
                </span>
              </Box>
            </Box>
          </AppForm>
        </Box>
      </Box>
    </Shell>
  );
};

export default CaregiverCreateUpdate;
