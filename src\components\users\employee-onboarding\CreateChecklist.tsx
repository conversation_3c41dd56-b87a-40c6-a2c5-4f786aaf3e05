// Global Imports
import { useEffect, useRef, useState } from 'react';
import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  IconButton,
  Input
} from '@mui/material';
import { AddCircleOutline, Cancel } from '@mui/icons-material';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import { RootState } from '../../../redux/reducers';
import { Icon } from '../../reusable/Icon';
import { AppDispatch } from '../../../redux/app.store';
import {
  createchecklist,
  deletelistelement,
  getchecklistdata
} from '../../../redux/reducers/user.reducer';
import {
  AppForm,
  FormInput,
  FormSelect,
  SubMenu,
  SubmitButton
} from '../../form.elements';
// import { SnackbarElement } from '../../reusable/SnackbarElement';
import '../../../css/checklist-styles.scss';
import Shell from '../../layout/Shell';
// import { update } from 'lodash';

const DateSection = ({ data }: { data: any }) => (
  <Box sx={{ background: '#FAF9F8', padding: '10px 50px' }}>
    <Box sx={{ display: 'flex', gap: '20px' }}>
      <Typography sx={{ width: '110px' }}>
        {data.label}{' '}
        <span style={{ color: 'red' }}>{data.required ? '*' : ''}</span>
      </Typography>

      <Input
        type={data?.type}
        sx={{ width: '100%' }}
        placeholder={`Enter ${data?.label}`}
        required={data?.required}
      />
    </Box>
  </Box>
);

const CreateChecklist = () => {
  const { checkListData } = useSelector((state: RootState) => state.user);
  const dispatch = useDispatch<AppDispatch>();
  const [list, setList] = useState<any>([]);
  const [clearForm, setClearForm] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // const [snackbarOpen, setSnackbarOpen] = useState<{
  //   status: boolean;
  //   message: string;
  //   type: 'success' | 'error' | 'warning' | 'info';
  // }>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });

  const [inputTypes, setInputTypes] = useState([
    {
      value: 'text',
      label: 'Text'
    },
    {
      value: 'number',
      label: 'Number'
    },
    {
      value: 'date',
      label: 'Date'
    },
    {
      value: 'time',
      label: 'Time'
    }
  ]);

  const [formData, setFormData] = useState({
    title: '',
    inputs: [],
    item_id: ''
  });

  const validationSchema = Yup.object().shape({
    title: Yup.string()?.required('Enter Title')
  });

  useEffect(() => {
    setList(checkListData);
    setFormData({
      title: '',
      inputs: [],
      item_id: ''
    });
  }, [checkListData]);

  useEffect(() => {
    setFormData({
      title: '',
      inputs: [],
      item_id: ''
    });
  }, [clearForm]);

  useEffect(() => {
    setInputTypes(inputTypes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (e: any, values: any) => {
    const { name, value } = e.target;
    const keys = name.split(/[[\].]+/).filter(Boolean);

    const data = {
      title: values?.title,
      item_id: values?.item_id,
      inputs: values?.inputs
    };

    if (name === 'title') {
      data.title = value;
    }
    if (name !== 'title') {
      data.inputs[parseInt(keys[1], 10)][keys[2]] = value;
    }
    setFormData(data);
  };

  const handleSwitchChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index?: number
  ) => {
    setFormData((prevData: any) => {
      const updatedValues = {
        ...prevData,
        inputs: prevData.inputs.map((input: any, i: number) =>
          i === index ? { ...input, required: event.target.checked } : input
        )
      };

      return updatedValues;
    });
  };

  const handleSubmit = async (values: any) => {
    const isNewItem = values?.item_id === '';
    let updatedChecklist;

    if (isNewItem) {
      updatedChecklist = list?.length > 0 ? [...list, values] : [values];
    } else {
      updatedChecklist = list.map((element: any) =>
        element?.item_id === values?.item_id
          ? { ...element, title: values?.title, inputs: values?.inputs }
          : element
      );
    }

    const data = { checklist: updatedChecklist };

    try {
      const res = await dispatch(createchecklist(data));

      if (res.payload.status) {
        setClearForm(true);

        dispatch(getchecklistdata(null));

        toast.success(
          isNewItem ? res.payload?.message : 'Item updated successfully!'
        );
      } else {
        toast.error(res.payload?.message);
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something went wrong, please try again later'
      );
    }

    // Update the list only after successful API response
    setList(updatedChecklist);
  };
  const handleDelete = async (item: any) => {
    const data = {
      checklist: list.filter((i: any) => i.item_id !== item.item_id)
    };

    try {
      const res = await dispatch(deletelistelement(data));
      if (res.payload.status) {
        setList(data?.checklist);
        toast.success(
          <>
            <div>Item Removed successfully!</div>
            <Button onClick={() => handleSubmit(item)}>Undo</Button>
          </>
        );
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const handleUpdateList = (value: any) => {
    setFormData(value);
  };

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      <Box
        sx={{
          padding: '0px 100px'
        }}
      >
        <Box
          sx={{
            background: '#ffffff'
          }}
        >
          <Box className="checklist-box">
            <AppForm
              key={1}
              initialValues={formData}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              <Box>
                <Box className="add-checklist">
                  <FormInput
                    name="title"
                    ref={inputRef}
                    label=""
                    placeholder="Enter title"
                    handleInputChange={handleInputChange}
                    containerStyles={{ width: '272px', marginRight: '15px' }}
                  />
                  <Box sx={{ padding: '37px 0px 10px 0px' }}>
                    <SubmitButton
                      title={formData?.item_id ? 'UPDATE' : 'ADD'}
                    />
                  </Box>
                </Box>

                {formData?.inputs?.length === 0 && (
                  <Button
                    variant="contained"
                    onClick={() => {
                      setFormData((prevData: any) => ({
                        ...prevData,
                        inputs: [
                          {
                            label: 'Label',
                            type: 'text',
                            required: false
                          }
                        ],
                        title: prevData?.title,
                        item_id: prevData?.item_id
                      }));
                    }}
                    sx={{ marginBottom: 2 }}
                  >
                    Additional Fields
                  </Button>
                )}

                {formData?.inputs?.length !== 0 && (
                  <Box>
                    {formData?.inputs.map((input: any, index: number) => {
                      const labelName = `inputs[${index}].label`;
                      const typeName = `inputs[${index}].type`;
                      const requiredName = `inputs[${input.id}]?.required`;

                      return (
                        <Box className="additional-input" key={`${index + 1}`}>
                          <Box className="inputs-box">
                            <Box>
                              {/* Label Name Input */}
                              <Box sx={{ marginBottom: 2 }}>
                                <FormInput
                                  name={labelName}
                                  label="Label Name"
                                  handleInputChange={handleInputChange}
                                  placeholder="Please Enter Label Name"
                                  containerStyles={{
                                    backgroundColor: '#fff',
                                    width: '100%'
                                  }}
                                />
                              </Box>

                              {/* Input Type Dropdown */}
                              <Box sx={{ marginBottom: 2 }}>
                                <FormSelect
                                  name={typeName}
                                  label="Select Input Type"
                                  placeholder="Select Input Type"
                                  data={inputTypes}
                                  onChange={handleInputChange}
                                  containerStyles={{
                                    width: '100%'
                                  }}
                                />
                              </Box>

                              <FormControlLabel
                                name={requiredName}
                                control={
                                  <Switch
                                    checked={input?.required}
                                    onChange={(
                                      event: React.ChangeEvent<HTMLInputElement>
                                    ) => handleSwitchChange(event, index)}
                                    color="primary"
                                  />
                                }
                                label="Required"
                                sx={{
                                  marginBottom: 2
                                }}
                              />
                            </Box>
                          </Box>

                          <Box
                            sx={{
                              display: 'flex'
                            }}
                          >
                            <Box className="cancel-box">
                              <IconButton
                                onClick={() =>
                                  setFormData((prevData: any) => ({
                                    ...prevData,
                                    inputs: prevData.inputs.filter(
                                      (existingInput: any) =>
                                        existingInput.id !== input.id
                                    )
                                  }))
                                }
                              >
                                <Cancel sx={{ color: '#ff000090' }} />
                              </IconButton>
                            </Box>

                            {formData?.inputs?.length < 2 && (
                              <Box className="add-box">
                                <IconButton
                                  onClick={() =>
                                    setFormData((prevData: any) => ({
                                      ...prevData,
                                      inputs: [
                                        ...prevData.inputs,
                                        {
                                          id: `id-${Date.now()}`, // Unique identifier
                                          label: 'Label',
                                          type: 'text',
                                          required: false
                                        }
                                      ]
                                    }))
                                  }
                                >
                                  <AddCircleOutline sx={{ color: '#0483ba' }} />
                                </IconButton>
                              </Box>
                            )}
                          </Box>
                        </Box>
                      );
                    })}
                  </Box>
                )}
              </Box>
            </AppForm>
          </Box>

          <Box className="list-box">
            {list?.map((checklist: any) => {
              return (
                <Box
                  className="checklist"
                  key={checklist?.item_id}
                  // onClick={() => handleUpdateList(checklist)}
                >
                  <Box key={checklist?.title} className="title">
                    <Typography className="title-typo">
                      {checklist?.title}
                    </Typography>
                    <Box>
                      <IconButton
                        sx={{
                          marginRight: '20px'
                        }}
                        onClick={() => handleUpdateList(checklist)}
                      >
                        <Icon name="Edit" />
                      </IconButton>
                      <IconButton
                        sx={{
                          marginRight: '20px'
                        }}
                        onClick={() => handleDelete(checklist)}
                      >
                        <Icon name="Delete" />
                      </IconButton>
                    </Box>
                  </Box>
                  {checklist?.inputs?.map((input: any) => (
                    <DateSection key={input.id} data={input} />
                  ))}
                </Box>
              );
            })}

            {list?.length === 0 && (
              <Box>
                <Typography className="no-items-typo">
                  Your checklist is currently empty. Add items to get started!
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      {/* <SnackbarElement
        message={snackbarOpen?.message}
        statusType={snackbarOpen?.type}
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Shell>
  );
};
export default CreateChecklist;
