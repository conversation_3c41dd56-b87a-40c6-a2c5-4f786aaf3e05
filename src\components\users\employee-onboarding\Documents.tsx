// Global Imports
import {
  Box,
  IconButton,
  Typography,
  Paper,
  CircularProgress
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';

// Local Imports
import '../../../css/documents-styles.scss';
import { Icon } from '../../form.elements';
import { AppState } from '../../../redux/app.store';

const Documents = () => {
  const { documentsList }: any = useSelector((state: AppState) => state.user);
  const [selectedDocument, setSelectedDocument] = useState<any>(undefined);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (documentsList.length > 0 && selectedDocument === undefined) {
      setLoading(true);
      setTimeout(() => {
        setSelectedDocument(documentsList[0]);
        setLoading(false);
      }, 300); // Simulating loading effect
    }
  }, [documentsList, selectedDocument]);

  const handleDocumentClick = (document: any) => {
    setLoading(true);
    setTimeout(() => {
      setSelectedDocument(document);
      setLoading(false);
    }, 300); // Simulating loading effect
  };

  const renderDocumentPreview = () => {
    if (loading) {
      return (
        <Box className="loading-container">
          <CircularProgress color="primary" />
          <Typography>Loading document...</Typography>
        </Box>
      );
    }

    if (!selectedDocument) {
      return <Typography>Select a document to view</Typography>;
    }

    return selectedDocument?.value?.name?.endsWith('.pdf') ? (
      <iframe
        src={selectedDocument?.value?.file}
        title="PDF Preview"
        className="pdf-preview"
      />
    ) : (
      <img
        src={selectedDocument?.value?.file || '/Images/ImageIconWithoutBG.png'}
        className="image-preview"
        alt="Document Preview"
      />
    );
  };

  return (
    <Box className="main-container">
      {/* Top Section */}
      <Box className="top">
        <Box className="left-box">
          <IconButton
            onClick={() =>
              navigate(`/users/employee-onboarding/checklist/${id}`)
            }
          >
            <Icon fontSize="large" name="ArrowBack" color="primary" />
          </IconButton>

          <Box sx={{ paddingLeft: '10px' }}>
            <Icon name="FilePresent" color="primary" fontSize="medium" />
          </Box>

          <Typography className="documents-typo">Documents</Typography>
          <Typography color="primary" className="document-number-typo">
            ({documentsList?.length || 0})
          </Typography>
        </Box>
      </Box>

      {/* Main Content */}
      <Box className="container">
        {/* Document List */}
        <Paper elevation={3} className="document-list">
          {documentsList.map((document: any, index: number) => (
            <Box
              key={`document-${index + 1}`}
              className={`document-name ${selectedDocument === document ? 'active' : ''}`}
              onClick={() => handleDocumentClick(document)}
              sx={{ overflowWrap: 'anywhere' }}
            >
              <Icon
                name={
                  document?.value?.name?.endsWith('.pdf')
                    ? 'PictureAsPdf'
                    : 'InsertDriveFile'
                }
                color="primary"
              />
              {document?.value?.name?.toUpperCase() || 'N/A'}
            </Box>
          ))}
        </Paper>

        {/* Document Preview */}
        <Paper elevation={3} className="document-preview">
          {renderDocumentPreview()}
        </Paper>
      </Box>
    </Box>
  );
};

export default Documents;
