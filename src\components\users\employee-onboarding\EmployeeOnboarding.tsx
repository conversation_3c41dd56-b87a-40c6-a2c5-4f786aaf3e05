// Global Imports
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  IconButton,
  Modal,
  Tooltip,
  Typography
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import EditIcon from '@mui/icons-material/Edit';
// import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';

// Local Imports
import '../../../css/employee-onboarding-styles.scss';
import {
  // Link,
  useNavigate
} from 'react-router-dom';
import { Widgets } from '@mui/icons-material';
import { AppDispatch, AppState } from '../../../redux/app.store';
import { ActiveSelectionBorderSvg, SubMenu } from '../../form.elements';
import { deleteapplicant } from '../../../redux/reducers/applicant.reducer';
import {
  getonboardapps,
  onboardapplicant
} from '../../../redux/reducers/user.reducer';
import Sidebar from '../../reusable/Sidebar';
// import { SnackbarElement } from '../../reusable/SnackbarElement';
import Shell from '../../layout/Shell';

const EmployeeOnboarding = () => {
  const { employeeList }: any = useSelector((state: AppState) => state.user);
  const dispatch = useDispatch<AppDispatch>();
  const [localEmployeeList, setLocalEmployeeList] = useState(employeeList);
  const [appsData, setAppsData] = useState([]);
  const navigate = useNavigate();

  // const [snackbarOpen, setSnackbarOpen] = useState<{
  //   status: boolean;
  //   message: string;
  //   type: 'success' | 'error' | 'warning' | 'info';
  // }>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [onboardapplicantid, setOnboardapplicantid] = useState('');
  const handleOnboardClick = (e: any, id: any) => {
    e.stopPropagation();
    handleOpen();
    setOnboardapplicantid(id);
  };
  const handleEditClick = (e: React.MouseEvent, id: any) => {
    e.stopPropagation();
    navigate(`/users/employee-onboarding/edit-applicant/${id}`);
  };

  async function handleReject(e: React.MouseEvent, type: any, id: any) {
    e.stopPropagation();
    const formData: any = {
      status: type
    };

    if (id) {
      try {
        const userData = await dispatch(
          deleteapplicant({ id, data: formData })
        );
        if (userData.payload) {
          setLocalEmployeeList((prevList: any) =>
            prevList.filter((employee: any) => employee.id !== id)
          );
        }
        if (userData.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: userData.payload?.error,
          //   type: 'error'
          // });
          toast.error(userData.payload?.error);
        } else if (userData.payload.status) {
          // setSnackbarOpen({
          //   status: true,
          //   message: userData.payload?.message,
          //   type: 'success'
          // });
          // toast.success(userData.payload?.message);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  }
  const handleSubmit = async () => {
    const employData = {
      apps: selectedCards
    };
    try {
      const response = await dispatch(
        onboardapplicant({ id: onboardapplicantid, data: employData })
      );

      if (response.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.error,
        //   type: 'error'
        // });
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.message,
        //   type: 'success'
        // });
        // toast.success(response.payload?.message);
        navigate('/users');
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 950,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4
  };

  const columns: any = [
    {
      field: 'name',
      headerName: 'Name',
      width: 200,
      headerAlign: 'left',
      align: 'left'
    },
    {
      field: 'mobile_number',
      headerName: 'Phone number',
      type: 'number',
      width: 200,
      headerAlign: 'left',
      align: 'left'
    },
    {
      field: 'email',
      headerName: 'Email Address',
      width: 250,
      headerAlign: 'left',
      align: 'left'
    },
    {
      field: 'form_filling_status',
      headerName: 'Form Submission Status',
      width: 250,
      headerAlign: 'left',
      align: 'left'
    },
    {
      field: 'action',
      headerName: 'Action',
      width: 250,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: any) => (
        <Box sx={{ gap: 1, flexWrap: 'wrap', width: '100%' }}>
          <IconButton onClick={(e) => handleEditClick(e, params.id)}>
            <EditIcon />
          </IconButton>
          {params.row.form_filling_status === 'Not Started' && (
            <Button
              variant="contained"
              size="small"
              sx={{
                marginLeft: 1,
                backgroundColor: 'green',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'darkgreen'
                }
              }}
              onClick={(e) => handleOnboardClick(e, params.id)}
            >
              Onboard
            </Button>
          )}
          <Button
            variant="contained"
            color="error"
            size="small"
            onClick={(e) => handleReject(e, 'rejected', params.id)}
            sx={{ marginLeft: 1 }}
          >
            Reject
          </Button>
        </Box>
      )
    }
  ];

  const menuItems = [
    { name: 'Users', url: '/users' },
    // { name: 'Roles', url: '/roles' },
    { name: 'Employee Onboarding', url: '/users/employee-onboarding' },
    { name: 'Client Assessment', url: '/users/client-assessment' }
  ];

  const handleRowClick = (e: any) => {
    navigate(`/users/employee-onboarding/checklist/${e.id}`);
  };

  useEffect(() => {
    setLocalEmployeeList(employeeList);
  }, [employeeList]);

  const getData = async () => {
    try {
      const apps = await dispatch(
        getonboardapps(localStorage.getItem('org_id'))
      );

      if (apps.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: apps.payload?.error,
        //   type: 'error'
        // });
        toast.error(apps.payload?.error);
      } else if (apps.payload.status) {
        setAppsData(apps.payload.data.apps);
        // setSnackbarOpen({
        //   status: true,
        //   message: apps.payload?.message,
        //   type: 'success'
        // });
        // toast.success(apps.payload?.message);
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };
  useMemo(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCardClick = (formId: string) => {
    // Toggle the selection: add to array if not selected, remove if already selected
    setSelectedCards(
      (prevSelectedCards) =>
        prevSelectedCards.includes(formId)
          ? prevSelectedCards.filter((id) => id !== formId) // Deselect if already selected
          : [...prevSelectedCards, formId] // Select if not selected
    );
  };

  const getSubMenu = () => {
    return <SubMenu backNavigation createChecklist createApplicant />;
  };

  const getDrawer = () => {
    return (
      <Sidebar
        menuItems={menuItems}
        children={undefined}
        userType={undefined}
      />
    );
  };

  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      <Box
        sx={{ display: 'flex', overflow: 'hidden', flexDirection: 'column' }}
      >
        <Box sx={{ width: '100%' }}>
          <Box sx={{ backgroundColor: '#FAF9F8' }}>
            <div>
              <Typography
                sx={{
                  color: '#27292D',
                  fontSize: '20px',
                  fontWeight: '600',
                  padding: '20px'
                }}
              >
                Applicants
              </Typography>
            </div>
          </Box>
          <Box
            sx={{
              backgroundColor: '#FFFFFF',
              padding: '10px',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              flexGrow: 1,
              minHeight: 'calc(100vh - 100px)'
            }}
          >
            <Box className="div-styles">
              {/* <Box>
                <Box
                  className="container"
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <input
                    type="text"
                    className="search-bar"
                    placeholder="Search in all columns..."
                    aria-label="Search in all columns"
                  />
                  <Box
                    className="export-buttons"
                    sx={{ display: 'flex', gap: '16px' }}
                  >
                    <Link
                      to="/"
                      className="export-button"
                      style={{
                        textDecoration: 'none',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}
                    >
                      <IconButton>
                        <InsertDriveFileOutlinedIcon
                          sx={{ color: '#0483BA' }}
                        />
                      </IconButton>
                      <Typography>Export to Excel</Typography>
                    </Link>
                    <Link
                      to="/"
                      className="export-button"
                      style={{
                        textDecoration: 'none',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}
                    >
                      <IconButton>
                        <InsertDriveFileOutlinedIcon
                          sx={{ color: '#0483BA' }}
                        />
                      </IconButton>
                      <Typography>Export to PDF</Typography>
                    </Link>
                  </Box>
                </Box>

                <Box
                  sx={{
                    backgroundColor: '#F0F2F4',
                    padding: '10px',
                    color: '#62656C',
                    fontSize: '18px',
                    fontWeight: '400'
                  }}
                >
                  <Typography>
                    Drag a column header and drop it here to group by that
                    column
                  </Typography>
                </Box>
              </Box> */}

              <Box sx={{ width: '100%', flexGrow: 1, minHeight: '300px' }}>
                {localEmployeeList?.length > 0 ? (
                  <DataGrid
                    rows={localEmployeeList}
                    columns={columns}
                    initialState={{
                      pagination: {
                        paginationModel: { page: 0, pageSize: 10 }
                      }
                    }}
                    pageSizeOptions={[5, 10]}
                    onRowClick={handleRowClick}
                  />
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: '130px',
                      backgroundColor: '#f5f5f5',
                      textAlign: 'center',
                      padding: '20px'
                    }}
                  >
                    <Typography sx={{ fontSize: '17px', fontWeight: '500' }}>
                      No applicants found.
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
        <Modal
          open={open}
          onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={style}>
            <Box style={{ backgroundColor: '#f6f6f6', padding: '20px' }}>
              <Typography id="modal-modal-title" variant="h6" component="h2">
                Onboard Applicant
              </Typography>
              <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                Are you sure you want to onboard this applicant?
              </Typography>

              <Box
                sx={{
                  borderRadius: 2,
                  paddingTop: '10px'
                }}
              >
                <Grid container spacing={1}>
                  {appsData?.map((app: any) => (
                    <Grid
                      item
                      xs={2.6}
                      sx={{ marginBottom: 4, marginTop: 3 }}
                      key={app?.id}
                      mx={1}
                    >
                      {selectedCards.includes(app.app_id) ? (
                        <ActiveSelectionBorderSvg
                          title={app?.name}
                          onClick={() => handleCardClick(app?.app_id)}
                        />
                      ) : (
                        <Card
                          onClick={() => handleCardClick(app?.app_id)}
                          sx={{
                            position: 'relative',
                            borderRadius: 50,
                            boxShadow: 3,
                            padding: 2,
                            width: 180,
                            height: 180,
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <CardContent>
                            <div className="cardContentStyles">
                              <Widgets
                                sx={{ color: '#616161', fontSize: '30px' }}
                              />
                              <Tooltip title={app?.name} arrow>
                                <Typography
                                  sx={{
                                    color: '#616161',
                                    fontWeight: '600',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    width: '149px',
                                    textAlign: 'center',
                                    textTransform: 'capitalize'
                                  }}
                                >
                                  {app?.name}
                                </Typography>
                              </Tooltip>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', mt: 2, justifyContent: 'end' }}>
              <Button
                variant="contained"
                onClick={handleClose}
                sx={{
                  mr: 1,
                  // flexGrow: 1,
                  backgroundColor: 'white2.main',
                  color: '#242424'
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={handleSubmit}
                sx={{
                  // flexGrow: 1,
                  backgroundColor: 'primaryBlue.main',
                  color: 'white2.main'
                }}
              >
                Save
              </Button>
            </Box>
          </Box>
        </Modal>
      </Box>
      {/* <SnackbarElement
        message={snackbarOpen.message}
        statusType={snackbarOpen.type}
        snackbarOpen={snackbarOpen.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Shell>
  );
};
export default EmployeeOnboarding;
