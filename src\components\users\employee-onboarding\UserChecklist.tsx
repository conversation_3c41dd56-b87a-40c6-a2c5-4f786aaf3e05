// Global Imports
import {
  Box,
  // Button,
  Checkbox,
  IconButton,
  Input,
  Typography
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local Imports
import '../../../css/checklist-styles.scss';
import { Icon } from '../../form.elements';
import { AppDispatch, AppState } from '../../../redux/app.store';
import { savechecklist } from '../../../redux/reducers/user.reducer';
import LoaderUI from '../../reusable/loaderUI';

// const LevelSelection = () => (
//   <Box sx={{ display: 'flex' }}>
//     {['Level I', 'Level II', 'Level III'].map((level, index) => (
//       <Box key={level} sx={{ padding: '0 10px' }}>
//         <Typography className="level-section">
//           <Checkbox defaultChecked={index === 0} />
//           {level}
//         </Typography>
//       </Box>
//     ))}
//   </Box>
// );

const Header = ({ navigate, employeeDetails }: any) => (
  <Box className="header-box">
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <IconButton onClick={() => navigate('/users/employee-onboarding')}>
        <Icon fontSize="large" name="ArrowBack" color="primary" />
      </IconButton>
      <Typography className="name-type">
        {employeeDetails?.name?.toUpperCase()}
      </Typography>
    </Box>
    {/* <LevelSelection /> */}
  </Box>
);

const Card = ({ card, navigate }: any) => (
  <Box onClick={() => navigate(card.url)} className="card">
    {card.count ? (
      <Typography className="card-count">{card.count}</Typography>
    ) : (
      <Icon name="RemoveRedEyeOutlined" fontSize="large" />
    )}
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        paddingTop: card.count ? '0' : '18px'
      }}
    >
      {card.secondaryIcon && <Icon name={card.secondaryIcon} color="primary" />}
      {card.icon && <Icon name={card.icon} color="primary" />}
      <Typography
        sx={{
          fontSize: '18px',
          fontWeight: '400',
          color: '#27292D',
          paddingLeft: card.icon ? '10px' : '0'
        }}
      >
        {card.checkbox && <Checkbox disabled />}
        {card.label}
      </Typography>
    </Box>
  </Box>
);

const CardSection = ({ cardData, navigate }: any) => (
  <Box className="card-section">
    {cardData?.map((card: any, index: number) => {
      const key = `user-card-${index}-${index * 9}`;
      return <Card key={key} card={card} navigate={navigate} />;
    })}
  </Box>
);

const DateSection = ({ label, type, required, value, handleInput }: any) => (
  <Box sx={{ background: '#FAF9F8', padding: '10px 50px' }}>
    <Box sx={{ display: 'flex', gap: '20px' }}>
      <Typography sx={{ width: '110px' }}>
        {label} <span style={{ color: 'red' }}>{required ? '*' : ''}</span>
      </Typography>
      <Input
        type={type}
        sx={{ width: '100%' }}
        required={required}
        value={value}
        onChange={(e: any) => {
          handleInput(e.target.value);
        }}
      />
    </Box>
  </Box>
);

const DocumentSection = ({
  checkList,
  selectedChecklists,
  onCheckboxChange
}: any) => (
  <Box sx={{ paddingTop: '20px' }}>
    <Typography className="all-documents">All Items</Typography>
    <Box sx={{ background: '#ffffff', padding: '20px' }}>
      {checkList && checkList.length > 0 ? (
        checkList.map((checklist: any) => {
          const isChecked = selectedChecklists?.some(
            (item: any) => item.item_id === checklist.item_id
          );

          // console.log({ isChecked, selectedChecklists, checklist });

          const updatedChecklist = isChecked
            ? selectedChecklists.find(
                (item: any) => item.item_id === checklist.item_id
              ) || checklist
            : checklist;

          return (
            <Box
              key={updatedChecklist?.item_id}
              sx={{
                borderBottom: '1px solid #0d0e0e42',
                backgroundColor: isChecked ? '#FAF9F8' : '#ffffff',
                padding: '5px',
                '&:hover': {
                  backgroundColor: '#f4f4f4',
                  cursor: 'pointer'
                }
              }}
            >
              <Box
                className="checklist-box"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <Typography className="checklist-title">
                  {updatedChecklist?.title}
                </Typography>
                <Checkbox
                  checked={isChecked}
                  onChange={() => onCheckboxChange(updatedChecklist)}
                />
              </Box>
              {updatedChecklist?.inputs?.map((input: any, index: number) => (
                <DateSection
                  label={input.label}
                  type={input?.type}
                  key={`input-${input.label}`}
                  required={input?.required}
                  value={input?.value || ''}
                  handleInput={(value: any) => {
                    onCheckboxChange(updatedChecklist, value, index);
                  }}
                />
              ))}
            </Box>
          );
        })
      ) : (
        <Typography>No checklists available</Typography>
      )}
    </Box>
  </Box>
);

// const Footer = ({ saveCheckList }: any) => (
//   <Box className="footer">
//     <Button
//       sx={{ padding: '10px 40px' }}
//       variant="contained"
//       onClick={saveCheckList}
//     >
//       Save
//     </Button>
//   </Box>
// );

const UserChecklist: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useParams();
  const {
    completedChecklist,
    documentsList,
    checkListData,
    employeeDetails,
    isLoading
  }: any = useSelector((state: AppState) => state?.user);

  const navigate = useNavigate();

  const [selectedChecklists, setSelectedChecklists] = useState<any[]>([]);
  const [stateCheckListData, setStateCheckListData] =
    useState<any[]>(checkListData);

  const cardData = [
    {
      count: `${selectedChecklists?.length ? selectedChecklists?.length : 0}/${checkListData?.length ? checkListData?.length : 0}`,
      label: 'Checklist',
      checkbox: true,
      url: '#'
    },
    {
      count: documentsList?.length > 0 ? documentsList?.length : '0',
      icon: 'FilePresentOutlined',
      label: 'Documents',
      url: `/users/employee-onboarding/documents/${id}`
    },
    {
      icon: 'FileCopyOutlined',
      label: 'Applicant Details',
      url: `/users/employee-onboarding/applicant-details/${id}`
    }
  ];

  const saveCheckList = async (updateSelectedChecklists: any) => {
    try {
      const response = await dispatch(
        savechecklist({
          id,
          data: { completed_checklist: updateSelectedChecklists }
        })
      );

      if (response.payload.status) {
        toast.success('Submission confirmed for this checklist.');
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  // const handleCheckboxChange = (
  //   checklist: any,
  //   value?: any,
  //   index?: number
  // ) => {
  //   // Ensure selectedChecklists is iterable (array)
  //   if (!Array.isArray(selectedChecklists)) {
  //     setSelectedChecklists([]);
  //     return;
  //   }

  //   const isIdExist = selectedChecklists.find(
  //     (s: any) => s.item_id === checklist.item_id
  //   );

  //   let updateSelectedChecklists = [];

  //   if (!isIdExist) {
  //     if (index !== undefined && value !== undefined) {
  //       setStateCheckListData((prevChecklists) =>
  //         prevChecklists.map((checklistItem: any, checkListIndex: number) =>
  //           checkListIndex === index
  //             ? {
  //                 ...checklist,
  //                 inputs: checklistItem.inputs.map((input: any, i: number) =>
  //                   i === index ? { ...input, value } : input
  //                 )
  //               }
  //             : checklist
  //         )
  //       );
  //     } else {
  //       const areRequiredFieldsFilled = checklist.inputs?.every(
  //         (input: any) => !input.required || input.value
  //       );

  //       if (!areRequiredFieldsFilled) {
  //         const missingFields = checklist.inputs
  //           ?.filter((input: any) => input.required && !input.value)
  //           .map((input: any) => input.label) // Assuming `label` is the property name
  //           .join(', ');

  //         toast.error(
  //           `Please fill all required fields before checking this item. Missing fields: ${missingFields}`
  //         );

  //         return;
  //       }

  //       updateSelectedChecklists = [...selectedChecklists, checklist];
  //     }
  //   } else {
  //     updateSelectedChecklists = selectedChecklists.filter(
  //       (s: any) => s.item_id !== checklist.item_id
  //     );
  //     saveCheckList(updateSelectedChecklists);
  //     setSelectedChecklists(updateSelectedChecklists);
  //   }
  // };

  const handleCheckboxChange = (
    checklist: any,
    inputValue?: string,
    inputIndex?: number
  ) => {
    // Find if the checklist item is already selected
    const isChecked = selectedChecklists.some(
      (item) => item.item_id === checklist.item_id
    );

    // If updating additional fields
    if (inputIndex !== undefined && inputValue !== undefined) {
      setStateCheckListData((prevState) =>
        prevState.map((item) =>
          item.item_id === checklist.item_id
            ? {
                ...item,
                inputs: item.inputs.map((input: any, index: number) =>
                  index === inputIndex ? { ...input, value: inputValue } : input
                )
              }
            : item
        )
      );
      return;
    }

    let finalUpdateChecklist: any = [];
    // If toggling checklist checkbox
    if (!isChecked) {
      const areRequiredFieldsFilled = checklist.inputs?.every(
        (input: any) => !input.required || input.value
      );

      if (!areRequiredFieldsFilled) {
        const missingFields = checklist.inputs
          ?.filter((input: any) => input.required && !input.value)
          .map((input: any) => input.label)
          .join(', ');

        toast.error(
          `Please fill all required fields before checking this item. Missing: ${missingFields}`
        );
        return;
      }

      finalUpdateChecklist = [...selectedChecklists, checklist];
    } else {
      setStateCheckListData((prevState) =>
        prevState.map((item) =>
          item.item_id === checklist.item_id
            ? {
                ...item,
                inputs: item.inputs.map((input: any, index: number) =>
                  index === inputIndex ? { ...input, value: '' } : input
                )
              }
            : item
        )
      );

      finalUpdateChecklist = selectedChecklists.filter(
        (item) => item.item_id !== checklist.item_id
      );
    }

    setSelectedChecklists(finalUpdateChecklist);
    saveCheckList(finalUpdateChecklist);
  };

  useEffect(() => {
    if (completedChecklist) {
      setSelectedChecklists(completedChecklist);
    }
    setStateCheckListData(checkListData);
  }, [completedChecklist, checkListData]);

  return isLoading ? (
    <LoaderUI />
  ) : (
    <Box sx={{ padding: '0px 62px', backgroundColor: '#FAF9F8' }}>
      <Box sx={{ padding: '40px' }}>
        <Header navigate={navigate} employeeDetails={employeeDetails} />
        <CardSection cardData={cardData} navigate={navigate} />
        <DocumentSection
          checkList={stateCheckListData}
          selectedChecklists={selectedChecklists}
          onCheckboxChange={handleCheckboxChange}
        />
        {/* <Footer saveCheckList={saveCheckList} /> */}
      </Box>
    </Box>
  );
};

export default UserChecklist;
