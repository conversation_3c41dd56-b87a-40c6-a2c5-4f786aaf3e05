.content {
  min-height: calc(100vh - 64px);
  height: auto;
  position: relative;
  background-color: #f0f0f0;
}

.form-container {
  padding: 0px 90px 60px 90px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .form{
    width: 100%;
    .form-title-card {
      background-color: #fff;
      border-radius: 4px;
      padding: 20px 20px 40px 30px;
      box-shadow: 0px 1px 0px 2px #24242410;
      display: flex;
      gap: 10px;
      flex-direction: column;
      .form-title-field {
        input {
          height: 82px;
          font-size: 28px;
          // outline: none;
          border-width: 0px 0px 1px 0px;
          border-style: solid;
          border-color: #CBDCE3;
          padding-left: 10px;
          color: #000000;
          font-weight: 600;
        }
        input::placeholder {
          color: #000000;
          opacity: 1; /* Firefox */
          // text-transform: capitalize;
        }
      }
    }
    .description-title-field {
      input {
        border-width: 0px 0px 1px 0px;
        border-style: solid;
        border-color: #CBDCE3;
        padding-left: 10px;
        color: #000000;
        font-weight: 400;
        font-size: 18px;
      }
      input::placeholder {
        color: #000000;
        opacity: 1; /* Firefox */
        // text-transform: capitalize;
      }
    }

    .section-container{
      position: relative;
      right: 0px;
      .form-section-card {
        background-color: #fff;
        border-radius: 4px;
        padding: 50px 20px 30px 30px;
        box-shadow: 0px 1px 0px 2px #24242410;
        display: flex;
        flex-direction: column;
        gap: 10px;
        .section-title-field {
          input {
            font-size: 24px;
            font-weight: 600;
            outline: none;
            border: none;
            padding-left: 10px;
            color: #000000;
          }
          input::placeholder {
            color: #000000;
            opacity: 1; /* Firefox */
            // text-transform: capitalize;
          }
        }
        .section-description {
          input {
            font-size: 18px;
            font-weight: 400;
            line-height: 28.89px;
            outline: none;
            border: none;
            padding-left: 10px;
            color: #000000;
          }
          input::placeholder {
            color: #000000;
            opacity: 1; /* Firefox */
            // text-transform: capitalize;
          }
        }
      }
      .activeSection{
        border-top: 4px solid #36C0ED;
      }
    }
  }
}

.w-template {
  width: calc(100% - 300px);
}

.form-section {
  padding: 6% 6%;
  display: flex;
  flex-grow: 1;
  .form {
    width: calc(100% - 300px);
    float: right;
    // width: 100%;



    .section-container-old {
      width: 100%;
      margin-top: 30px;
      .section-header {
        background-color: #fff;

        .section-icons {
          .close-icon {
            font-size: 28px;
          }
        }
        .section-header-input {
          .section-title {
            .error-header {
            }
          }
          .section-description {
            // .error-header{
            //   height: 40px;
            //   margin: 0px;
            //   font-size: 14px;
            // }
          }
        }
        .section-iteration-input {
          width: 150px;
          .iteration-input {
            margin-right: 20px;

            display: flex;
            flex-direction: column;
            align-items: flex-end;
            position: relative;
            input {
              border: 1px solid rgba(0, 0, 0, 0.12);
            }
            .error-header {
              position: absolute;
              top: 35px;
              height: 40px;
              margin: 0px;
              font-size: 14px;
            }
          }
        }
      }
      .fields-container {
        .field-card {
          background-color: #fff;
          border-radius: 6px;
          width: 100%;
          padding: 10px 50px 50px 50px;
          margin-bottom: 8px;
          position: relative;
          .drag {
            width: 100%;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            .drag-icon {
              font-size: 14px;
            }
          }
          .field-inputs {
            display: flex;
            align-items: center;
            padding-top: 10px;
            .label-field {
              width: 100%;
              padding-right: 10px;
              .section-title {
                display: flex;
                align-items: flex-start;
                flex-direction: column;
                width: 100%;
                .error-header {
                  height: 40px;
                  margin: 0px;
                  font-size: 14px;
                }
              }
            }
            .form-fields {
              .form-field-select {
                width: 220px;
                font-size: 16px;
              }
            }
          }
        }
        .field-active {
          border-left: 6px solid #f88b8b;
          .section-title {
            border: 1px solid #cbdce3;
            background-color: #cbdce330;
            padding: 0px 10px;
            height: 40px;
            display: flex;
            flex-direction: row !important;
            align-items: center !important;
            border-radius: 4px;
            input {
              height: 40px;
              font-size: 16px;
            }
          }
        }
      }
    }
    .active {
      border-top: 6px solid #f88b8b;
    }
  }
}

.MuiSelect-select {
// .css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.MuiSelect-select {
  display: flex !important;
  align-items: center;
  gap: 10px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-radius: 4px;
}
//   .justify-content-center {
//     justify-content: center;
//   }

//   .justify-content-end {
//     justify-content: flex-end;
//   }

//   .d-flex {
//     display: flex;
//   }

.my-2 {
  margin: 0.5rem 0;
}

.back-icon {
  cursor: pointer;
}

.label-style {
  margin-right: 0.5rem;
}

.input-styles {
  padding: 0.5rem;
}

.title-input {
  font-size: 1.5rem;
  font-weight: bold;
}

.error {
  color: red;
}

.left-action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: fixed;
  bottom: 8%;
  left: 10px;
  z-index: 10;
  button {
    background-color: #fff;
    border: none;
    width: 48px;
    height: 48px;
    span {
      margin: 0px;
      svg {
        color: cornflowerblue;
      }
    }
  }
  button:hover {
    color: #fff;
    svg {
      color: #fff;
    }
  }
}

.left-310 {
  left: 310px !important;
}
.add-btn-style {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: fixed;
  bottom: 8%;
  right: 25px;
  z-index: 10;
  button {
    background-color: #fff;
    border: none;
    width: 48px;
    height: 48px;
    span {
      margin: 0px;
      svg {
        color: cornflowerblue;
      }
    }
  }
  button:hover {
    color: #fff;
    svg {
      color: #fff;
    }
  }
}

.d-flex-column{
  display:"flex";
  flex-direction: "column";
  width: "100%";
  height: "100%";
}