.section-template-main-component {
  background-color: #f8f8f8;
  min-height: 100%;
  height: auto;
  padding: 2% 12%;
}


.section-template {
  width: 100%;
  height: auto;
  background-color: white;
  margin-top: 30px !important;
  border: 1px solid rgba(128, 128, 128, 0.308);

}

#template-form-name {
  border: none;
    padding-left: 30px;
    color: #000;
    border-bottom: 1px solid rgba(128, 128, 128, 0.308);
    text-transform: capitalize;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    overflow: visible;
    outline: none;
    width: 100%;
  }
  
  #template-form-Description {
    font-size: 12px !important;
    text-transform: capitalize;
    border: none;
    padding-left: 30px;
    color: #000;
    border-bottom: 2px solid rgba(128, 128, 128, 0.308);
    overflow: visible;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    outline: none;
    width: 100%;
  }


#template-section-name {
border: none;
  padding-left: 30px;
  color: #000;
  text-transform: capitalize;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  overflow: visible;
  outline: none;
  width: 100%;
}

#template-section-Description {
  font-size: 12px !important;
  text-transform: capitalize;
  border: none;
  padding-left: 30px;
  color: #000;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  outline: none;
  width: 100%;
}

.section-options-placeholder{
  border: none !important;
  outline: none !important;
}

.section-input {
display: flex;
align-items: center;
padding:0px 20px;
}

#template-form-name::placeholder,
#template-form-Description::placeholder {
  color: #000;
  opacity: 1; /* Firefox */
}

#template-form-name::-ms-input-placeholder,
#template-form-Description::-ms-input-placeholder {
  /* Edge 12-18 */
  color: #000;
}

#template-section-name::placeholder,
#template-section-Description::placeholder {
  color: #000;
  opacity: 1; /* Firefox */
}

#template-section-name::-ms-input-placeholder,
#template-section-Description::-ms-input-placeholder {
  /* Edge 12-18 */
  color: #000;
}
.section-drag-components {
  width: 100%;
  margin-top: 30px;
  height: auto;
  background-color: white;
border-top-left-radius: 5px;
border-bottom-left-radius: 5px;
  // border-left: 5px solid #36C0ED;
  height: auto;
  margin-top: 30px !important;

}
.drag-icon {
  width: 40px;
  height: 40px;
  margin: auto;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

// .section-options{
// }


#template-section-drag-name {
  width: 100%;
  padding: 10px 30px;
  border: 1px solid #cbdce3;
  background: #f7fafb;
  width: inherit;
  margin-right: 0 !important;
  margin-left: 0 !important;
  padding: 0.8rem !important;
  outline: none;
}

.section-drag-input {
  width: 100%;
  outline: none;
}

.section-drag-dropdown {
width: 300px;
  // .css-y4kxc9-MuiInputBase-root-MuiOutlinedInput-root {
    
  //   outline: none !important;
  // }
  // .css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
  //   width: 100%;
  //   padding: 10px 30px;
  //   border: 1px solid #cbdce3;
  //   background: #f7fafb;
  //   padding: 7px 5px 0 0;
  //   width: inherit;
  //   margin-right: 0 !important;
  //   margin-left: 0 !important;
  //   padding: 0.5rem !important;
  //   outline: none !important;
  // }
}

.section-switches {
  width: 100%;
  display: flex;
  float: right;
  justify-content: right;
  .section-more-button {
    font-weight: 900;
    cursor: pointer;
  }
  .section-copy-button {
    font-weight: 900;
    cursor: pointer;
    padding: 10px;
  }
}

.section-button-container {
  padding: 20px 0px;
}
.section-cancel-button {
  width: 100px;
  cursor: pointer;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 5px;
  color: #000;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  float: right;
}

.section-save-button {
  width: 100px;
  cursor: pointer;
  height: 40px;
  margin-left: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fbecff;
  border: none;
  border-radius: 5px;
  color: #000;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  float: right;
}

.section-dropdown {

  
  .section-bullet-button-static{
    background-color: #000;
    border-radius: 50%;
    font-size: 16px;
margin-bottom: 30px;
  }
  .section-dropdown-input-static {
    background-color: #f7fafb;
    font-size: 16px;
    width: 100%;
    border: none;
    border-bottom: 1px solid #f8f8f8;
    text-transform: capitalize;
    outline: none;
    padding-left: 10px;
    display: flex;
margin-bottom: 30px;
height: auto;
  }
  .section-bullet-button-1 {
    background-color: #000;
    border-radius: 50%;
    font-size: 16px;
  }
  .section-bullet-button-2 {
    background-color: #000;
    border-radius: 50%;
    font-size: 16px;
    margin-right: 10px;
  }
  .section-bullet-add-button {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    vertical-align: middle;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
  }
  .section-dropdown-input {
    background-color: #f7fafb;
    font-size: 16px;
    width: 95%;
    border: none;
    border-bottom: 1px solid #f8f8f8;
    text-transform: capitalize;
    outline: none;
    padding-left: 10px;
  }
}
.section-components-main{
  height: auto;
}

.section-checkbox-input-static {
  background-color: #f7fafb;
  font-size: 16px;
  width: 100%;
  border: none;
  border-bottom: 1px solid #f8f8f8;
  text-transform: capitalize;
  outline: none;
  padding-left: 10px;
  display: flex;
margin-bottom: 30px;
height: auto;
}
.section-checkbox {
  display: flex;
  padding-left: 30px;
  
  .section-checkbox-input-static {
    background-color: #f7fafb;
    font-size: 16px;
    width: 100%;
    border: none;
   border-bottom: 1px solid #f8f8f8;
    text-transform: capitalize;
    outline: none;
    display: flex;
margin-bottom: 30px;
height: auto;
  }
  .section-checkbox-input {
    background-color: #f7fafb;
    font-size: 16px;
    width: 95%;
    border: none;
    border-bottom: 1px solid #f8f8f8;
    text-transform: capitalize;
    outline: none;
  }
}
.section-bullet-add-button {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
  cursor: pointer;
  display: inline-block;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  vertical-align: middle;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
}

.section-checkbox{
  font-size: 20px !important;

}

  .section-multiple-choice .section-multiple-choice-button-static {
    border-radius: 50%;
    font-size: 16px;
    margin-bottom: 30px;
}

.section-multiple-choice {
  align-items: center;
  display: flex;
  padding-left: 30px;
  

  .section-multiple-choice-button-static{
    border-radius: 50%;
    font-size: 26px;
margin-bottom: 30px;
  }
  .section-multiple-choice-input-static {
    background-color: #f7fafb;
    font-size: 16px;
    width: 100%;
    border: none;
    border-bottom: 1px solid #f8f8f8;
    text-transform: capitalize;
    outline: none;
    padding-left: 10px;
    display: flex;
margin-bottom: 30px;
height: auto;
  }
  .section-multiple-choice-button-1 {
    border-radius: 50%;
    font-size: 26px;
  }
  .section-multiple-choice-button-2 {
    border-radius: 50%;
    font-size: 26px;
    margin-right: 10px;
  }
  .section-bullet-add-button {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    vertical-align: middle;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
  }
  .section-multiple-choice-input {
    background-color: #f7fafb;
    font-size: 16px;
    width: 95%;
    border: none;
    border-bottom: 1px solid #f8f8f8;
    text-transform: capitalize;
    outline: none;
    padding-left: 10px;
  }
}