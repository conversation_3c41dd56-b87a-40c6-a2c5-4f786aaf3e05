.Appdtls-appName {
  height: 45px;
  font-size: 28px;
  font-weight: 400;
  text-transform: capitalize;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  margin: 10px;
}

.Appdtls-edit {
  border-radius: 50px;
  border: none;
}

.Appdtls-description {
  font-size: 16px;
  font-weight: 400;
  margin-top: 15px;
  color: #616161;
  text-transform: capitalize;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

.Appdtls-version {
  width: 50px;
  color: #616161;
  background-color: #ebebeb;
  height: 13px;
  border-radius: 20px;
  font-size: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
}

.Appdtls-cardStyles {
  border-radius: 50%;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  padding: 16px;
  width: 160px;
  height: 160px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.Appdtls-cardContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.Appdtls-cardName {
  color: #616161;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px;
  text-align: center;
  text-transform: capitalize;
}
