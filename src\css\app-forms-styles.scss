#root {
    background-color: #F0F0F0 !important;
  }
  body {
    background-color: #F0F0F0 !important;
  }
.mainBox{
    padding: 0 4%;
    background-color:#F0F0F0;
    height: auto;
    min-height: 100%;
}
.innerBox{
    border-radius:133.95px;
    color:gray;
    border: 2px solid;
    width: 168px;
    height:168px;
    display:flex;
    align-items: center;
    justify-content: center;
    
}
.cardContentStyles{
    display:block;
    text-align: center;
    margin-top: 19px;
}
.div{
    border-bottom-right-radius: 2px;
}
.innerdiv{
    background-color:#FFFFFF;
    width:100%
}
// .MuiAccordionSummary-content.MuiAccordionSummary-contentGutters.css-eqpfi5-MuiAccordionSummary-content{
//     display: flex;
//     align-items: center;
//     justify-content: flex-start;
// }
// .MuiGrid-root.MuiGrid-item.MuiGrid-grid-xs-2\.2.css-1qk2uxh-MuiGrid-root{
//     padding-left: 100px;
// }