/* Custom styles for sidebar and tabs */

.custom-sidebar {
    width: 250px;
    height: 100%;
    overflow-y: auto;
    border-right: 1px solid #e0e0e0;
    background-color: #fff;
  }
  
  .custom-tabs {
    min-height: 100%;
  }
  
  .custom-tab {
    border-bottom: 1px solid #f0f2f4;
    text-align: left;
    white-space: nowrap;
    background-color: inherit;
    padding: 8px 16px;
    border-right: none;
    cursor: pointer;
  }
  
  .custom-tab:hover {
    background-color: #f6f6f6;
  }
  
  .custom-tab-selected {
    background-color: #f6f6f6 !important; /* Selected tab background color */
    border-bottom: 2px solid #0483BA !important;/* Custom bottom border for selected tab */
    border-right: none !important; /* Ensure no right border */
  }
  
  .custom-tab-label {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  
  .custom-tab-text {
    font-size: 13px !important;
    color: #62656C !important;
  }
  
  /* Add any additional styles you need */
  