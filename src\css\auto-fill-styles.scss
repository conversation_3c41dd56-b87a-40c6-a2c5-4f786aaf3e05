.inputSelectRound-styles {
  padding: 10px;
  font-size: 16px;
  color: #333;
  width: 100%;
  border: 1px solid #808080b0;
  border-radius: 4px;
  height:50px;
  margin-bottom: 10px;
  appearance: auto; 
}

.custom-selectstyles {
  min-width: 300px;
  position: relative;
}

.custom-selectstyles select {
  appearance: none;
  width: 100%;
  font-size: 1.15rem;
  height: 44px;
  margin-bottom: 12px;
  padding: 10px; /* Adjust space for the custom arrow */
  background-color: #fff;
  border: 1px solid #caced1;
  border-radius: 0.25rem;
  color: #000;
  cursor: pointer;
}

.custom-selectstyles::after {
  content: "▼"; /* Custom arrow */
  font-size: 10px;
  color: gray;
  position: absolute;
  top: 48%;
  right: 12px; /* Move the arrow to the left */
  transform: translateY(-50%);
  pointer-events: none; /* Make sure the arrow is not clickable */
}
  
  
  .mb-4{
    margin-bottom: 4;
  }

  .mb-2{
    margin-bottom: 2;
  }

  //fontWeight: "500", marginBottom: 2,fontSize:"18px",color:"#000000"

  .font-weight-500{
    font-weight:500;
  }

  .font-size-18{
    font-size: 18;
  }

  .font-size-15{
    font-size: 15;
  }

  .color-000000{
    color:#000000
  }

  .color-808080{
    color:#808080
  }

  .mr-2{
    margin-right: 2;
  }

  .pb-8{
    padding-bottom: 8px;
  }

  