//CreateChecklist styles code review
.checklist-box {
    padding: 30px 70px;
    background: #ffffff;

    .add-checklist {
        display: flex;
        align-items: normal;
        margin-bottom: 10px;
    }

    .additional-input {
        display: flex;
        align-items: flex-end;
        margin-bottom: 20px;
        width: 500px;

        .inputs-box {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #EBEBEB;
            position: relative;
            width: 55%;
        }

        .cancel-box {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
        }

        .add-box {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
        }
    }
}

.list-box {
    background: #ffffff;
    padding: 30px;
    display: flex;
    flex-direction: column-reverse;

    .checklist {
        border-bottom: 1px solid #0d0e0e42;
        background-color: #ffffff;
        padding: 10px 20px;

        &:hover {
            background-color: #FAF9F8;
            cursor: pointer;
        }

        .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 20px;

            .title-typo {
                font-size: 15px;
                font-weight: 600;
                color: #27292D;
            }
        }
    }

    .no-items-typo {
        color: #27292D;
        font-size: 20px;
        font-weight: 600;
        text-align: center;
        padding: 20px;
    }
}

//UserChecklist style code review
.header-box {
    display: flex;
    justify-content: space-between;
    padding-bottom: 40px;

    .name-type {
        font-size: 24px;
        font-weight: 600;
        color: #27292D;
    }
}

.level-section {
    font-size: 18px;
    font-weight: 400;
    color: #000000;
    line-height: 21.9px;
}

.card-section {
    display: flex;
    align-items: center;
    justify-content: left;
}

.card {
    width: 268px;
    height: 180px;
    background: #ffffff;
    border-radius: 7px;
    box-shadow: 0px 4px 8px -1px #0000001A;
    margin: 0 20px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .card-count {
        font-size: 36px;
        font-weight: 700;
        color: #27292d;
    }
}

.all-documents {
    font-size: 20px;
    font-weight: 600;
    color: #27292D;
    line-height: 30px;
    padding: 20px;
}

.checklist-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;

    .checklist-title {
        font-size: 16px;
        font-weight: 400;
        color: #27292D;
    }
}

.footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}