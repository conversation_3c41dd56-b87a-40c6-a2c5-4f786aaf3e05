.container {
  gap: 10px;
  background-color: #F0F2F4;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 35px 10px;

}

.search-bar {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  background-color: #FAFAFA;
  margin-left: 5px;
  color: #62656C;
  font-weight: 400;
  font-size: 18px;
}

.export-buttons {
  display: flex;
  gap: 10px;
  justify-content: end;
}

.export-button {
  padding: 5px 10px;
  border-radius: 4px;
  background-color: #FAFAFA;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
  width: 214px;
  height: 34px;
  color: #62656C;
  font-weight: 400;
  font-size: 18px;
}


.div-styles {
  padding: 15px;
}




.MuiDataGrid-cell,
.MuiDataGrid-columnHeader {
  border: 1px solid #F0F2F4 !important;
  width: 20px;
}

.MuiDataGrid-cell:last-child,
.MuiDataGrid-columnHeader:last-child {
  border-right: 1px solid #F0F2F4 !important;
}

.MuiDataGrid-row,
.MuiDataGrid-columnHeader {
  border-bottom: 1px solid #F0F2F4 !important;
}


.MuiDataGrid-columnHeader {
  color: #62656C;
  font-size: 18px;
  background-color: #FAFAFA;

}


.striped-row:nth-of-type(odd) {
  background-color: white;
}

.css-t89xny-MuiDataGrid-columnHeaderTitle {
  font-weight: 600 !important;
}

