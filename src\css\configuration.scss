//addressConfig style
.ac-container {
    flex-grow: 1;
    padding: 40px;

    .addr-config {
        font-weight: 600;
        font-size: 20px;
        margin: 0 0 17px;
        color: #27292D;
    }
}

//All
.form-box {
    background-color: #fff;
    border-radius: 10px;
    padding: 40px 60px;
    border: 1px solid #EBEBEB;
}

//FileManager
.fm-container {
    padding: 10px;
    background: #ffffff;
    display: flex;
    flex-direction: column;

    .choose-driver-box {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .typography {
            flex-grow: 1;
            font-size: 24px;
            font-weight: 600;
        }
    }

    .main-container {
        display: flex;
        flex-direction: column;
        flex-grow: 1;

        .folder-list {
            padding: 20px;
            display: flex;
            flex-grow: 1;
        }

        .card {
            position: relative;
            border-radius: 50%;
            box-shadow: 3;
            padding: 10px;
            width: 168px;
            height: 168px;
            flex-direction: column;
            cursor: pointer;
            transition: transform 0.3s ease box-shadow 0.3s ease;

            &:hover {
                transform: scale(1.05);
                box-shadow: 6;
            }

            .selected-folder-icon {
                color: #3f51b5;
                font-size: 48px;
                transition: color 0.3s ease;
            }

            .unselected-folder-icon {
                color: #616161;
                font-size: 48px;
                transition: color 0.3s ease;
            }

            .folder-name-typography {
                font-weight: 400;
                margin-top: 8px;
                transition: color 0.3s ease;
                overflow: hidden;
                width: 100%;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .button-box {
            display: flex;
            justify-content: right;
            align-items: end;
        }
    }
}

//Storage Configuration
.sc-conatiner {
    background: #faf9f8;
    width: 100%;
    padding: 30px 50px;

    .storage-typography {
        padding: 10px;
        font-weight: 600;
        font-size: 24px;
    }
}