.ActvUsr-container {
	border: 0.125rem solid #faf9f8; // 2px
	width: 100%;
	box-shadow: 0px 0.25rem 0.5rem -0.0625rem #0000001a;

	.ActvUsr-header {
		background: #faf9f8;
		padding: 0.625rem; // 10px
	}

	.ActvUsr-title {
		font-size: 1.75rem; // 28px
		font-weight: 400;
		color: #616161;
	}

	.ActvUsr-content {
		width: 100%;

		.ActvUsr-imageBox {
			padding: 5rem 2.5rem; // 80px 40px

			.ActvUsr-imageInnerBox {
				width: 100%;
				height: 12.5rem; // 200px
				background: #d9d9d9;
				cursor: pointer;
			}
		}
	}

	.ActvUsr-userIcons {
		display: flex;

		.ActvUsr-user {
			display: flex;
			align-items: center;
			padding: 0.625rem; // 10px

			.ActvUsr-userIconWrapper {
				padding-right: 0.3125rem; // 5px

				.ActvUsr-userIcon {
					width: 0.75rem; // 12px
					height: 0.75rem; // 12px
					background: #0483ba;
					border-radius: 50%;
				}
			}

			.ActvUsr-userText {
				font-size: 1.125rem; // 18px
				font-weight: 400;
				color: #616161;
			}
		}
	}
}
