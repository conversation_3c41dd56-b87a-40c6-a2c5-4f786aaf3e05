.AppList-header {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 10px 0;
}

.AppList-label {
	font-size: 28px;
	font-weight: 400;
}

.AppList-count {
	display: flex;
	align-items: flex-end;
	gap: 4px;
}

.AppList-number {
	font-size: 28px;
	font-weight: 400;
}

.AppList-text {
	font-size: 17px;
	font-weight: 400;
	padding-bottom: 4px;
}

.AppList-container {
	background: #faf9f8;
	border-top: 2px solid #0483ba; // 0.125rem
	padding: 30px 40px; // 1.875rem 2.5rem
}

.AppList-loading {
	text-align: center;
	padding: 2rem;
}

.AppList-show-more {
	text-align: center;
	margin-top: 1rem;
}

.AppList-show-more-text {
	color: #08366b;
	font-size: 18px;
}

.AppList-show-more-icon {
	color: #08366b;
	cursor: pointer;
}

.RenderApp-container {
	width: 9.25rem; // 196px
	aspect-ratio: 1;
	border-radius: 0.9rem;
	background: #ffffff;
	box-shadow: 0px 0.25rem 0.5rem -0.0625rem #0000001a; // 4px 8px -1px
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 1rem; // 16px
	margin: 0 auto; // Center each app horizontally
	cursor: pointer;
	text-align: center;
}

.RenderApp-logo {
	width: 7.2rem;
	height: 3.5rem;
	margin-bottom: 0.5rem;
}

.RenderApp-icon {
	font-size: 3.5rem;
	color: #808080;
	margin-bottom: 0.5rem;
}

.RenderApp-name {
	font-size: 1rem; // 16px
	color: #242424;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 100%;
}
