.clientGrph-container {
	border: 0.125rem solid #faf9f8; // 2px
	width: 100%;
	box-shadow: 0px 0.25rem 0.5rem -0.0625rem #0000001a;
}

.clientGrph-header {
	width: 100%;
	background: #faf9f8;
	padding: 0.625rem;
}

.clientGrph-title {
	font-size: 1.75rem; // 28px in rem
	font-weight: 400;
	color: #616161;
}

.clientGrph-graph-container {
	display: flex;
	align-items: center;
	justify-content: center;
}

.clientGrph-graph-box {
	padding: 1.25rem; // 20px in rem
}

.clientGrph-graph {
	width: 20.3125rem; // 325px in rem
	height: 20.0125rem; // 325px in rem
	background: #d9d9d9;
	border-radius: 50%;
	cursor: pointer;
}

.clientGrph-footer {
	display: flex;
}

.clientGrph-footer-item {
	display: flex;
	align-items: center;
	padding: 0.625rem;
}

.clientGrph-footer-dot {
	padding-right: 0.3125rem;
	width: 0.75rem; // 12px in rem
	height: 0.75rem; // 12px in rem
	background: #0483ba;
	border-radius: 50%;
}

.clientGrph-footer-text {
	font-size: 1.125rem; // 18px in rem
	font-weight: 400;
	color: #616161;
}
