.MetricsLst-container {
	border: 0.125rem solid #faf9f8; // 2px
	width: 100%;
	box-shadow: 0px 0.25rem 0.5rem -0.0625rem #0000001a;
}

.MetricsLst-header {
	width: 100%;
	background: #faf9f8;
	padding: 0.625rem;
}

.MetricsLst-title {
	font-size: 1.75rem;
	font-weight: 400;
	color: #616161;
}

.MetricsLst-content {
	background: #ffffff;
	padding: 1.875rem 2.5rem;
}

.MetricsLst-app-box {
	width: 110px;
	height: 110px;
	border-radius: 50%;
	background: #ffffff;
	display: flex;
	border: 0.0625rem solid #0483ba;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	margin: auto;
}

.MetricsLst-app-value {
	font-size: 2.5rem;
	font-weight: 500;
	color: #242424;
}

.MetricsLst-app-info {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 0.625rem;
}

.MetricsLst-app-icon {
	padding-right: 0.3125rem;
}

.MetricsLst-app-name {
	font-size: 1rem;
	color: #242424;
	text-align: center;
}
