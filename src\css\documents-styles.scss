.main-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100vh; // Takes full viewport height
  overflow: hidden;
}

/* Top Section */
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  // background-color: white;
  position: sticky;
  top: 0;
  z-index: 100;
  // box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Left Box for Icons & Title */
.left-box {
  display: flex;
  align-items: center;
}

/* Document Container */
.container {
  display: flex;
  flex: 1;
  gap: 15px;
  overflow: hidden; 
  min-height: 0; // Prevents unnecessary height stretching
}

/* Document List */
.document-list {
  width: 30%;
  height: calc(100vh - 100px); // Adjust dynamically based on header height
  overflow-y: auto;
  padding: 10px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
}

/* Document Name Styles */
.document-name {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s;
  border-radius: 5px;
}

.document-name:hover {
  background: #f5f5f5;
}

.document-name.active {
  background: #e0f7fa;
  font-weight: bold;
}

/* Document Preview */
.document-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  height: calc(100vh - 100px); // Adjust based on the top section height
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pdf-preview {
  width: 100%;
  height: 100%;
  border: none;
}

.image-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.documents-typo {
  font-size: 1.2rem;
  font-weight: 500;
  margin-left: 10px;
}

.document-number-typo {
  font-size: 1.1rem;
  font-weight: bold;
  margin-left: 5px;
}
