.container {
  gap: 10px;
  background-color: #F0F2F4;
  min-height: 50px; // Instead of fixed height
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 10px; // Reduce excessive padding
  width: 100%;
}

/* 🔹 Search Bar */
.search-bar {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  background-color: #FAFAFA;
  margin-left: 5px;
  color: #62656C;
  font-weight: 400;
  font-size: 1rem; // Responsive font size

  &:focus {
    outline: 2px solid #0483BA;
  }
}

/* 🔹 Export Buttons */
.export-buttons {
  display: flex;
  gap: 10px;
  justify-content: end;
  flex-wrap: wrap; // Allows buttons to wrap on smaller screens
}

/* 🔹 Export Button */
.export-button {
  padding: 5px 10px;
  border-radius: 4px;
  background-color: #FAFAFA;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
  min-width: 150px; // Instead of fixed width
  flex-grow: 1; // Allow buttons to scale based on content
  height: 34px;
  color: #62656C;
  font-weight: 400;
  font-size: 1rem;

  &:hover {
    background-color: #E0E0E0;
  }
}

/* 🔹 Data Grid Styling */
.MuiDataGrid-cell,
.MuiDataGrid-columnHeader {
  border: 1px solid #F0F2F4 !important;
}

.MuiDataGrid-cell:last-child,
.MuiDataGrid-columnHeader:last-child {
  border-right: 1px solid #F0F2F4 !important;
}

.MuiDataGrid-row,
.MuiDataGrid-columnHeader {
  border-bottom: 1px solid #F0F2F4 !important;
}

.MuiDataGrid-columnHeader {
  color: #62656C;
  font-size: 1rem;
  background-color: #FAFAFA;
  font-weight: 600;
}

/* 🔹 Alternating Row Colors */
.striped-row:nth-of-type(odd) {
  background-color: white;
}

/* 🔹 Main Container */
.main-container {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  max-width: 1400px; // Limit width on large screens
  margin: auto; // Center content

  .title {
    color: #27292D;
    font-size: clamp(1rem, 1.5vw, 1.25rem); // Responsive font size
    font-weight: 600;
    padding: 20px;
  }

  .content {
    background-color: #FFFFFF;
    padding: 10px;
    max-width: 100vw; // Prevent horizontal overflow
    overflow-x: hidden; // Fix unwanted scrolling
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: calc(100vh - 100px);

    .drag-column {
      background-color: #F0F2F4;
      padding: 10px;
      color: #62656C;
      font-size: 1rem;
      font-weight: 400;
      text-align: center;
    }

    .no-applicants {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 130px;
      background-color: #f5f5f5;
      text-align: center;
      padding: 20px;
      font-size: 1rem;
    }
  }
}

/* 🔹 Modal Styling */
.modal-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%; // Use a percentage instead of fixed width
  max-width: 950px; // Keep a max-width for large screens
  background-color: white;
  border: 2px solid #000;
  box-shadow: 24px;
  padding: 20px;

  .modal-content {
    background-color: #f6f6f6;
    padding: 20px;
  }
}

/* 🔹 Buttons */
.button-primary {
  background-color: #0483BA;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #035A88;
  }
}

.button-secondary {
  background-color: #E0E0E0;
  color: #27292D;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #D6D6D6;
  }
}

/* 🔹 Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 16px;
  width: 100%;
  min-height: 120px;

  .MuiPaginationItem-root {
    &.Mui-selected {
      background-color: #29abe2;
      color: white;

      &:hover {
        background-color: #29abe2;
      }
    }
  }

  .MuiPaginationItem-previousNext {
    color: #29abe2;
  }
}

/* 🔹 Responsive Fixes */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
  }

  .export-buttons {
    flex-direction: column;
    align-items: flex-start;
  }

  .export-button {
    width: 100%;
  }

  .modal-container {
    width: 95%;
    padding: 15px;
  }

  .content {
    padding: 5px;
  }

  .title {
    font-size: 1rem;
  }

  .search-bar {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .search-bar {
    font-size: 0.8rem;
  }

  .export-button {
    font-size: 0.9rem;
  }

  .title {
    font-size: 0.9rem;
  }

  .modal-container {
    width: 98%;
    padding: 10px;
  }
}
