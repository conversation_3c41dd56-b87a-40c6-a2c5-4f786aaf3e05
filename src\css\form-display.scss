//FormDisplay styles code review
.main-container {
  //   padding: 0px 100px;

  .title {
    font-size: 22px;
    font-weight: 600;
    margin-top: 50px;
    margin-bottom: 10px;
    padding: 2px;
  }

  .client-form-box {
    margin-bottom: 20px;
    background-color: #ffffff;

    .client-name-box {
      background-color: #cee4f4;
      height: 70px;
      display: flex;
      align-items: center;

      .client-name-typo {
        margin-left: 45px;
        margin-right: 45px;
        font-weight: 600;
        font-size: 20px;
      }
    }

    .group-box {
      padding: 20px 20px;

      .group-title {
        font-weight: 700;
        font-size: 20px;
        margin-bottom: 5px;
        margin-left: 45px;
      }

      .field-label {
        padding: 7px 80px;
        font-weight: 500;
        font-size: 15px;
      }

      .value {
        width: 500px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
