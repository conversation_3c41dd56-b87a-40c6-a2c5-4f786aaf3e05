
.Gridstyl-boxContainer {
    padding: 32px; 
    background-color: #eaf7fb;
  }
.Gridstyl-helpButtonContainer {
    align-items: center;
    justify-content: end;
    margin-bottom: 10px;
  }
  .Gridstyl-clientTextAlign{
    align-items: center;
  justify-content: space-between;
  }
  .Gridstyl-inquiry-btnStyles{
    text-transform: none;
    background-color: #f47b20;
  }



.Gridstyl-mainDataBox{
    background-color: #00ced14d;
    padding: 80px;
    width: 100%;
}
.Gridstyl-mainGridContainer{
    display: flex;
    justify-content: center;
    align-items: center;
}
.Gridstyl-avatar-box{
    display: flex;
    justify-content: flex-start;
    position: relative;
}
.Gridstyl-card-avatar {
    position: absolute;
    left: 13px;
    width: 110px;
    height: 110px;
    z-index: 2;
    border: 2px solid #00ACC1;
    margin-top: -20px;
    color: #ABC1D1;
    background-color: #eaf7fb;
  }

  .Gridstyl-card-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .Gridstyl-data-container{
    display: flex;
    align-items: center;
    background-color: white;
    padding: 16px; 
    border-radius: 20px;
    width: 100%;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16); 
    position: relative;
    height: 240px;
  }
  .Gridstyl-actionIcon-styles{
        display: flex;
        justify-content: flex-end;
        margin-bottom: 8px; 
  }
  .Gridstyl-actionIcon-btn{
    background-color: orange;
    border-radius: 4px;
    margin-right: 8px; 
  }
  .Gridstyl-uploadIcon-btn{
    background-color: orange;
    border-radius: 4px;
  }
  .Gridstyl-status-styles{
    color: red;
    position: absolute;
    top: 10px;
    right: 10px;
  }
  .Gridstyl-actionIcon-color{
    color: white;
  }