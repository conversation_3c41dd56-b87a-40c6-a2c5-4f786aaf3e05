.custom-checkbox {
  width: 18px;
  height: 18px;
  appearance: none;
  -webkit-appearance: none;
  background-color: white;
  border: 1px solid black;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  position: relative;
  transition: background-color 0.2s ease;
}

/* Checked checkbox styles */
.custom-checkbox:checked {
  background-color: #0078d4; /* Blue background when checked */
  border-color: #0078d4;
}

.custom-checkbox:checked::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg); /* Creates a checkmark */
}

/* Styles for disabled checked checkboxes */
.custom-checkbox:checked:disabled {
  background-color: rgb(202, 197, 197); /* Gray background for disabled checked checkbox */
  border-color: rgb(222, 221, 221);
}

.custom-checkbox:checked:disabled::before {
  border-color: white;
}

/* Styles for disabled unchecked checkboxes */
.custom-checkbox:disabled {
  border-color: rgb(189, 187, 187); /* Gray border for disabled unchecked checkbox */
  // cursor: not-allowed;
}

.p-30{
  padding:30px;
}