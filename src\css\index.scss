@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

body,
* {
  margin: 0;
  padding: 0;
  font-family: Montserrat !important;
}
html,
body,
#root {
  width: 100%;
  height: 100%;
}
.flex {
  gap: 10px;
  display: flex;
  align-items: center;
}

.user-profile-button {
  color: white !important;
  display: flex;
  align-items: center;
  gap: 20px;
  // cursor: pointer;
}

.user-profile {
  background-color: #08376a !important;
  color: white !important;
  padding: 20px;
}
.user-icon .MuiSvgIcon-root {
  font-size: 40px;
  margin: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}
// .user-name {
//   font-size: 28px;
// }
.user-email {
  font-size: 16px;
}
.header-icons .MuiSvgIcon-root {
  color: #0483ba !important;
}
.loader-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
// .navigation {
//     gap: 30px;
//     display: flex;
//     list-style: none;
//     align-items: center;
// }
// .navigation li  a{
//     gap: 5px;
//     display: flex;
//     align-items: center;
//     text-decoration: none;
// }

.header-container{
  padding: 0px 100px !important;
}
.cursor-pointer {
  cursor: pointer;
}

.mandatory {
  color: red !important;
}

.d-flex {
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-end{
  justify-content: flex-end;
}

.justify-content-right{
  justify-content: right;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center{
  align-items: center !important;
}

.back {
  color: #f88b8b !important;
  // cursor: pointer;
}

.form-active-selection{
  border-bottom-color: #01BFD6;
  border-bottom-style: solid;
}





// Common Styles
.overflow-auto{
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.bg-white {
  background-color:#ffffff !important;
}

.bg-grayF0 {
  background-color: #F0F0F0 !important;
}
// width Styles
.w-full {
  width: 100%;
}

// Height Styles
.h-full {
  height: 100%;
}

.h-50{
  height: 50px;
}

.h-body {
  height: calc(100% - 82px);
}
// Flex Styles
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-1 {
  flex-grow: 1;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.gap-0 {
  gap: 0px;
}

.gap-6 {
  gap: 6px;
}

.gap-4 {
  gap: 4px;
}

.gap-10{
  gap: 10px;
}
// Border Radius
.radius-4 {
  border-radius: 4px;
}

.radius-6 {
  border-radius: 6px;
}

.radius-8 {
  border-radius: 8px;
}

.radius-10 {
  border-radius: 10px;
}

.radius-20 {
  border-radius: 20px;
}

.radius-30 {
  border-radius: 30px;
}

.radius-50{
  border-radius: 50px;
}

// margin 
.m-10 {
  margin: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}
.mt-5 {
    margin-top: 5px;
}

// padding
.p-10 {
  padding: 10px !important;
}


.p-40 {
  padding: 40px;
}

.p-60{
  padding: 60px;
}

.pl-10 {
  padding-left: 10px;
}

.pr-10 {
  padding-right: 10px;
}

.pt-10 {
  padding-top: 10px;
}
// positions

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-sticky {
  position: sticky !important;
}

// .sideBarMUITabs{
// .css-iuok5g-MuiTabs-indicator {
// display:"none";
// opacity: 0;
// }
// }

.w-h-100 {
  width: 100%;
  height: 100%;
}

.font-weight-400{
  font-weight: 400 !important;
}

 .w-100
{
  width: 100%;
}

// .h-100 {
//   height: 100%;
// }

.d-flex-center {
  display: flex !important;
  align-items:center !important;
}


.flex-space-betweeen{   
  display: flex !important;
  align-items:center !important;
  justify-content: space-between !important;
}

.flex-content-end{   
  display: flex !important;
  align-items:center !important;
  justify-content:end !important;
}

.position-relative{
  position: relative !important;
}

.text-center{
  text-align: center !important;
}

.w-300{
  width: 300px;
}

.bg-FAF9F8{
  background-color: #FAF9F8 !important;
}

.h-60{
  height: 60px;
}
.p-0-10{
  padding:0px 10px;
}

.p-0-20{
  padding:0px 20px;
}

.color-0483BA{
  color:#0483BA !important;
}

.color-616161{
  color:#616161 !important;
}

.font-16{
  font-size: 16px;
}

.w-200{
  width: 200px;
}

.textTransform-capitalize{
  text-transform: capitalize;
}

.cursor-pointer{
  cursor: pointer !important;
}

.p-20-0{
  padding: 20px 0px;
}

.p-20{
  padding: 20px;
}

.color-f6f6f6{
  color: #f6f6f6 !important;
}

.color-2B2B2B{
  color:#2b2b2b !important;
}

.justify-content-space-around{
  justify-content: space-around;
}

.bg-f5f5f5{
  background-color: #f5f5f5 !important;
}

.bg-fafafa{
  background-color:#fafafa !important;
}

.font-weight-500{
  font-weight: 500 !important;
}

.font-size-18{
  font-size: 18px;
}

.color-white{
  color:#fff !important;
}
.bg-08366B{
  background-color: #08366B !important;
}

.bg-f9f9f9{
  background: #f9f9f9 !important;
}
.p-28{
  padding:28px
}

.w-50{
  width: 50%;
}

.justify-content-space-between{
  justify-content: space-between !important;
}

.bg-f6f6f6{
  background-color: #f6f6f6 !important;
}