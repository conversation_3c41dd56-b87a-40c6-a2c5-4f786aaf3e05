/* Root container */
.shell-container {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

/* Content when drawer is not visible */
.shell-content {
	width: 100%;
	height: 100%;
	overflow: auto;

	&.shell-content-submenu {
		height: calc(100% - 82px); /* Adjust height when submenu is visible */
	}
}

/* Wrapper for the drawer */
.shell-drawer-container {
	width: 100%;
	height: 100%;
	overflow: hidden;
	display: flex;

	/* Content inside the drawer */
	.shell-drawer-content {
		width: calc(100% - 250px); /* Adjust width for drawer space */
		height: 100%;
		overflow: auto;
	}
}
