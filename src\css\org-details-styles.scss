.navStyles {
  width: 100%;
  height: 70px;
  background-color: #08366b;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.navTextStyles {
  color: #ffffff;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.gridRowStyles {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
}
.s-styles {
  margin-left: 10px;
}
.icon-styles {
  font-size: small;
  width: 18px;
  height: 18px;
  margin-left: 2px;
}
.editicon {
  color: #36c0ed;
}

.img-container {
  width: 70px;
  box-shadow:
    0 4px 8px 0 rgba(0, 0, 0, 0.2),
    0 6px 20px 0 rgba(0, 0, 0, 0.19);
  margin-right: 30px;
}
.text-styles {
  margin-top: 5px;
  font-size: small;
  color: black;
}
.uploadicon-styles {
  margin-bottom: 5px;
  margin-left: 50px;
}

.apps-container {
  padding: 1px 48px;
}

.header-app-page {
  background-color: #fbecff;
  width: 100%;
  justify-content: space-between;
  color: #3b5864;
  display: flex;
  align-items: center;
  padding: 20px 40px;
}

.header-app-page-button {
  color: #f88b8b;
  cursor: pointer;
  background-color: transparent;
  display: flex;
  align-items: center;
  border: none;
}

.app_icons {
  width: 180px;
  height: 180px;
  margin: auto;
  cursor: pointer;
  border: none;
  grid-template-columns: repeat(n, 1fr);
  margin-right: calc((100% - (n * width)) / (n - 1));
  width: width;
  gap: 10px;
  border-radius: 50%;
  box-shadow:
    0 4px 8px 0 rgba(0, 0, 0, 0.2),
    0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.bg-pink {
  background-color: #fbecff;
  color: #3b5864;
}
.bg-blue {
  background-color: #26bbfa;
  color: #ffff;
}

.mainStyles {
  padding: 0px 54px;
  .devStyles {
    background-color: #fff;
    padding: 60px 80px;
    .innernav-styles {
      display: flex;
      justify-content: space-between;
      padding: 0px;
      margin-bottom: 20px;
      .org-custom-name {
        font-size: 28px;
        font-weight: 700;
      }
      .editicon {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .editicon svg {
        font-size: 20px;
        margin-right: 10px;
      }
    }
    .innerText-styles {
      margin: 0px;
      .ankrotectText {
        color: #242424;
        font-size: 20px;
        // font-weight: 600;
      }
      .upload-icon {
        position: absolute;
        bottom: 0px;
        right: 0px;
        background-color: #0483ba;
        color: #fff;
        padding: 2px;
        border-radius: 50%;
      }
      .radio-icon {
        position: absolute;
        bottom: 0px;
        right: 0px;
        // background-color: #0483BA;
        color: #000;
        padding: 2px;
        border-radius: 50%;
      }
    }
  }
  .appsBox {
    background-color: #faf9f8;
    border-radius: 10px;
    padding: 20px;
    .textapp {
      color: #616161;
    }
    .app_icons_container {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
      width: 100%;
      grid-gap: 20px;
      padding-top: 20px;
    }
  }
}

.edit-org-button:hover {
  background-color: #fff;
  border-radius: 30px;
  border: 2px solid #0483ba;
  margin-right: 20px;
  // padding: 0px 20px;
}
