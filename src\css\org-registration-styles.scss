.loginButton {
    background-color: #08366b !important;
    color: #f2f2f2;
    font-size: 18px !important;
    font-weight: 500 !important;
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  
  // .card-styles{
  //     background-color: #4f4843;
  
  // }
  
  .gridRowstyles {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  
  
  .formStyles {
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 2px 2px #ccc;
    border-radius: 4px;
    padding: 60px;
    background-color: #7d7979;
    width: 1000px;
    height: 500px;
    margin-top: 43px;
    margin-bottom: 30px;
  }
  
  .spaceStyles {
    margin-bottom: 30px;
  }
  
  .errorMessage {
    color: red;
  }
  
  .divStyles {
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // height: 100vh;
    // padding: 0px 100px;
    background-color: #fff;
    margin: 0px;
    padding: 0px !important;
  }
  
  .button-container {
    display: flex;
    align-items: center;
    justify-content: end;
    width: 100%;
    margin-top: 10px;
  }
  
  .main-styles {
    background-color: #f1f3f7;
  }
  
  #root {
    background-color: #f1f3f7 !important;
  }
  body {
    background-color: #f1f3f7 !important;
    
  }
  // .css-1obmsmg{
  //   overflow-x: hidden;
  // }
  
  .icon-styles {
    font-size: small;
    width: 18px;
    height: 18px;
    margin-left: 2px;
  }
  .computericon-styles {
    font-size: small;
    width: 18px;
    height: 18px;
    margin-right: 20px;
  }
  .card-styles {
    background-color: #ffffff;
    width: 1393px;
    padding-left: 38px;
    margin-left: 61px;
    height: 600px;
  }
  .navStyles {
    width: 100%;
    height: 70px;
    background-color: #08366b;
    display: flex;
    align-items: center;
  }
  .navTextStyles {
    color: #ffffff;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .gridRowStyles {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
  }
  .s-styles {
    // margin-right:900px;
    margin-left: 10px;
  }
  
  .form-container {
    // padding: 16px;
  
    .typo-text {
      margin-bottom: 10px;
      color:#3B5864;
      padding: 0px 60px 40px 40px;
      font-size: 28px;
    }
    form {
      padding: 16px;
      border-radius: 8px;
      .form-fields-container{
          // background-color: #f1f3f7;
          border-radius: 8px;
          padding: 20px;
          .labelStyles {
              color: #7E949D;
              margin-bottom: 10px;
              font-size: 16px;
          }
          .inputTagstyles {
              background-color:#FAF9F8;
              border: 1px solid #ffffff;
              font-size: 14px;
              border-radius: 10px;
              height: 55px;
              padding: 10px 20px;
              width: 100%;
              border: none;
              outline: none;
            }
      }
    }
  }

  .apps-container{
    padding: 1px 48px;
}


.header-app-page{
    background-color: #fbecff;
    width: 100%;
    justify-content: space-between;
    color: #3b5864;
    display: flex;
    align-items: center;
    padding: 20px 40px;
}

.header-app-page-button{
    color: #f88b8b;
    cursor: pointer;
    background-color: transparent;
    display: flex;
    align-items: center;
    border: none;
}
.app_icons_container{
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  width: 100%;
  grid-gap: 20px;
  margin-top:65px;
}
    
    
.app_icons {
   width: 180px;
   height: 180px;
   margin: auto;
   cursor: pointer;
   border: none;
   grid-template-columns: repeat(n, 1fr);
   margin-right: calc((100% - (n * width)) / (n - 1));
   width: width;
   gap: 10px;
   border-radius: 50%;
   box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.bg-pink{
    background-color: #fbecff;
    color: #3b5864;
}
.bg-blue{
    background-color: #26bbfa;
    color: #ffff;
}
.active-app{
  border: 2px solid green;
}

.upload-icon {
  position: absolute;
  bottom: 0px;
  right: 0px;
  background-color: #0483ba;
  color: #fff;
  padding: 2px;
  border-radius: 50%;
}
.radio-icon {
  position: absolute;
  bottom: 0px;
  right: 0px;
  // background-color: #0483BA;
  color: #000;
  padding: 2px;
  border-radius: 50%;
}
.innerLabel{
  
  position: relative;
    bottom: 50px;
}


  