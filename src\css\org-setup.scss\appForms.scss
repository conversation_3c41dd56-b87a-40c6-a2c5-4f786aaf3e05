.appFrms-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.appFrms-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 20px 100px;
}

.appFrms-main {
  background-color: #FBF9F9;
  width: 100%;
}

.appFrms-titleSection {
  border: none;
  border-radius: 2px;
}

.appFrms-heading {
  font-size: 26px;
  font-weight: 600;
  padding: 40px;
}

.appFrms-emptyMessage {
  padding: 193px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--background-paper);
  border-radius: 16px;
  box-shadow: var(--box-shadow-1);
  text-align: center;
}

.appFrms-cardContainer {
  background-color: #FBF9F9;
  border-radius: 2px;
  padding: 15px;
}

.appFrms-card {
  position: relative;
  border-radius: 50%;
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 160px;
  height: 160px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.appFrms-cardContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.appFrms-icon {
  color: #616161;
  font-size: 30px;
}

.appFrms-cardText {
  color: #616161;
  font-weight: 500;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-transform: capitalize;
  width: 150px;
}
