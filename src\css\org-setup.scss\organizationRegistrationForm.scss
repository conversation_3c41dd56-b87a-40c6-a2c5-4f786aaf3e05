/* org-registn-container */
.org-registn-container {
	padding: 20px 100px;
	background-color: #EEEFEF;
}

.org-registn-inner-box {
	background-color: #FBF8F8;
	padding-bottom: 20px;
}

.org-registn-title {
	font-size: 26px;
	font-weight: 500;
	// margin-bottom: 40px;
	padding: 20px 0px 0px 50px;
}

.org-registn-form-container {
	padding: 0px 30px;
}

.org-registn-grid-container {
	padding: 40px 60px 40px 20px;
}

.org-registn-upload-logo-container {
	margin-top: 10px;
}

.org-registn-upload-logo-label {
	margin-bottom: 10px;
}

.org-registn-upload-logo-box {
	display: flex;
	align-items: center;
	gap: 10px;
}

.org-registn-upload-logo-card {
	max-width: 120px;
	max-height: 120px;
	background-color: #faf9f8;
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.org-registn-logo-box {
	background-color: #ffffff;
	border-radius: 10px;
	padding: 15px;
	cursor: pointer;
}

.org-registn-logo-media {
	height: 60px;
	width: 60px;
}

.org-registn-upload-icon {
	position: absolute;
	bottom: 0;
	right: 0;
	cursor: pointer;
}

.org-registn-apps-container {
	background-color: #faf9f8;
	padding: 20px;
}

.org-registn-apps-title {
	font-size: 22px;
	font-weight: 500;
	width: 100%;
	background-color: #faf9f8;
	z-index: 1;
	position: relative;
	padding-bottom: 10px;
}

.org-registn-apps-box {
	height: 190px;
	overflow: auto;
	padding: 25px;
}

.org-registn-app-item {
	width: 160px;
	height: 160px;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
}

.org-registn-submit-button {
	background-color: var(--primaryBlue-main);
	color: var(--white2-main);
	padding: 10px 35px;
	box-shadow: 0px 4px 8px 2px rgba(0, 0, 0, 0.2);

	&:hover {
		color: var(--white2-main);
		background-color: #08366b;
		box-shadow: 0px 8px 10px 4px rgba(0, 0, 0, 0.2);
	}
}
