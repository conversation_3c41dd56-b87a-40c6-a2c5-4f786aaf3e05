/* Main Box Styles */
.org-dtls-mainStyles {
	.org-dtls-backgroundBox {
		background-color: #FBF8F8;
	}

	.org-dtls-devStyles {
		padding: 50px;
	}

	.org-dtls-innernavStyles {
		display: flex;
	}

	.org-dtls-captalizedStyles {
		text-transform: capitalize;
	}

	.org-dtls-typographyTitle {
		font-size: 28px;
		font-weight: 400;
	}

	.org-dtls-editOrgButton {
		display: flex;
		align-items: center;
		gap: 10px;
		cursor: pointer;
		padding: 0 20px;
		border: 1px solid transparent;
	}

	.org-dtls-typographyEdit {
		color: #323739;
		font-size: 18px;
	}

	.org-dtls-paddingTop30px {
		padding-top: 30px;
	}

	.org-dtls-flexRow {
		display: flex;
		align-items: center;
		gap: 80px;
	}

	.org-dtls-flexColumn {
		display: flex;
		justify-content: center;
		gap: 14px;
		flex-direction: column;
	}

	.org-dtls-typographyName {
		font-size: 22px;
	}

	.org-dtls-flexRowGap20px {
		display: flex;
		align-items: center;
		gap: 20px;
	}

	.org-dtls-typographyDetails {
		font-weight: 400;
		font-size: 18px;
		margin: 0;
	}

	.org-dtls-flexAlignCenterGap10px {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.org-dtls-card {
		max-width: 120px;
		max-height: 120px;
		background-color: #faf9f8;
		padding: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
	}

	.org-dtls-cardContent {
		border-radius: 10px;
	}

	.org-dtls-cardMedia {
		height: 60px;
		width: 60px;
	}

	.org-dtls-uploadIcon {
		position: absolute;
		bottom: 6px;
		right: 6px;
	}

	.org-dtls-appsBox {
		background-color: #faf9f8;
		padding: 30px 0px;
	}

	.org-dtls-typographyApps {
		font-size: 26px;
		padding: 10px 50px;
		font-weight: 500;
		color: #616161;
		position: relative;
		z-index: 1;
	}

	.org-dtls-gridContainer {
		max-height: 400px;
		overflow: auto;
		padding-bottom: 10px;
		padding-left: 10px;
	}
}
