// Container styles
.org-lst-container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}

// Padding container
.org-lst-padding {
padding:0px 120px
}

// Inner box styles
.org-lst-inner-box {
padding: 20px 0px;
}

// Header styles
.org-lst-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 1.5rem;

	@media (min-width: 600px) {
		margin-bottom: 2rem;
	}

	@media (min-width: 960px) {
		margin-bottom: 2.5rem;
	}
}

.org-lst-total {
	display: flex;
	align-items: center;
	gap: 0.5rem;

	@media (min-width: 600px) {
		gap: 0.625rem;
	}
}

.org-lst-total-text {
	font-size: 1.125rem;

	@media (min-width: 600px) {
		font-size: 1.25rem;
	}

	@media (min-width: 960px) {
		font-size: 1.375rem;
	}
}

.org-lst-count {
	font-size: 1.5rem;
	font-weight: 400;

	@media (min-width: 600px) {
		font-size: 1.625rem;
	}

	@media (min-width: 960px) {
		font-size: 1.75rem;
	}
}

// Search bar styles
.org-lst-searchbar {
	background: white !important;
}

.org-lst-search-form {
	display: flex;
	align-items: center;
	border-radius: 30px;
	background-color: #f0f0f0;
}

// Grid and Grid item styles
.org-lst-grid {
	justify-content: flex-start;
}

.org-lst-grid-item {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	flex-direction: column;
	max-width: 100%;
	padding-left: 0;
}
.org-lst-grid-item:hover .org-lst-app-count-box  {
	cursor: pointer;
  }
// App count box styles
.org-lst-app-count-box {
	background-color: #0483ba;
	width: 6rem;
	height: 5.5rem;
	border-top-left-radius: 1.25rem;
	border-top-right-radius: 1.25rem;
	display: flex;
	align-items: center;
	justify-content: center;

	@media (min-width: 600px) {
		width: 9.5rem;
		height: 7.875rem;
	}
}

.org-lst-app-count {
	display: flex;
	align-items: flex-end;
}

.org-lst-app-count-number {
	color: #fff;
	font-size: 1.5rem;
	font-weight: 600;

	@media (min-width: 600px) {
		font-size: 2rem;
	}
}

.org-lst-app-text {
	color: #fff;
	margin-left: 0.25rem;
	margin-bottom: 0.5rem;
	font-size: 0.875rem;
}

// Organization name box styles
.org-lst-name-box {
	background-color: #fff;
	border-radius: 1.875rem;
	border: 1px solid #edf4f8;
	width: 80%;
	padding: 0.625rem;
	display: flex;
	align-items: center;
	box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.24);
	height: 2.5rem;
	position: relative;
	top: -1.5625rem;
	cursor: pointer;

	@media (min-width: 600px) {
		height: 3.125rem;
	}
}

.org-lst-icon-button {
	width: 2rem;
	height: 2rem;

	@media (min-width: 600px) {
		width: 2.25rem;
		height: 2.25rem;
	}
}

.org-lst-org-name {
	width: 6rem;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 0.875rem;

	@media (min-width: 600px) {
		width: 8.125rem;
	}
}
