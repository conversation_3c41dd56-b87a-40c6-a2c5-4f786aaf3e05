
//Common input fields styles
.inputSelectRound-styles {
    padding: 10px;
    font-size: 16px;
    color: #333;
    width: 100%;
    border: 1px solid #808080b0;
    border-radius: 4px;
    height:50px;
    margin-bottom: 10px;
    appearance: auto; 
}

.custom-selectstyles {
    min-width: 350px;
    position: relative;
  }

.custom-selectstyles select {
    appearance: none;
    width: 100%;
    font-size: 1.15rem;
    height: 44px;
    margin-bottom: 12px;
    padding: 10px; /* Adjust space for the custom arrow */
    background-color: #fff;
    border: 1px solid #caced1;
    border-radius: 0.25rem;
    color: #000;
    cursor: pointer;
  }
  
  .custom-selectstyles::after {
    content: "▼"; /* Custom arrow */
    font-size: 10px;
    color: gray;
    position: absolute;
    top: 48%;
    right: 12px; /* Move the arrow to the left */
    transform: translateY(-50%);
    pointer-events: none; /* Make sure the arrow is not clickable */
  }

//ListView popup-styles

.popuplst-main-box{
  border: 1px solid #E0E0E0;
  border-radius: 8px; /* Converting borderRadius: 2 (assuming 2 is the theme spacing equivalent of 8px) */
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.popuplst-avatar-styles{
  width: 120px;
  height: 120px;
  background-color: #E0E0E0; /* Note: `bgcolor` converted to `background-color` */
  margin-left: 16px; /* Assuming `ml: 2` corresponds to 16px (theme spacing multiplier) */
  border: 2px solid #00ACC1;
  color: #ABC1D1;
  background-color: #eaf7fb; /* This overrides the previous `background-color` */
}
.popuplst-input-align{
  display: flex;
  align-items: center;
  padding: 5px 0px;
}
.popuplst-icons-box{
  display: flex;
  margin-left: 16px; /* This corresponds to `ml: 2` in Material-UI (which is 16px) */
  align-items: flex-end;
  margin-bottom: 150px;
}

//GridView popup-styles
  .popup-inner-box {
    border: 1px solid #E0E0E0;
    border-radius: 8px;
    padding: 16px;
    padding-top: 40px;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    position: relative;
  }
  .popup-avatar-style {
    width: 110px;
    height: 110px;
    background-color: #eaf7fb;
    border: 2px solid #00ACC1;
    position: absolute;
    top: -80px;
    left: 16px;
    color: #ABC1D1;
  }
  .popup-select-box{
    padding: 10px 0;
  }
  .popup-icon-box {
    display: flex;
    position: absolute;
    top: -45px;
    right: 16px;
    z-index: 1;
  }
  .popup-icon-color{
    color: white;
  }
  .popup-icon-container{
    background-color: orange;
    border-radius: 4px;
    margin-right: 8px;
  }
  .popup-uploadicon-style{
    background-color: orange;
    border-radius: 4px;
  }
  .popup-completed-text {
    position: absolute;
    top: 8px;
    right: 8px;
  }
  .popup-bottom-controls {
    width: 100%;
    text-align: right;
    margin-top: 16px;
    display: flex;
    gap: 16px;
    justify-content: right;
    height: 40px;
  }
  .popup-form-control-style {
    width: 90px;
    height: 40px;
  }
  .popup-button-heights{
    height: 40px;
  }