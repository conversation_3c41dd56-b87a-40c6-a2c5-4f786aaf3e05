#root {
  background-color: #cccfd2 !important;
}

body {
  background-color: #cccfd2 !important;
}

.img-container {
  width: 100%;

}

.navStyles {
  width: 100%;
  height: 82px;
  background-color: #08366b;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 100px;
}

.navTextStyles {
  color: #ffffff;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.s-styles {
  // margin-right:900px;
  margin-left: 10px;
}

.previewResponsive {
  color: white;
}

.previewResponsivetext {
  margin-left: 2px;
  margin-right: 15px;
  font-size: 20px;
}


//   .mainParentdiv {
//     display: block;
//     margin: 20px auto;        
//     padding-top: 50px;        
//     width: 100%;              
//   }

// .mobileView{
//   width: 435px;
//   height: 640px;

// }
// .tabView{
//   width: 700px;
//   height: 640px;

// }

.mainParentdiv {
  display: block;
  margin: 20px auto;
  padding-top: 50px;
  width: 100%;
}

.mobileView {
  width: 435px !important;
  height: 640px;
  overflow-y: hidden;
  overflow-x: hidden;
  scrollbar-width: thin;
  padding: 0;
}

.tabView {
  width: 870px !important;
  // height: 640px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  padding: 0;
}
// .previewOuter{
//     display: flex;
//     align-items: center;
//     justify-content: center;
// }
.deskTopIconStyles {
  color: orange;
}

.mobileAndTabStyles {
  color: blue;
}

.sectionText {
  margin-bottom: 20px;
}

// .textFields {
//   margin-top: 5px;
//   padding: 60px;
// }


// .textFields.mobileView {
//   padding: 20px;
// }


.loginButton {
  background-color: #08366b !important;
  color: #f2f2f2;
  font-size: 18px !important;
  font-weight: 500 !important;
  padding-left: 3rem !important;
  padding-right: 3rem !important;
  margin-left: 980px;
  margin-top: 75px;
}

.formStyles {
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 2px 2px #ccc;
  border-radius: 4px;
  padding: 60px;
  background-color: #7d7979;
  width: 1000px;
  height: 500px;
  margin-top: 43px;
  margin-bottom: 30px;
}

.spaceStyles {
  margin-bottom: 30px;
}

.errorMessage {
  color: red;
}

.labelStyles {
  color: #788184;
  margin-bottom: 10px;
  font-size: 16px;
}

.inputTagstyles {
  background-color: #F0F0F0;
  // border: 1px solid #ffffff;
  font-size: 14px;
  border-radius: 10px;
  height: 55px;
  padding: 10px 20px;
  width: 100%;
  border: none;
  outline: none;
}

.formCardHeader {
  margin-top: 10px;
}

.formik-container {
  margin-top: 30px;
}

.serviceText {
  margin-top: 20px;
  margin-bottom: 10px;
}

.checkboxInput-styles {
  margin-right: 10px;
}

.checkboxGrid-styles {
  margin-top: 5px;
  margin-bottom: 20px;
}

.appsFormGridStyles {
  margin-left: 20px;
}

.textArea-styles {
  width: 100%;
  background-color: #F0F0F0;
  border: none;
  outline: none;
  border-radius: 10px;
  padding: 10px 20px;
}

.d-block{
  display: block;
}
.font-size-24{
  font-size: 24px;
}
.font-size-14{
  font-size: 14px;
}