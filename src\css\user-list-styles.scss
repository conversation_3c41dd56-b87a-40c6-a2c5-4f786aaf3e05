.userAccordionDetailsStyles {
  background-color: #e3f2fd !important;
  height: 100% !important;
  padding: 16px !important;
}

.accordion {
  box-shadow: none;
  border: 1px solid #e9f5f7;
  border-radius: 1px;

  .accordion-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px;
    padding-left: 30px;
    padding-right: 30px;

    .left-box {
      display: flex;
      align-items: center;
      flex: 2;
    }

    .center-box {
      flex: 1; // Center this section by giving equal space
      display: flex;
      justify-content: center; // Center horizontally
      align-items: center; // Center vertically
    }

    .right-box {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 1;
    }
  }

  .sub-accordian-summary {
    display: flex;
    align-items: center;

    .accordion-box {
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .accordion-box2 {
      display: flex;
      align-items: center;
    }
  }

  .user-ad-box {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .user-name {
      margin-bottom: 15px;
      font-weight: 500;
      font-size: 20px;
    }

    .mail-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .mail-icon {
        margin-right: 8px;
        color: #757575;
        font-size: 16px;
      }

      .user-email {
        font-size: 16px;
        font-weight: 400;
      }
    }

    .number-box {
      display: flex;
      align-items: center;

      .phone-icon {
        margin-right: 8px;
        color: #757575;
        font-size: 16px;
      }
    }

    .apps-list {
      border-radius: 2%;
      padding-top: 10px;
      display: flex;
      justify-content: flex-end;

      .app-card {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 16px;
        background: #fff;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
        transition: 0.3s ease-in-out;
        cursor: pointer;
        overflow: hidden;
        padding: 10px;
        width: 150px;
        height: 150px;

        .app-name-typography {
          color: #616161;
          font-weight: 600;
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-transform: capitalize;
        }
      }
    }
  }
}

.org-accordian {
  .user-box {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .accordion-details {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .user-name {
      margin-bottom: 15px;
      font-weight: 500;
      font-size: 20px;
    }

    .mail-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .mail-icon {
        margin-right: 8px;
        color: #757575;
        font-size: 16px;
      }

      .user-email {
        font-size: 16px;
        font-weight: 400;
      }
    }

    .number-box {
      display: flex;
      align-items: center;

      .phone-icon {
        margin-right: 8px;
        color: #757575;
        font-size: 16px;
      }
    }

    .apps-list {
      background-color: #e3f2fd;
      border-radius: 2%;
      padding-top: 10px;
      display: flex;
      justify-content: flex-end;

      .app-card {
        position: relative;
        border-radius: 50%;
        box-shadow: 3;
        padding: 10px;
        width: 150px;
        height: 150px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        .app-name-typography {
          color: #616161;
          font-weight: 600;
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-transform: capitalize;
        }
      }
    }
  }
}
