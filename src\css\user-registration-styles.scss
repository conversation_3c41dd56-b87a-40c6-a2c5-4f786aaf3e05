.loginButton {
  background-color: #08366b !important;
  color: #f2f2f2;
  font-size: 18px !important;
  font-weight: 500 !important;
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

// .card-styles{
//     background-color: #4f4843;

// }

.gridRowstyles {
  display: flex;
  justify-content: center;
  align-items: center;
}



.formStyles {
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 2px 2px #ccc;
  border-radius: 4px;
  padding: 60px;
  background-color: #7d7979;
  width: 1000px;
  height: 500px;
  margin-top: 43px;
  margin-bottom: 30px;
}

.spaceStyles {
  margin-bottom: 30px;
}

.errorMessage {
  color: red;
}

.divStyles {
  // display: flex;
  // align-items: center;
  // justify-content: center;
  // height: 100vh;
  padding: 0px 20px;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: end;
  width: 100%;
  margin-top: 20px;
}

.main-styles {
  background-color: #f1f3f7;
}

#root {
  background-color: #f1f3f7 !important;
}

body {
  background-color: #f1f3f7 !important;
}

.icon-styles {
  font-size: small;
  width: 18px;
  height: 18px;
  margin-left: 2px;
}

.computericon-styles {
  font-size: small;
  width: 18px;
  height: 18px;
  margin-right: 20px;
}

.card-styles {
  background-color: #ffffff;
  width: 1393px;
  padding-left: 38px;
  margin-left: 61px;
  height: 600px;
}

.navStyles {
  width: 100%;
  height: 70px;
  background-color: #08366b;
  display: flex;
  align-items: center;
}

.navTextStyles {
  color: #ffffff;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.gridRowStyles {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
}

.s-styles {
  // margin-right:900px;
  margin-left: 10px;
}

.form-container {
  // padding: 16px;

  .typo-text {
    margin-bottom: 10px;
    padding: 0px 16px;
  }

  form {
    padding: 16px;
    border-radius: 8px;

    .form-fields-container {
      background-color: #f1f3f7;
      border-radius: 8px;
      padding: 20px;

      .labelStyles {
        color: #7E949D;
        margin-bottom: 10px;
        font-size: 16px;
      }

      .inputTagstyles {
        background-color: #ffffff;
        border: 1px solid #ffffff;
        font-size: 14px;
        border-radius: 10px;
        height: 55px;
        padding: 10px 20px;
        width: 100%;
        border: none;
        outline: none;
      }
    }
  }
}

.field-input #mui-component-select-Organistion,
#mui-component-select-RoleName {
  background-color: #fff;
}

.field-input input {
  background-color: #fff;
}

//UserRegistration style in code review

.main-conatiner {
  background-color: #FBF8F8;
  padding: 60px 60px 60px 90px;

  .title {
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 40px;
  }

  .form-grid {
    background-color: #FBF8F8;
    border-radius: 10px;
    padding: 40px 60px 40px 20px;
    border: 1px solid #95E7DB;

    .apps-box {
      background-color: #FAF9F8;
      display: flex;
      flex-flow: wrap;
      padding: 20px 10px 20px 10px;

      .apps-typo {
        font-size: 22px;
        font-weight: 500;
        width: 100%;
        z-index: 1;
        position: relative;
        padding-bottom: 10px;
      }

      .app-box {
        width: 160px;
        height: 160px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
      }
    }
  }

}