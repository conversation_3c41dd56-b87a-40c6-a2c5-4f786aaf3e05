#root{
    background-color:rgb(230, 221, 221);
}
.divStyles{
    display: flex;
    align-items: center;
    justify-content: center;
    
   
}
.org-name{
    display: flex;
    align-items: center;
    
}

.nameStyles{
    margin-left:20px;
    justify-content:center;
    display: flex;
    color: #000000;
}
.text-styles{
    margin-left:20px;
    justify-content:center;
    display: flex;
    color: #000000;
}
.Mui-expanded .nameStyles, #active-user.Mui-expanded .text-styles{
    color:#FFFFFF;
}

.iconStyles, #active-user .iconStyles{
    color:#36C0ED;
}
.Mui-expanded .iconStyles, #active-user.Mui-expanded .iconStyles{
    color:#FFFFFF;
}

.accordionStyles{
   background-color: aquamarine;
    
}
.navStyles{
    width: 100%;
    height: 70px;
    background-color: #08366B;
    display: flex;
    justify-content: center;
    align-items: center;
}
.gridRowStyles{
    display: flex;
    align-items: center;
    justify-content:space-between;
    width: 100%;
    padding: 0px 30px;
    
}
.s-styles{
    // margin-right: 1057px;
    // margin-left: 10px;
    font-size: 22px;
}
.navTextStyles{
    color: #FFFFFF;
//    padding-top:5px;
   
}

.devStyles{
    width:100%;
    background-color: #fff;
    padding: 50px;
} 

.mainStyles{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 100px;
    background-color:rgb(230, 221, 221);
}
// .background-styles{
//     background-color:rgb(230, 221, 221);
// }
.iconsAlignment{
    display: flex;
    align-items: center;
    justify-content: end;
}
#panel1-header.Mui-expanded{
    background-color: #36C0ED;
    margin: 0px !important;
}
#panel1-header .Mui-expanded{
    margin: 0px !important;
}
#active-user .Mui-expanded{
    background-color: #36C0ED;
}
#active-user .Mui-expanded{
    margin: 0px !important;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0px;
}
.accordion-style{
    margin-bottom: 4px;
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0) !important;
    border: 2px solid #F0F0F0;
}