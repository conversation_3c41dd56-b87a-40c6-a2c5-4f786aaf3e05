import { createTheme, CssBaseline, ThemeProvider } from '@mui/material';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { ToastContainer, Zoom } from 'react-toastify';

import './css/index.scss';
import App from './App';
import appStore from './redux/app.store';

const appTheme = createTheme({
  palette: {
    primary: {
      main: '#0483BA',
      dark: '#0483BA',
      light: '#0483BA'
    },
    secondary: {
      main: '#08366B',
      dark: '#0836dd',
      light: '#ff0'
    },
    common: {
      white: '#FAF9F8'
    },
    white2: {
      main: '#FFFFFF',
      light: '#FFFFFF',
      dark: '#FFFFFF'
    },
    gray: {
      main: '#FAF9F8',
      light: '#FAF9F8',
      dark: '#FAF9F8'
    },
    gray2: {
      main: '#F0F0F0',
      light: '#F0F0F0',
      dark: '#F0F0F0'
    },
    gray3: {
      main: '#F0F0F0',
      light: '#F0F0F0',
      dark: '#F0F0F0'
    },
    gray4: {
      main: '#EBEBEB',
      light: '#EBEBEB',
      dark: '#EBEBEB'
    },
    gray5: {
      main: '#616161',
      light: '#616161',
      dark: '#616161'
    },
    blk2: {
      main: '#242424',
      light: '#242424',
      dark: '#242424'
    },
    blk: {
      main: '#2B2B2B',
      light: '#2B2B2B',
      dark: '#2B2B2B'
    },
    cyanlight: {
      main: '#EDF4F8',
      light: '#EDF4F8',
      dark: '#EDF4F8'
    },
    cyan: {
      main: '#CBDCE3',
      light: '#CBDCE3',
      dark: '#CBDCE3'
    },
    cyan2: {
      main: '#9EB9C5',
      light: '#9EB9C5',
      dark: '#9EB9C5'
    },
    cyan3: {
      main: '#7E949D',
      light: '#7E949D',
      dark: '#7E949D'
    },
    dark: {
      main: '#3B5864',
      light: '#3B5864',
      dark: '#3B5864'
    },
    secondary2: {
      main: '#F6BBA8',
      light: '#F6BBA8',
      dark: '#F6BBA8'
    },
    red2: {
      main: '#EF4848',
      light: '#EF4848',
      dark: '#EF4848'
    },
    red: {
      main: '#BC0202',
      light: '#BC0202',
      dark: '#BC0202'
    },
    lightOrange: {
      main: '#FFF3EF',
      light: '#FFF3EF',
      dark: '#FFF3EF'
    },
    G3: {
      main: '#D3F9EB',
      light: '#D3F9EB',
      dark: '#D3F9EB'
    },
    vi3: {
      main: '#FCF0FF',
      light: '#FCF0FF',
      dark: '#FCF0FF'
    },
    vi2: {
      main: '#F5CBFF',
      light: '#F5CBFF',
      dark: '#F5CBFF'
    },
    blu: {
      main: '#36C0ED',
      light: '#36C0ED',
      dark: '#36C0ED'
    },
    primaryBlue: {
      main: '#08366B',
      light: '#08366B',
      dark: '#08366B'
    },
    b1: {
      main: '#C7E9F4',
      light: '#C7E9F4',
      dark: '#C7E9F4'
    },
    b2: {
      main: '#98CEF6',
      light: '#98CEF6',
      dark: '#98CEF6'
    },
    b3: {
      main: '#0483BA',
      light: '#0483BA',
      dark: '#0483BA'
    },
    purple: {
      main: '#8E24A8',
      light: '#8E24A8',
      dark: '#8E24A8'
    },
    blue2: {
      main: '#235FA5',
      light: '#235FA5',
      dark: '#235FA5'
    },
    green: {
      main: '#469A68',
      light: '#469A68',
      dark: '#469A68'
    },
    blue3: {
      main: '#297EAE',
      light: '#297EAE',
      dark: '#297EAE'
    },
    gray6: {
      main: '#D7D6D6',
      light: '#D7D6D6',
      dark: '#D7D6D6'
    }
  },
  // Black ToolTip

  // components: {
  //   MuiTooltip: {
  //     styleOverrides: {
  //       popper: {
  //         '& .MuiTooltip-tooltip': {
  //           backgroundColor: '#08366B',
  //           color: '#fff',
  //           fontSize: '0.675rem',
  //           fontWeight: 500,
  //           borderRadius: '12px',
  //           padding: '10px 14px',
  //           backdropFilter: 'blur(10px)',
  //           WebkitBackdropFilter: 'blur(10px)',
  //           transition: 'opacity 0.3s ease-in-out, transform 0.2s ease-in-out',
  //           transform: 'translateY(4px)',
  //           border: '1px solid rgba(255, 255, 255, 0.15)'
  //         },
  //         '& .MuiTooltip-arrow': {
  //           color: 'rgba(30, 30, 30, 0.85)'
  //         }
  //       }
  //     }
  //   }
  // }

  // Blue ToolTip

  components: {
    MuiTooltip: {
      defaultProps: {
        enterDelay: 150,
        enterNextDelay: 300
      },
      styleOverrides: {
        popper: {
          '& .MuiTooltip-tooltip': {
            background: '#e9feff',
            color: '#6B6B6B',
            fontSize: '0.9rem',
            fontWeight: 600,
            letterSpacing: '0.02em',
            borderRadius: '14px',
            padding: '10px 14px',
            backdropFilter: 'blur(16px) saturate(200%)',
            WebkitBackdropFilter: 'blur(16px) saturate(200%)',
            transition: 'opacity 0.3s ease-in-out, transform 0.25s ease-in-out',
            transform: 'scale(1) translateY(4px)',
            border: '1px solid rgba(48, 231, 237, 0.55)'
          },
          '& .MuiTooltip-arrow': {
            color: '#6B6B6B'
          }
        }
      }
    }
  }
  // components: {
  // 	MuiButton: {
  // 		styleOverrides: {
  // 			root: { 	// root is like it will change every type of button in the project insted use your required Type
  // 				borderRadius: 20,
  // 			},
  // 		},
  // 	},
  // },
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <Provider store={appStore}>
    <ThemeProvider theme={appTheme}>
      <CssBaseline />
      <ToastContainer
        position="bottom-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick={false}
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        transition={Zoom}
      />
      <App />
    </ThemeProvider>
  </Provider>
);
