import { useDispatch } from "react-redux";
import { useMemo } from "react";

import { AppDispatch } from "../../redux/app.store";
import { getapps } from "../../redux/reducers/apps.reducer";
import AppList from "../../components/dashboard/AppList";

const AppListPage:React.FC=()=> {
  const dispatch = useDispatch<AppDispatch>();
  const getData = async () => {
    await dispatch(getapps(null));
  };
  useMemo(() => {
    getData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <AppList />;
}
export default AppListPage;
