// Global imports
import {
  useMemo
  //  useState
} from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import AppDetails from '../../components/apps/AppDetails';
import { AppDispatch } from '../../redux/app.store';
import { getapp, updateAppId } from '../../redux/reducers/apps.reducer';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';

const AppDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const getData = async () => {
    try {
      if (id) {
        const getAppData = await dispatch(getapp(id));
        const updateAppData = await dispatch(updateAppId(id));
        if (getAppData.payload?.status) {
          // setSnackbarOpen({
          //   status: false,
          //   message:
          //     getAppData.payload?.message ||
          //     updateAppData.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          // toast.success(
          //   getAppData.payload?.message ||
          //     updateAppData.payload?.message ||
          //     'Success.'
          // );
        } else {
          toast.error(
            getAppData.payload?.message ||
              updateAppData.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useMemo(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <AppDetails />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default AppDetailsPage;
