// Global imports
import { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import AppRegistration from '../../components/apps/AppRegistration';
import '../../css/app-registration-styles.scss';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  getappdata,
  getindustryappprocess
} from '../../redux/reducers/apps.reducer';
import { getindustrytypes } from '../../redux/reducers/org.reducer';
import { capitalizeWords } from '../../utils/functions';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';

const AppRegistrationPage: React.FC = () => {
  const { id } = useParams();
  const trimmedId = id ? id.trim().replace(/^, /, '') : '';
  const dispatch = useDispatch<AppDispatch>();
  const appState = useSelector((state: AppState) => state.app);
  const [industryTypes, setIndustryTypes] = useState<any>([]);
  const [industryAppProcess, setIndustryAppProcess] = useState<any>([]);
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const getIndustryTypes = async () => {
    try {
      const industryTypesData = await dispatch(getindustrytypes(null));
      if (industryTypesData.payload.status) {
        const data = industryTypesData.payload.data.map((type: any) => {
          return {
            label: capitalizeWords(type?.industry_type),
            value: type.industry_type_id
          };
        });
        setIndustryTypes(data);
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     industryTypesData?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          industryTypesData?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getIndustryAppProcess = async () => {
    try {
      const industryAppProcessData = await dispatch(
        getindustryappprocess(null)
      );
      if (industryAppProcessData.payload.status) {
        const data = industryAppProcessData.payload.data.map(
          (appProcess: any) => {
            return {
              label: capitalizeWords(appProcess.process_name),
              value: appProcess.industry_app_process_id
            };
          }
        );
        setIndustryAppProcess(data);
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     industryAppProcessData?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          industryAppProcessData?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getData = async () => {
    try {
      if (trimmedId) {
        const appdata = await dispatch(getappdata(trimmedId));
        if (appdata.payload?.status) {
          // setSnackbarOpen({
          //   status: false,
          //   message:
          //     appdata.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          // toast.success(appdata.payload?.message || 'Success.');
        }
      } else {
        toast.error('Something Went Wrong, Please Try Again Later.');
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useMemo(() => {
    if (id) {
      getData();
    }
    getIndustryTypes();
    getIndustryAppProcess();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);
  return (
    <>
      <AppRegistration
        appData={appState.app}
        industryTypes={industryTypes}
        industryAppProcess={industryAppProcess}
      />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default AppRegistrationPage;
