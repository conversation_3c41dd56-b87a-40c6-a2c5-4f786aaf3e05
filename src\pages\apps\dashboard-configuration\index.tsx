// Global imports
import { useMemo, useState } from 'react';
import { Box, Button, Grid, Modal, Typography } from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local imports
import GridstyleConfiguration from '../../../components/apps/dashboard-configuration/GridstyleConfiguration';
import ListStyleConfiguration from '../../../components/apps/dashboard-configuration/ListStyleConfiguration';
import Popstyles from '../../../components/apps/dashboard-configuration/Popstyles';
import { SubMenu } from '../../../components/form.elements';
import klezalogo from '../../../assets/fab-logo.png';
import { AppDispatch } from '../../../redux/app.store';
import {
  getdashboardconfig,
  getdashboardconfigpreviewdata,
  getprimaryform
} from '../../../redux/reducers/apps.reducer';
import Shell from '../../../components/layout/Shell';
import '../../../css/index-dashboard-config-styles.scss';
// import { SnackbarElement } from '../../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../../types';

const DashboardConfiguration: React.FC = () => {
  const { appId } = useParams<{ appId: string }>();
  const dispatch = useDispatch<AppDispatch>();

  const [style, setStyle] = useState('Grid');
  const [open, setOpen] = useState(false);
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => setOpen(false);

  const getData = async () => {
    try {
      if (appId) {
        const response1 = await dispatch(getprimaryform(appId));
        if (response1?.payload?.status) {
          // setSnackbarOpen({
          //   status: false,
          //   message:
          //     response1.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          // toast.success(response1.payload?.message || 'Success.');
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message:
          //     response1.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          toast.error(
            response1.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }

        const response2 = await dispatch(getdashboardconfig(appId));
        if (response2.payload.status) {
          // toast.success(response2.payload?.message || 'Success.');
          const updateStyle = response2?.payload?.data?.listing_style
            ? response2?.payload?.data?.listing_style
            : 'Grid';
          setStyle(updateStyle);
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message: 'Something Went Wrong, Please Try Again Later.'
          // });
          toast.error('Something Went Wrong, Please Try Again Later.');
        }

        const response3 = await dispatch(getdashboardconfigpreviewdata(appId));

        if (response3.payload.status) {
          // setSnackbarOpen({
          //   status: false,
          //   message:
          //     response3.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          toast.success(response3.payload?.message || 'Success.');
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message:
          //     response3.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          toast.error(
            response3.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };
  useMemo(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <>
      <Shell subMenu={getSubMenu()}>
        <Box className="dsbrd-container">
          <Box className="dsbrd-box">
            <Grid sx={{ padding: '11px' }}>
              <Grid item xs={4}>
                <Button
                  style={{
                    color: style === 'Grid' ? 'blue' : 'inherit',
                    textTransform: 'capitalize'
                  }}
                  onClick={() => setStyle('Grid')}
                >
                  Grid
                </Button>
                <Typography component="span" sx={{ margin: '0 8px' }}>
                  |
                </Typography>
                <Button
                  style={{
                    color: style === 'List' ? 'blue' : 'inherit',
                    textTransform: 'capitalize'
                  }}
                  onClick={() => setStyle('List')}
                >
                  List
                </Button>
              </Grid>
              <Grid item xs={4} textAlign="right">
                <Button className="dsbrd-button-edit" onClick={handleOpen}>
                  Edit
                </Button>
              </Grid>

              <Grid container alignItems="center">
                <Grid item xs={4}>
                  <DashboardIcon fontSize="medium" />
                </Grid>

                <Grid item xs={4} container justifyContent="center">
                  <img src={klezalogo} alt="Logo" style={{ height: 40 }} />
                </Grid>

                <Grid item xs={4} container justifyContent="flex-end">
                  <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                    Welcome
                    <Typography
                      variant="body1"
                      component="span"
                      sx={{ fontWeight: 'bold', ml: 1 }}
                    >
                      Lakshmi
                    </Typography>
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <Box>
              {style === 'Grid' ? (
                <GridstyleConfiguration />
              ) : (
                <ListStyleConfiguration />
              )}
            </Box>
          </Box>
        </Box>

        <Modal open={open} onClose={handleClose}>
          <Box className="dsbrd-modal-box">
            <Popstyles style={style} setOpen={setOpen} setStyle={setStyle} />
          </Box>
        </Modal>
      </Shell>
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default DashboardConfiguration;
