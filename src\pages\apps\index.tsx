// Global imports
import { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local imports
import AppsList from '../../components/apps/AppsList';
import { AppDispatch, AppState } from '../../redux/app.store';
import { getapps } from '../../redux/reducers/apps.reducer';
import LoaderUI from '../../components/reusable/loaderUI';

const AppsPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, apps } = useSelector((state: AppState) => state.app);

  const getAppsList = async () => {
    try {
      const response = await dispatch(getapps(null));

      if (!response.payload.status || response?.payload?.statusCode) {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong Please try again later'
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  useMemo(() => {
    getAppsList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return isLoading ? <LoaderUI /> : <AppsList appList={apps} />;
};
export default AppsPage;
