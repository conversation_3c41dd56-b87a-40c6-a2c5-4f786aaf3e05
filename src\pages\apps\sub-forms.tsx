// Global imports
import {
  useMemo
  //  useState
} from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import { AppDispatch } from '../../redux/app.store';
import { getsubforms } from '../../redux/reducers/apps.reducer';
import SubForms from '../../components/apps/SubForms';

const SubFormsPage: React.FC = () => {
  const { formId } = useParams<{ formId: string }>();
  const dispatch = useDispatch<AppDispatch>();

  const getData = async () => {
    try {
      if (formId) {
        const res = await dispatch(getsubforms(formId));

        if (res?.payload?.status) {
          // toast.success(res?.payload?.message);
        } else {
          toast.error(
            res?.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useMemo(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <SubForms />;
};
export default SubFormsPage;
