// Global Imports
import {
  useMemo
  // useState
} from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
// import { SnabackBarState } from '../../types';
import { AppDispatch } from '../../redux/app.store';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
import { getaddressconfig } from '../../redux/reducers/addressconfig.reducer';
import AddressConfiguration from '../../components/configurations/AddressConfiguration';

const AddressConfigurationPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  const getAddressConfigData = async () => {
    try {
      const response = await dispatch(getaddressconfig(null));
      if (response.payload.status) {
        // setSnackbarOpen({
        //   status: true,
        //   message: 'Something Went Wrong Please try again later.'
        // });
        // toast.success(response?.payload?.message || 'Success');
      } else {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong Please try again later.'
        );
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message: error?.message || 'Something Went Wrong Please try again later'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };
  // const changeSnackBarStatus = (bool: boolean) => {
  //   setSnackbarOpen({
  //     ...snackbarOpen,
  //     status: bool
  //   });
  // };

  useMemo(() => {
    getAddressConfigData();
  }, []);

  return (
    <>
      <AddressConfiguration />
      {/* <SnackbarElement
        statusType="error"
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={() => changeSnackBarStatus(false)}
        message={snackbarOpen?.message}
      /> */}
    </>
  );
};

export default AddressConfigurationPage;
