// Global Imports
import { useSearchParams } from 'react-router-dom';
import {
  useMemo
  //  useState
} from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
// import { SnabackBarState } from '../../types';
import {
  getlinkredirectdata,
  getonedrivefolder,
  getstorageconfig
} from '../../redux/reducers/addressconfig.reducer';
import { AppDispatch } from '../../redux/app.store';
import FileManager from '../../components/configurations/FileManager';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';

const GetLinkPage: React.FC = () => {
  const orgId = localStorage.getItem('org_id');
  const [queryParam] = useSearchParams();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  const getStorageDetails = async () => {
    const response = await dispatch(getstorageconfig(null));
    if (response.payload.status) {
      await dispatch(getonedrivefolder(null));
    }
  };
  const getData = async () => {
    try {
      const response = await dispatch(
        getlinkredirectdata({ orgId, code: queryParam.get('code') })
      );
      if (response.payload.statusCode) {
        // setSnackbarOpen({
        //   status: true,
        //   message: 'Something Went Wrong Please try again later.'
        // });
        toast.error(
          response.payload.message ||
            'Something Went Wrong Please try again later.'
        );
      }
      if (response.payload.status) {
        getStorageDetails();
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message: error?.message || 'Something Went Wrong Please try again later'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  // const changeSnackBarStatus = (bool: boolean) => {
  //   setSnackbarOpen({
  //     ...snackbarOpen,
  //     status: bool
  //   });
  // };

  useMemo(() => {
    getData();
  }, [orgId]);

  return (
    <>
      <FileManager />
      {/* <SnackbarElement
        statusType="error"
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={() => changeSnackBarStatus(false)}
        message={snackbarOpen?.message}
      /> */}
    </>
  );
};

export default GetLinkPage;
