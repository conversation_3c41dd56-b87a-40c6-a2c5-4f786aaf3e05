// Global Imports
import { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import DashboardPanel from '../../components/dashboard/DashboardPanel';
import { dashboardapps } from '../../redux/reducers/dashBoard.reducer';
import { AppDispatch, AppState } from '../../redux/app.store';
import LoaderUI from '../../components/reusable/loaderUI';

const Dashboard: React.FC = () => {
  const { isLoading } = useSelector((state: AppState) => state.dashBoard);
  const dispatch = useDispatch<AppDispatch>();

  const getData = async () => {
    try {
      const response = await dispatch(dashboardapps(null));

      if (!response.payload.status || response?.payload?.statusCode) {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong Please try again later'
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  useMemo(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return isLoading ? <LoaderUI /> : <DashboardPanel />;
};

export default Dashboard;
