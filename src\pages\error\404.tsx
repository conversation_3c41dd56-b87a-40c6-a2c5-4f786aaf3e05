import React from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';

import logo from '../../../public/logo.svg';

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box
      sx={{
        gap: 4,
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        alignContent: 'center',
        flexDirection: 'column',
        justifyContent: 'center'
      }}
    >
      <Box
        sx={{
          textAlign: 'center'
        }}
      >
        <img src={logo} width="200" alt="FAB" />
        <Typography variant="h3">Kleza FAB</Typography>
      </Box>
      <Typography variant="h5">404 page not found</Typography>
      <Button variant="outlined" onClick={() => navigate(-1)}>
        Goback
      </Button>
    </Box>
  );
};

export default NotFound;
