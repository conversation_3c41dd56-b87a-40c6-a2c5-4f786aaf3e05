import { useParams } from 'react-router-dom';
import {
  useMemo
  // useState
} from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import FormShell from '../../components/form-builder/FormShell';
import { AppDispatch } from '../../redux/app.store';
import {
  getform,
  getformFields
  // updateFormId
} from '../../redux/reducers/form.reducer';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';

const CreateFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  const getData = async () => {
    try {
      if (id) {
        const res = await dispatch(getform(id));
        if (res.payload.status) {
          // toast.success(res?.payload?.message || 'Success.');
          const data = res.payload?.data?.is_quiz_form
            ? {
                quiz_form_fields: true
              }
            : '';

          await dispatch(getformFields(data));

          if (res.payload.status) {
            // setSnackbarOpen({
            //   status: true,
            //   message:
            //     res2?.payload?.message ||
            //     'Something Went Wrong, Please Try Again Later.'
            // });
            // toast.success(res2?.payload?.message || 'Success.');
          }
        } else {
          // setSnackbarOpen({
          //   status: true,
          //   message:
          //     res?.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });

          toast.error(res?.payload?.message || ' Not Found');
        }
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useMemo(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  return (
    <>
      <FormShell />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default CreateFormPage;
