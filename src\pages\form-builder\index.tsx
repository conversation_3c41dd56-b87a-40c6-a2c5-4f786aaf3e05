import { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import { AppDispatch, AppState } from '../../redux/app.store';
import { getapps } from '../../redux/reducers/apps.reducer';
import { AppsList } from '../../components/form-builder/AppList';
import LoaderUI from '../../components/reusable/loaderUI';

const FormBuilderPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  const { isLoading, apps } = useSelector((state: AppState) => state.app);

  const getAppsAlongWithForms = async () => {
    try {
      const response = await dispatch(getapps(null));

      if (!response.payload.status || response?.payload?.statusCode) {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong Please try again later'
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  useMemo(() => {
    getAppsAlongWithForms();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return isLoading ? <LoaderUI /> : <AppsList appsList={apps} />;
};

export default FormBuilderPage;
