import {
  useMemo
  // useState
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import PreviewForm from '../../components/form-builder/PreviewForm';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  getcountrieslist,
  getform,
  getthemes
} from '../../redux/reducers/form.reducer';
import { getapps } from '../../redux/reducers/apps.reducer';
import { getassessmentclients } from '../../redux/reducers/clients.reducer';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';

const PreviewFormPage: React.FC = () => {
  const params = useParams<{ id: string }>();
  const id = params.id ?? '';
  const dispatch = useDispatch<AppDispatch>();
  // const formState = useSelector((state: AppState) => state.form);
  const { appId, form }: any = useSelector((state: AppState) => state.form);
  const userType = localStorage.getItem('user_type');
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  try {
    const getData = async () => {
      const res = await dispatch(getform(id));
      if (res.payload.status) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     res?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        // toast.success(res?.payload?.message || 'Success');
      } else {
        toast.error(
          res?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
      if (userType === 'organization') {
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const res = await dispatch(getthemes(appId));
        if (res?.payload?.statusCode) {
          toast.error(
            res?.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      }
    };

    const getCountriesData = async () => {
      try {
        const res = await dispatch(getcountrieslist(null));
        if (res?.payload?.statusCode) {
          // setSnackbarOpen({
          //   status: true,
          //   message:
          //     res?.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          toast.error(
            res?.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      } catch (err) {
        toast.error('Something Went Wrong, Please Try Again Later.');
      }
    };
    const getClientsList = async () => {
      const response = await dispatch(getapps(null));
      if (response.payload.status) {
        response.payload.data.forEach(async (app: any) => {
          if (app?.industry_app_process?.process_code === 'HC_CLIASS') {
            await dispatch(getassessmentclients(app?.app_id));
          }
        });
      }
    };

    useMemo(() => {
      getData();
      getCountriesData();
      getClientsList();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
  } catch (err) {
    // setSnackbarOpen({
    //   status: true,
    //   message: 'Something Went Wrong, Please Try Again Later.'
    // });
    toast.error('Something Went Wrong, Please Try Again Later.');
  }

  return (
    <>
      <PreviewForm form={form} />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default PreviewFormPage;
