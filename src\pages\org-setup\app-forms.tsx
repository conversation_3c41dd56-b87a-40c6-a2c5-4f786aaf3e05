import { useDispatch } from 'react-redux';
import {
  useEffect
  //  useState
} from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// import { SnabackBarState } from '../../types';
import { AppDispatch } from '../../redux/app.store';
import { getapp } from '../../redux/reducers/apps.reducer';
import AppForms from '../../components/org-setup/AppForms';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';

const AppFormsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const getApp = async () => {
    try {
      if (id) {
        const response = await dispatch(getapp(id));
        if (response?.payload?.status) {
          // setSnackbarOpen({
          //   status: true,
          //   message:
          //     response?.payload?.message ||
          //     'Something Went Wrong, Please Try Again Later.'
          // });
          // toast.success(response?.payload?.message || 'Success.');
        } else {
          toast.error(
            response?.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };
  useEffect(() => {
    getApp();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <AppForms />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};

export default AppFormsPage;
