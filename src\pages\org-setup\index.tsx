// Global imports
import { useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local imports
import '../../css/org-set-up-styles.scss';
import { AppDispatch } from '../../redux/app.store';
import { ORGDATA } from '../../types';
import { getorganizations } from '../../redux/reducers/org.reducer';
import OrganizationsList from '../../components/org-setup/OrganizationsList';

const OrgSetupPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [list, setList] = useState<ORGDATA[]>([]);

  const fetchOrganizationsList = async () => {
    try {
      const orgData = await dispatch(getorganizations(null));
      if (orgData?.payload?.status) {
        setList(orgData?.payload?.data);
      } else {
        toast.error(
          orgData?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useMemo(() => {
    fetchOrganizationsList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <OrganizationsList list={list} />;
};

export default OrgSetupPage;
