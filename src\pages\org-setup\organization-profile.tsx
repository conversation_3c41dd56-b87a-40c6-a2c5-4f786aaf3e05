import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import '../../css/org-details-styles.scss';
import { AppDispatch } from '../../redux/app.store';
import { getorganizationdetails } from '../../redux/reducers/org.reducer';
import { ORGANIZATIONAPPLISTDETAILS } from '../../types';
import OrganizationDetails from '../../components/org-setup/OrganizationDetails';

const OrganizationProfile: React.FC = () => {
  const param = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const [orgData, setOrgData] = useState<ORGANIZATIONAPPLISTDETAILS[]>([]);

  useMemo(() => {
    try {
      (async () => {
        const orgaData = await dispatch(getorganizationdetails(param.id));
        if (orgaData.payload.status) {
          setOrgData(orgaData?.payload?.data);
        } else {
          toast.error(
            orgaData?.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      })();
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <OrganizationDetails orgData={orgData} />;
};

export default OrganizationProfile;
