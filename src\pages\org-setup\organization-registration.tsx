import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import React, { useMemo, useState } from 'react';
import { toast } from 'react-toastify';

import {
  APPS,
  INDUSTRYTYPE,
  IndustryTypes
  // SnabackBarState
} from '../../types';
import {
  getorganizationdetails,
  getindustrytypes
} from '../../redux/reducers/org.reducer';
import '../../css/org-registration-styles.scss';
import { AppDispatch } from '../../redux/app.store';
import { getapps } from '../../redux/reducers/apps.reducer';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
import OrganizationRegistrationForm from '../../components/org-setup/Registration';

const OrganizationRegistration: React.FC = () => {
  const param = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const [appsData, setAppsData] = useState<APPS[]>([]);
  const [selectedAppsList, setSelectedAppsList] = useState<APPS[]>([]);
  const [industryTypes, setIndustryTypes] = useState<IndustryTypes[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    logo: '',
    custom_name: '',
    email: '',
    password: '',
    industry_type_id: '',
    mobile_number: ''
  });
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const getData = async () => {
    try {
      const orgData = await dispatch(getorganizationdetails(param.id));
      if (orgData?.payload?.status) {
        const data = {
          ...orgData?.payload?.data,
          industry_type_id: orgData.payload.data.industry_type.industry_type_id
        };

        setFormData(data);
        setSelectedAppsList(data?.apps?.map((app: APPS) => app?.app_id));

        const apps = await dispatch(getapps(data.industry_type_id));
        if (apps.payload.status) {
          // toast.success(apps?.payload?.message || 'Success');
          setAppsData(apps?.payload?.data);
        }
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     orgData?.payload?.message ||
        //     'Something went wrong. Please try again later.'
        // });
        toast.error(
          orgData?.payload?.message ||
            'Something went wrong. Please try again later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something went wrong. Please try again later.'
      // });
      toast.error('Something went wrong. Please try again later.');
    }
  };

  const getIndustryData = async () => {
    try {
      const industryTypesData = await dispatch(getindustrytypes(null));
      if (industryTypesData.payload.status) {
        const data = industryTypesData.payload.data.map(
          (type: INDUSTRYTYPE) => ({
            label: type.industry_type || '',
            value: type.industry_type_id || ''
          })
        );
        setIndustryTypes(data);
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     industryTypesData?.payload?.message ||
        //     'Something went wrong. Please try again later.'
        // });
        // toast.success(
        //   industryTypesData?.payload?.message ||
        //     'Something went wrong. Please try again later.'
        // );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something went wrong. Please try again later.'
      // });
      toast.error('Something went wrong. Please try again later.');
    }
  };

  useMemo(() => {
    if (param.id) {
      getData();
    }
    getIndustryData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <OrganizationRegistrationForm
        getappsData={appsData}
        selectedAppsList={selectedAppsList}
        industryTypes={industryTypes}
        formData={formData}
      />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </>
  );
};

export default OrganizationRegistration;
