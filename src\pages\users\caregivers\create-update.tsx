// Global Imports
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import { getCaregiverDetails } from '../../../redux/reducers/caregivers.reducer';
import { AppDispatch } from '../../../redux/app.store';
import CaregiverCreateUpdate from '../../../components/users/caregivers/CaregiverCreateUpdate';
import { getapps } from '../../../redux/reducers/apps.reducer';
import { getassessmentclients } from '../../../redux/reducers/clients.reducer';

const CaregiverCreateUpdatePage: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const fetchCaregiver = async (caregiverId: any) => {
    try {
      const response = await dispatch(getCaregiverDetails(caregiverId));
      if (response?.payload?.status) {
        toast.success(response?.payload?.message);
      } else {
        throw Error(response?.payload?.message);
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getClientsList = async () => {
    const response = await dispatch(getapps(null));
    if (response.payload.status) {
      response.payload.data.forEach(async (app: any) => {
        if (app?.industry_app_process?.process_code === 'HC_CLIASS') {
          // setAppId(app?.app_id);
          await dispatch(getassessmentclients(app?.app_id));
        }
      });
    }
  };

  useEffect(() => {
    getClientsList();

    if (id !== undefined || id !== undefined) {
      fetchCaregiver(id);
    }
  }, [id]);

  return <CaregiverCreateUpdate />;
};

export default CaregiverCreateUpdatePage;
