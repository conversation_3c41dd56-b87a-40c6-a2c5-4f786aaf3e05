// Global Imports
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import { AppDispatch } from '../../../redux/app.store';
import {
  getclientforms,
  getassessmentclients
} from '../../../redux/reducers/clients.reducer';
import FormDisplay from '../../../components/users/employee-onboarding/FormDisplay';
import { getapps } from '../../../redux/reducers/apps.reducer';

const CaregiverDetailsPage: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const fetchCaregiverForms = async (caregiverId: any) => {
    try {
      const response = await dispatch(
        getclientforms({ id: caregiverId, type: 'caregiver' })
      );

      if (response?.payload?.status) {
        toast.success(response?.payload?.message);
      } else {
        throw Error(response?.payload?.message);
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getClientsList = async () => {
    const response = await dispatch(getapps(null));
    if (response.payload.status) {
      response.payload.data.forEach(async (app: any) => {
        if (app?.industry_app_process?.process_code === 'HC_CLIASS') {
          // setAppId(app?.app_id);
          await dispatch(getassessmentclients(app?.app_id));
        }
      });
    }
  };

  useEffect(() => {
    if (id !== undefined || id !== undefined) {
      fetchCaregiverForms(id);
      getClientsList();
    }
  }, [id]);

  return <FormDisplay module="caregiver" />;
};

export default CaregiverDetailsPage;
