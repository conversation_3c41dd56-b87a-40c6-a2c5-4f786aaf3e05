// Global imports
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  TextField,
  Typography
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import ChevronRightOutlinedIcon from '@mui/icons-material/ChevronRightOutlined';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonAddAltIcon from '@mui/icons-material/PersonAddAlt';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import { AppDispatch, AppState } from '../../../redux/app.store';
import 'react-tagsinput/react-tagsinput.css';
import { SubMenu } from '../../../components/form.elements';
import Sidebar from '../../../components/reusable/Sidebar';
import Shell from '../../../components/layout/Shell';
import { UserNavBarModules } from '../../../components/users/UsersList';
import LoaderUI from '../../../components/reusable/loaderUI';
import '../../../css/client-assessment-styles.scss';
import {
  getCaregivers,
  removeCaregiver,
  updateCaregiver
} from '../../../redux/reducers/caregivers.reducer';
import { getapps } from '../../../redux/reducers/apps.reducer';
import { getassessmentclients } from '../../../redux/reducers/clients.reducer';

export interface AssessmentClients {
  client_id: string;
  mobile_number: string;
  name: string;
}
const CaregiversListPage: React.FC = () => {
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedCaregiverId, setSelectedCaregiverId] = useState<string | null>(
    null
  );
  const [openClientDialog, setOpenClientDialog] = useState(false);

  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();

  const { caregiversList, isLoading }: any = useSelector(
    (state: AppState) => state.caregivers
  );

  const { assessmentClients }: any = useSelector(
    (state: AppState) => state.clients
  );
  const [client, setClient] = useState<AssessmentClients[]>([]);

  const handleClients = (e: any, value: AssessmentClients[]) => {
    console.log(e?.target?.value);
    setClient(value);
  };

  const fetchCaregiversList = async () => {
    try {
      const response = await dispatch(getCaregivers(null));

      if (response?.payload?.status) {
        toast.success(response?.payload?.message);
      } else {
        throw Error(response?.payload?.message);
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const handleDelete = async () => {
    if (selectedCaregiverId) {
      try {
        const response = await dispatch(removeCaregiver(selectedCaregiverId));

        if (response?.payload?.status) {
          toast.success(response?.payload?.message);
        } else {
          throw Error(response?.payload?.message);
        }
      } catch (error) {
        toast.error('Something Went Wrong, Please Try Again Later.');
      }

      fetchCaregiversList();
      setOpenDeleteDialog(false);
      setSelectedCaregiverId(null);
    }
  };

  const handleOpenDeleteDialog = (caregiverId: string) => {
    setSelectedCaregiverId(caregiverId);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedCaregiverId(null);
  };

  const handleSaveClients = async () => {
    if (selectedCaregiverId) {
      const selectedClientIds = client.map((c) => c.client_id);
      const careGiver = caregiversList.find(
        (c: any) => c.caregiver_id === selectedCaregiverId
      );
      const data = {
        ...careGiver,
        clients: selectedClientIds
      };
      try {
        const response = await dispatch(
          updateCaregiver({ id: selectedCaregiverId, data })
        );

        if (response?.payload?.status) {
          fetchCaregiversList();
          setOpenClientDialog(false);
          setSelectedCaregiverId(null);
        } else {
          const errorMessage = response?.payload?.message;
          if (Array.isArray(errorMessage) && errorMessage.length > 0) {
            errorMessage.forEach((error: any) => {
              toast.error(error);
            });
          } else {
            toast.error(errorMessage || 'An error occurred. Please try again.');
          }
        }
      } catch (error: any) {
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  };
  const getClientsList = async () => {
    const response = await dispatch(getapps(null));
    if (response.payload.status) {
      response.payload.data.forEach(async (app: any) => {
        if (app?.industry_app_process?.process_code === 'HC_CLIASS') {
          await dispatch(getassessmentclients(app?.app_id));
        }
      });
    }
  };

  const handleOpenClientDialog = (caregiverId: string) => {
    setSelectedCaregiverId(caregiverId);
    const selectedClients = assessmentClients.filter(
      (mapClient: AssessmentClients) =>
        caregiversList
          .find((c: any) => c.caregiver_id === caregiverId)
          .clients.includes(mapClient?.client_id)
    );
    setClient(selectedClients);
    setOpenClientDialog(true);
  };
  const handleCloseClientDialog = () => {
    setSelectedCaregiverId(null);
    setOpenClientDialog(false);
  };

  useEffect(() => {
    getClientsList();
    fetchCaregiversList();
  }, []);

  const columns = [
    { field: 'name', headerName: 'Name', flex: 1 },
    { field: 'email', headerName: 'Email', flex: 1 },
    { field: 'mobile_number', headerName: 'Phone number', flex: 1 },
    {
      field: 'action',
      headerName: 'Action',
      flex: 1,
      renderCell: (params: any) => (
        <Box>
          {/* Edit Icon */}
          <IconButton
            onClick={() =>
              navigate(`/users/caregivers/create-update/${params.id}`)
            }
          >
            <EditIcon color="primary" />
          </IconButton>

          {/* Delete Icon */}
          <IconButton onClick={() => handleOpenDeleteDialog(params.id)}>
            <DeleteIcon color="error" />
          </IconButton>

          <IconButton onClick={() => handleOpenClientDialog(params.id)}>
            <PersonAddAltIcon color="primary" />
          </IconButton>

          {/* Right Arrow Icon */}
          <IconButton
            onClick={() => navigate(`/users/caregivers/${params.id}`)}
          >
            <ChevronRightOutlinedIcon color="primary" />
          </IconButton>
        </Box>
      )
    }
  ];

  const menuItems = UserNavBarModules();

  const getSubMenu = () => (
    <SubMenu backNavigation enableCreateCaregiverButton />
  );

  const getDrawer = () => (
    <Sidebar menuItems={menuItems} userType={undefined} />
  );

  return (
    <>
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete this caregiver? This action cannot
            be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Clients Dialog */}
      <Dialog
        open={openClientDialog}
        onClose={handleCloseClientDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle id="delete-dialog-title">Select Clients</DialogTitle>
        <DialogContent>
          <Autocomplete
            multiple
            options={assessmentClients}
            getOptionLabel={(option) =>
              `${option.name} | ${option.mobile_number} `
            }
            value={client}
            onChange={handleClients}
            isOptionEqualToValue={(option, value) =>
              option.client_id === value.client_id
            }
            renderTags={(value: AssessmentClients[], getTagProps) =>
              value.map((option: AssessmentClients, index: number) => (
                <Chip
                  {...getTagProps({ index })}
                  // variant="outlined"
                  label={`${option.name} | ${option.mobile_number}`}
                  key={`${index + 1}`}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                // variant="outlined"
                // label="Select Currencies"
                placeholder="Choose..."
              />
            )}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseClientDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSaveClients} color="primary" autoFocus>
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Existing Shell and DataGrid */}
      <Shell showDrawer drawerData={getDrawer()} subMenu={getSubMenu()}>
        <Box
          sx={{ display: 'flex', overflow: 'hidden', flexDirection: 'column' }}
        >
          {isLoading ? (
            <LoaderUI />
          ) : (
            <Box
              sx={{
                backgroundColor: '#FFFFFF',
                padding: '10px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                minHeight: 'calc(100vh - 100px)'
              }}
              className="main-container"
            >
              <Box sx={{ width: '100%', overflowX: 'auto' }}>
                <Box sx={{ backgroundColor: '#FAF9F8' }}>
                  <div>
                    <Typography
                      sx={{
                        fontSize: '22px',
                        fontWeight: '600',
                        // marginTop: '50px',
                        marginBottom: '10px',
                        padding: '2px',
                        marginLeft: '20px'
                      }}
                    >
                      Caregivers
                    </Typography>
                  </div>
                </Box>

                <Box className="content">
                  <Box className="div-styles">
                    <Box
                      sx={{ width: '100%', flexGrow: 1, minHeight: '300px' }}
                    >
                      {caregiversList?.length > 0 ? (
                        <DataGrid
                          rows={caregiversList}
                          getRowId={(row) => row?.caregiver_id}
                          columns={columns}
                          initialState={{
                            pagination: {
                              paginationModel: { page: 0, pageSize: 5 }
                            }
                          }}
                          pageSizeOptions={[5, 10]}
                          disableRowSelectionOnClick
                          // checkboxSelection
                          getRowClassName={() => `striped-row`}
                          sx={{
                            '& .MuiDataGrid-columnHeader': {
                              '& .MuiDataGrid-menuIcon': {
                                visibility: 'visible',
                                width: 'auto'
                              }
                            }
                          }}
                        />
                      ) : (
                        <Box className="no-applicants">
                          <Typography
                            sx={{ fontSize: '17px', fontWeight: '500' }}
                          >
                            No records found.
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>
          )}
        </Box>
      </Shell>
    </>
  );
};

export default CaregiversListPage;
