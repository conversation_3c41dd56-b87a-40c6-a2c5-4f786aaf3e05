import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import FormDisplay from '../../../components/users/employee-onboarding/FormDisplay';

const FormDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const isProduction = import.meta.env.VITE_APP_MODE === 'production';

  useEffect(() => {
    if (isProduction) {
      // Disable Right Click
      const disableRightClick = (event: MouseEvent) => event.preventDefault();
      document.addEventListener('contextmenu', disableRightClick);

      // Disable Developer Shortcuts
      const disableDevTools = (event: KeyboardEvent) => {
        if (
          event.key === 'F12' ||
          (event.ctrlKey &&
            event.shiftKey &&
            (event.key === 'I' || event.key === 'J')) ||
          (event.ctrlKey && event.key === 'U')
        ) {
          event.preventDefault();
        }
      };
      document.addEventListener('keydown', disableDevTools);

      // Detect DevTools Open and Redirect
      const checkDevTools = () => {
        if (
          window.outerWidth - window.innerWidth > 160 ||
          window.outerHeight - window.innerHeight > 160
        ) {
          alert('Developer tools detected! Redirecting...');
          navigate('/'); // Redirect to another page
        }
      };
      const interval = setInterval(checkDevTools, 2000);

      // Cleanup event listeners
      return () => {
        document.removeEventListener('contextmenu', disableRightClick);
        document.removeEventListener('keydown', disableDevTools);
        clearInterval(interval);
      };
    }
  }, [isProduction, navigate]);

  return <FormDisplay module="client-assessment" />;
};

export default FormDisplayPage;

// import React, { useEffect, useState } from 'react';
// import {
//   Box,
//   Typography,
//   Button,
//   TextField,
//   Dialog,
//   DialogActions,
//   DialogContent,
//   DialogContentText,
//   Grid,
//   Checkbox,
//   Link
// } from '@mui/material';
// import { useDispatch, useSelector } from 'react-redux';
// import { useParams } from 'react-router-dom';
// import TagsInput from 'react-tagsinput';

// import { SubMenu } from '../../../components/form.elements';
// import { AppDispatch } from '../../../redux/app.store';
// import {
//   checkfolder,
//   getclientforms,
//   sendpdf,
//   exportToCSV
// } from '../../../redux/reducers/clients.reducer';
// import { RootState } from '../../../redux/reducers';
// import '../../../css/form-values-styles.scss';

// const FormResponses: React.FC = () => {
//   const { id, appId } = useParams();

//   const dispatch = useDispatch<AppDispatch>();
//   const { clientForms } = useSelector((state: RootState) => state.clients);

//   useEffect(() => {
//     dispatch(getclientforms({ id, type: 'client-assessment' }));
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, []);

//   const [open, setOpen] = useState(false);
//   const [path, setPath]: any = useState();
//   const [type, setType]: any = useState();
//   const [tags, setTags] = useState<any>([]);

//   const handleSendMailOpen = () => {
//     setType('share');
//     setOpen(true);
//   };

//   const handleClickOpen = async () => {
//     setType('upload');
//     const response = await dispatch(
//       checkfolder({
//         clientId: id
//       })
//     );
//     if (response.payload.status) {
//       if (response.payload.isExisted) {
//         setOpen(true);
//       } else {
//         await dispatch(
//           sendpdf({
//             clientId: id,
//             data: {
//               request_type: 'upload',
//               app_id: appId
//             }
//           })
//         );
//       }
//     }
//   };

//   const handleExportCSVClick = async () => {
//     try {
//       const response: any = await dispatch(
//         exportToCSV({ client_id: id, exportToExcel: true })
//       );

//       // Extract the Blob from the response
//       const blob = new Blob([response.payload], {
//         type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
//       });

//       // Create a URL for the blob
//       const url = window.URL.createObjectURL(blob);

//       // Create an anchor element
//       const link = document.createElement('a');
//       link.href = url;

//       // Set the filename for the download
//       link.setAttribute('download', 'form_values.xlsx');

//       // Append the anchor to the body
//       document.body.appendChild(link);

//       // Trigger the download
//       link.click();

//       // Remove the anchor from the body
//       document.body.removeChild(link);

//       // Clean up the URL object
//       window.URL.revokeObjectURL(url);
//     } catch (error) {
//       // add snackbar to show error
//     }
//   };

//   const handleClose = () => {
//     setOpen(false);
//   };

//   const [checked, setChecked] = useState(false);
//   const [input, setInput]: any = useState();

//   const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     setChecked(event.target.checked);
//   };
//   const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     setInput(event.target.value);
//     // add snackbar to show error
//   };
//   const isValidEmail = (email: string): boolean => {
//     const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
//     return emailRegex.test(email);
//   };
//   const handleTagsChange = (tagss: any) => {
//     if (isValidEmail(tagss[tagss.length - 1])) {
//       setTags(tagss);
//     }
//   };

//   const stripHtmlTags = (html: any) => {
//     const doc = new DOMParser().parseFromString(html, 'text/html');
//     return doc.body.textContent || '';
//   };

//   return (
//     <div>
//       <SubMenu
//         backNavigation
//         uploadForms
//         exportCSV
//         sendMail
//         handleUploadClick={handleClickOpen}
//         handleExportCSVClick={handleExportCSVClick}
//         handleSendMailClick={handleSendMailOpen}
//       />

//       <Box sx={{ padding: '0px 100px' }}>
//         <Typography
//           sx={{
//             fontSize: '22px',
//             fontWeight: '600',
//             marginTop: '50px',
//             marginBottom: '10px',
//             padding: 2,
//             text: 'capitalize'
//           }}
//         >
//           Form Responses
//         </Typography>
//         <Box sx={{ padding: '20px' }}>
//           <Dialog
//             open={open}
//             onClose={handleClose}
//             maxWidth="sm"
//             fullWidth
//             PaperProps={{
//               component: 'form',
//               onSubmit: async (event: React.FormEvent<HTMLFormElement>) => {
//                 event.preventDefault();
//                 const formData = new FormData(event.currentTarget);
//                 const formJson = Object.fromEntries(
//                   (formData as any).entries()
//                 );
//                 if (type === 'share') {
//                   const email = formJson?.email;
//                   if (email) {
//                     const response = await dispatch(
//                       sendpdf({
//                         clientId: id,
//                         data: {
//                           request_type: 'share',
//                           app_id: appId,
//                           email,
//                           cc_emails: tags
//                         }
//                       })
//                     );
//                     if (response.payload.status) {
//                       handleClose();
//                     }
//                   }
//                 } else {
//                   const folderName = formJson?.folderName;
//                   const checkbox = formJson?.checkbox;
//                   // Object.keys(formJson).length == 1
//                   if (folderName && !checkbox) {
//                     const response = await dispatch(
//                       checkfolder({
//                         clientId: id,
//                         folderName
//                       })
//                     );
//                     if (response.payload.status) {
//                       if (response.payload.isExisted) {
//                         setPath(folderName);
//                         setInput('');
//                       } else {
//                         const response2 = await dispatch(
//                           sendpdf({
//                             clientId: id,
//                             data: {
//                               request_type: 'upload',
//                               app_id: appId,
//                               path: folderName
//                             }
//                           })
//                         );
//                         if (response2.payload.status) {
//                           setChecked(false);
//                           setPath('');
//                           setInput('');
//                           handleClose();
//                         }
//                       }
//                     }
//                   } else if (checkbox) {
//                     const response = await dispatch(
//                       sendpdf({
//                         clientId: id,
//                         data: {
//                           request_type: 'upload',
//                           app_id: appId
//                         }
//                       })
//                     );
//                     if (response.payload.status) {
//                       handleClose();
//                       setChecked(false);
//                       setPath('');
//                       setInput('');
//                     }
//                   }
//                 }
//               }
//             }}
//           >
//             <DialogContent sx={{ paddingLeft: 7, paddingRight: 7 }}>
//               {type === 'share' ? (
//                 <>
//                   <TextField
//                     autoFocus
//                     required
//                     margin="dense"
//                     id="email"
//                     name="email"
//                     label="Please enter email"
//                     placeholder="Enter Email"
//                     type="email"
//                     fullWidth
//                     variant="standard"
//                     InputLabelProps={{
//                       shrink: true,
//                       style: { color: 'black', fontSize: '25px' }
//                     }}
//                     InputProps={{
//                       style: { marginTop: '40px' }
//                     }}
//                   />
//                   <TagsInput value={tags} onChange={handleTagsChange} />
//                 </>
//               ) : (
//                 <>
//                   {path && (
//                     <DialogContentText
//                       sx={{
//                         fontSize: '20px',
//                         fontWeight: '500',
//                         color: 'black'
//                       }}
//                     >
//                       <Typography
//                         sx={{
//                           fontSize: '24px',
//                           fontWeight: '600',
//                           paddingTop: 4,
//                           text: 'capitalize'
//                         }}
//                       >
//                         Folder name already exists !
//                       </Typography>
//                       <Checkbox
//                         disabled={input !== ''}
//                         checked={checked}
//                         onChange={handleChange}
//                         inputProps={{ 'aria-label': 'controlled' }}
//                         sx={{ paddingLeft: 0 }}
//                         id="checkbox"
//                         name="checkbox"
//                       />
//                       Use same folder
//                     </DialogContentText>
//                   )}
//                   {path && (
//                     <Typography
//                       sx={{
//                         display: 'flex',
//                         alignItems: 'center',
//                         justifyContent: 'center',
//                         text: 'capitalize'
//                       }}
//                     >
//                       (or)
//                     </Typography>
//                   )}
//                   <TextField
//                     autoFocus
//                     disabled={checked}
//                     onChange={handleInputChange}
//                     margin="dense"
//                     id="name"
//                     name="folderName"
//                     label="Please enter new folder"
//                     placeholder="Enter Folder Name"
//                     type="text"
//                     fullWidth
//                     variant="standard"
//                     InputLabelProps={{
//                       shrink: true,
//                       style: { color: 'black', fontSize: '25px' }
//                     }}
//                     InputProps={{
//                       style: { marginTop: '40px' }
//                     }}
//                   />
//                 </>
//               )}
//             </DialogContent>
//             <DialogActions
//               sx={{ paddingBottom: 5, paddingLeft: 6, paddingRight: 5 }}
//             >
//               <Button onClick={handleClose}>Cancel</Button>
//               <Button type="submit">Ok</Button>
//             </DialogActions>
//           </Dialog>

//           {clientForms.map((client: any) => (
//             <Box
//               key={client?.form?.form_id}
//               sx={{ marginBottom: '20px', backgroundColor: '#ffffff' }}
//             >
//               <Box
//                 sx={{
//                   backgroundColor: '#cee4f4',
//                   height: '70px',
//                   display: 'flex',
//                   alignItems: 'center'
//                 }}
//               >
//                 <Typography
//                   sx={{
//                     marginLeft: '45px',
//                     fontWeight: '600',
//                     fontSize: '20px',
//                     text: 'capitalize'
//                   }}
//                 >
//                   {client?.form?.name}
//                 </Typography>
//               </Box>

//               {Object.keys(client?.form?.fields)?.map((group: any) => (
//                 <Box key={group} sx={{ padding: '20px 0px' }}>
//                   <Typography
//                     variant="h4"
//                     sx={{
//                       fontWeight: '600',
//                       fontSize: '20px',
//                       marginBottom: '5px',
//                       marginLeft: '45px',
//                       text: 'capitalize'
//                     }}
//                   >
//                     {client?.form?.fields[group]?.group_title}
//                   </Typography>
//                   {client?.form?.fields[group]?.sectionCaption && (
//                     <Typography
//                       variant="body1"
//                       color="textSecondary"
//                       sx={{ text: 'capitalize' }}
//                     >
//                       {client?.form?.fields[group]?.sectionCaption}
//                     </Typography>
//                   )}
//                   {client?.form?.fields[group]?.group_description && (
//                     <Box sx={{ paddingTop: 2 }}>
//                       <Typography
//                         variant="body1"
//                         sx={{ marginLeft: '45px', text: 'capitalize' }}
//                       >
//                         {client?.form?.fields[group]?.group_description}
//                       </Typography>
//                     </Box>
//                   )}

//                   <Grid container spacing={2} sx={{ paddingTop: 2 }}>
//                     {client?.form?.fields?.[group]?.fields?.map(
//                       (field: any) => (
//                         <React.Fragment key={field?.field_id}>
//                           <Grid item xs={field.description ? 12 : 6}>
//                             <Typography
//                               variant="body1"
//                               sx={{
//                                 padding: '7px 80px',
//                                 fontWeight: 600,
//                                 fontSize: '15px',
//                                 text: 'capitalize'
//                               }}
//                             >
//                               {stripHtmlTags(field.label)}
//                             </Typography>
//                             {field.description && (
//                               <Typography
//                                 variant="body2"
//                                 color="textSecondary"
//                                 sx={{
//                                   padding: '10px 40px',
//                                   text: 'capitalize'
//                                 }}
//                               >
//                                 {stripHtmlTags(field.description)}
//                               </Typography>
//                             )}
//                           </Grid>

//                           <Grid item xs={field.description ? 12 : 6}>
//                             {(() => {
//                               let foundData = false;
//                               let displayData = null;

//                               Object.keys(client.values)?.forEach((section) => {
//                                 if (group === section) {
//                                   Object.keys(client.values[section])?.forEach(
//                                     (fieldName: string) => {
//                                       if (fieldName === field.name && id) {
//                                         foundData = true;
//                                         switch (field.type?.toLowerCase()) {
//                                           case 'signature':
//                                             displayData = (
//                                               <Link
//                                                 href={
//                                                   client.values[section][id]
//                                                 }
//                                                 target="_blank"
//                                               >
//                                                 Signature
//                                               </Link>
//                                             );
//                                             break;
//                                           case 'file':
//                                             <Link
//                                               href={client.values[section][id]}
//                                               target="_blank"
//                                             >
//                                               File
//                                             </Link>;
//                                             break;
//                                           case 'image':
//                                             <Link
//                                               href={client.values[section][id]}
//                                               target="_blank"
//                                             >
//                                               Image
//                                             </Link>;
//                                             break;
//                                           default:
//                                             <Typography
//                                               sx={{
//                                                 width: '500px',
//                                                 whiteSpace: 'nowrap',
//                                                 textOverflow: 'ellipsis',
//                                                 overflow: 'hidden',
//                                                 text: 'capitalize'
//                                               }}
//                                             >
//                                               {client.values[section][id] ||
//                                                 'N/A'}
//                                             </Typography>;
//                                         }
//                                       }
//                                     }
//                                   );
//                                 }
//                               });

//                               // Return the found data or "N/A" if no data was found
//                               return foundData ? displayData : <Box>N/A</Box>;
//                             })()}
//                           </Grid>
//                         </React.Fragment>
//                       )
//                     )}
//                   </Grid>
//                 </Box>
//               ))}
//             </Box>
//           ))}
//         </Box>
//       </Box>
//     </div>
//   );
// };

// export default FormResponses;
