// Global imports
import {
  Box,
  IconButton,
  Ty<PERSON><PERSON>,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Checkbox,
  Button
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
// import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import UploadIcon from '@mui/icons-material/Upload';
import ChevronRightOutlinedIcon from '@mui/icons-material/ChevronRightOutlined';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import TagsInput from 'react-tagsinput';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  checkfolder,
  getassessmentclients,
  sendpdf
} from '../../../redux/reducers/clients.reducer';
import { getapps } from '../../../redux/reducers/apps.reducer';
import 'react-tagsinput/react-tagsinput.css';
import { Icon, SubMenu } from '../../../components/form.elements';
import Sidebar from '../../../components/reusable/Sidebar';
import Shell from '../../../components/layout/Shell';
import { UserNavBarModules } from '../../../components/users/UsersList';
import LoaderUI from '../../../components/reusable/loaderUI';
import '../../../css/client-assessment-styles.scss';

const ClientAssessmentListPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { assessmentClients, isLoading }: any = useSelector(
    (state: AppState) => state.clients
  );
  const navigate = useNavigate();
  const [appId, setAppId] = useState();
  const [path, setPath]: any = useState();
  const [clientId, setClientId] = useState();
  const [type, setType]: any = useState();
  const [tags, setTags] = useState<any>([]);
  const [open, setOpen] = useState(false);
  const [checked, setChecked] = useState(false);
  const [input, setInput]: any = useState();

  useEffect(() => {
    const getClientsList = async () => {
      const response = await dispatch(getapps(null));
      if (response.payload.status) {
        response.payload.data.forEach(async (app: any) => {
          if (app?.industry_app_process?.process_code === 'HC_CLIASS') {
            setAppId(app?.app_id);
            await dispatch(getassessmentclients(app?.app_id));
          }
        });
      }
    };
    getClientsList();
  }, [dispatch]);

  const handleClickOpen = async (param: any, type2: string) => {
    setClientId(param);
    setType(type2);
    if (type2 === 'share') {
      setOpen(true);
    } else {
      const response = await dispatch(checkfolder({ clientId: param }));
      if (response.payload.status) {
        if (response.payload.isExisted) {
          setOpen(true);
        } else {
          await dispatch(
            sendpdf({
              clientId: param,
              data: { request_type: 'upload', app_id: appId }
            })
          );
        }
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (event: any) => {
    setChecked(event.target.checked);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInput(event.target.value);
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const handleTagsChange = (tagss: any) => {
    if (isValidEmail(tagss[tagss.length - 1])) {
      setTags(tagss);
    }
  };

  const columns = [
    { field: 'name', headerName: 'Name', flex: 1 },
    { field: 'mobile_number', headerName: 'Phone number', flex: 1 },
    {
      field: 'action',
      headerName: 'Action',
      flex: 1,
      renderCell: (params: any) => (
        <Box>
          <IconButton onClick={() => handleClickOpen(params.id, 'upload')}>
            <UploadIcon color="primary" />
          </IconButton>
          <IconButton onClick={() => handleClickOpen(params.id, 'share')}>
            <Icon name="Mail" fontSize="small" color="primary" />
          </IconButton>
          <IconButton
            onClick={() =>
              navigate(
                `/users/client-assessment/form-values/${params.id}/${appId}`
              )
            }
          >
            <ChevronRightOutlinedIcon color="primary" />
          </IconButton>
        </Box>
      )
    }
  ];

  const menuItems = UserNavBarModules();

  const getSubMenu = () => <SubMenu backNavigation />;
  const getDrawer = () => (
    <Sidebar menuItems={menuItems} userType={undefined} />
  );

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const formJson = Object.fromEntries((formData as any).entries());
    if (type === 'share') {
      const email = formJson?.email;
      if (email) {
        const response = await dispatch(
          sendpdf({
            clientId,
            data: {
              request_type: 'share',
              app_id: appId,
              email,
              cc_emails: tags
            }
          })
        );
        if (response.payload.status) {
          toast.success(response.payload?.message);
          handleClose();
        }
      }
    } else {
      const folderName = formJson?.folderName;
      const checkbox = formJson?.checkbox;
      if (folderName && !checkbox) {
        const response = await dispatch(checkfolder({ clientId, folderName }));
        if (response.payload.status) {
          if (response.payload.isExisted) {
            setPath(folderName);
            setInput('');
          } else {
            const resp = await dispatch(
              sendpdf({
                clientId,
                data: {
                  request_type: 'upload',
                  app_id: appId,
                  path: folderName
                }
              })
            );
            if (resp.payload.status) {
              setChecked(false);
              setPath('');
              setInput('');
              handleClose();
            }
          }
        }
      } else if (checkbox) {
        const response = await dispatch(
          sendpdf({ clientId, data: { request_type: 'upload', app_id: appId } })
        );
        if (response.payload.status) {
          handleClose();
          setChecked(false);
          setPath('');
          setInput('');
        }
      }
    }
  }

  return (
    <Shell showDrawer drawerData={getDrawer()} subMenu={getSubMenu()}>
      <Box
        sx={{ display: 'flex', overflow: 'hidden', flexDirection: 'column' }}
      >
        {isLoading ? (
          <LoaderUI />
        ) : (
          <Box
            sx={{
              backgroundColor: '#FFFFFF',
              padding: '10px',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              flexGrow: 1,
              minHeight: 'calc(100vh - 100px)'
            }}
            className="main-container"
          >
            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Box sx={{ backgroundColor: '#FAF9F8' }}>
                <div>
                  <Typography
                    sx={{
                      fontSize: '22px',
                      fontWeight: '600',
                      // marginTop: '50px',
                      marginBottom: '10px',
                      padding: '2px',
                      marginLeft: '20px'
                    }}
                  >
                    Clients
                  </Typography>
                </div>
              </Box>

              <Box className="content">
                <Box className="div-styles">
                  <Box sx={{ width: '100%', flexGrow: 1, minHeight: '300px' }}>
                    {assessmentClients?.length > 0 ? (
                      <DataGrid
                        rows={assessmentClients}
                        getRowId={(row) => row?.client_id}
                        columns={columns}
                        initialState={{
                          pagination: {
                            paginationModel: { page: 0, pageSize: 5 }
                          }
                        }}
                        pageSizeOptions={[5, 10]}
                        // checkboxSelection
                        getRowClassName={() => `striped-row`}
                        sx={{
                          '& .MuiDataGrid-columnHeader': {
                            '& .MuiDataGrid-menuIcon': {
                              visibility: 'visible',
                              width: 'auto'
                            }
                          }
                        }}
                      />
                    ) : (
                      <Box className="no-applicants">
                        <Typography
                          sx={{ fontSize: '17px', fontWeight: '500' }}
                        >
                          No records found.
                        </Typography>
                      </Box>
                    )}
                  </Box>
                  <Box sx={{ padding: '20px' }}>
                    <Dialog
                      open={open}
                      onClose={handleClose}
                      maxWidth="sm"
                      fullWidth
                      PaperProps={{ component: 'form', onSubmit: handleSubmit }}
                    >
                      <DialogContent sx={{ paddingLeft: 7, paddingRight: 7 }}>
                        {type === 'share' ? (
                          <>
                            <TextField
                              autoFocus
                              required
                              margin="dense"
                              id="email"
                              name="email"
                              label="Please enter email"
                              placeholder="Enter Email"
                              type="email"
                              fullWidth
                              variant="standard"
                              InputLabelProps={{
                                shrink: true,
                                style: { color: 'black', fontSize: '25px' }
                              }}
                              InputProps={{ style: { marginTop: '40px' } }}
                            />
                            <TagsInput
                              value={tags}
                              onChange={handleTagsChange}
                            />
                          </>
                        ) : (
                          <>
                            {path && (
                              <DialogContentText
                                sx={{
                                  fontSize: '20px',
                                  fontWeight: '500',
                                  color: 'black'
                                }}
                              >
                                <Typography
                                  sx={{
                                    fontSize: '24px',
                                    fontWeight: '600',
                                    paddingTop: 4
                                  }}
                                >
                                  Folder name already exists !
                                </Typography>
                                <Checkbox
                                  disabled={input !== ''}
                                  checked={checked}
                                  onChange={handleChange}
                                  inputProps={{ 'aria-label': 'controlled' }}
                                  sx={{ paddingLeft: 0 }}
                                  id="checkbox"
                                  name="checkbox"
                                />
                                Use same folder
                              </DialogContentText>
                            )}
                            {path && (
                              <Typography
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                (or)
                              </Typography>
                            )}
                            <TextField
                              autoFocus
                              disabled={!!checked}
                              onChange={handleInputChange}
                              margin="dense"
                              id="name"
                              name="folderName"
                              label="Please enter new folder"
                              placeholder="Enter Folder Name"
                              type="text"
                              fullWidth
                              variant="standard"
                              InputLabelProps={{
                                shrink: true,
                                style: { color: 'black', fontSize: '25px' }
                              }}
                              InputProps={{ style: { marginTop: '40px' } }}
                            />
                          </>
                        )}
                      </DialogContent>
                      <DialogActions
                        sx={{
                          paddingBottom: 5,
                          paddingLeft: 6,
                          paddingRight: 5
                        }}
                      >
                        <Button onClick={handleClose}>Cancel</Button>
                        <Button type="submit">Ok</Button>
                      </DialogActions>
                    </Dialog>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
        )}
      </Box>
    </Shell>
  );
};

export default ClientAssessmentListPage;
