/* eslint-disable react-hooks/exhaustive-deps */
import {
  useEffect
  //  useState
} from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import UserChecklist from '../../../components/users/employee-onboarding/UserChecklist';
import CreateChecklist from '../../../components/users/employee-onboarding/CreateChecklist';
import { AppDispatch } from '../../../redux/app.store';
import {
  getchecklistdata,
  // getempchecklist,
  getemployee
  // getformvalues
} from '../../../redux/reducers/user.reducer';
// import { SnackbarElement } from '../../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../../types';

const ChecklistPage: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const getEmployeeDetails = async () => {
    try {
      const response = await dispatch(getemployee(id));
      if (response?.payload?.statusCode) {
        toast.error(
          response?.payload?.message ||
            'Something went wrong, please try again later'
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something went wrong, please try again later'
      );
    }
  };

  // const getEmpChecklist = async () => {
  //   try {
  //     const response = await dispatch(getempchecklist(id));
  //     if (response?.payload?.statusCode) {
  //       setSnackbarOpen({
  //         status: true,
  //         message: 'Something Went Wrong, Please Try Again Later.'
  //       });
  //     }
  //   } catch (error) {
  //     setSnackbarOpen({
  //       status: true,
  //       message: 'Something Went Wrong, Please Try Again Later.'
  //     });
  //   }
  // };

  // const getFormValues = async () => {
  //   try {
  //     const response = await dispatch(getformvalues(id));
  //     if (response?.payload?.statusCode) {
  //       setSnackbarOpen({
  //         status: true,
  //         message: 'Something Went Wrong, Please Try Again Later.'
  //       });
  //     }
  //   } catch (error) {
  //     setSnackbarOpen({
  //       status: true,
  //       message: 'Something Went Wrong, Please Try Again Later.'
  //     });
  //   }
  // };

  const getChecklistData = async () => {
    try {
      const response = await dispatch(getchecklistdata(null));
      if (response?.payload?.statusCode) {
        // setSnackbarOpen({
        //   status: true,
        //   message: 'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useEffect(() => {
    getChecklistData();

    if (id) {
      getEmployeeDetails();
      // getEmpChecklist();
      // getFormValues();
    }
  }, [id]);

  return id ? (
    <UserChecklist />
  ) : (
    <>
      <CreateChecklist />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default ChecklistPage;
