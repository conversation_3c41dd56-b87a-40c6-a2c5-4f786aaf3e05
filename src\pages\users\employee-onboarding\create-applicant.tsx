import * as Yup from 'yup';
import { Box, Button, CircularProgress, Grid, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import '../../../css/create-applicant-styles.scss';
import { FormikValues } from 'formik';
import {
  AppForm,
  FormInput,
  FormPhoneInput,
  SubMenu
} from '../../../components/form.elements';
import {
  createapplicant,
  getapplicant,
  updateapplicant
} from '../../../redux/reducers/applicant.reducer';
import { AppDispatch } from '../../../redux/app.store';
import { RootState } from '../../../redux/reducers';
import { PasswordInput } from '../../../components/reusable/PasswordInput';
// import { SnackbarElement } from '../../../components/reusable/SnackbarElement';
import Shell from '../../../components/layout/Shell';

const CreateApplicant: React.FC = () => {
  const { id } = useParams();
  const { loadingSpinner } = useSelector((state: RootState) => state.applicant);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  // const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  // const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  // const [snackbarSeverity, setSnackbarSeverity] = useState<
  //   'success' | 'error' | 'warning' | 'info'
  // >('success');

  const validatePassword = () => {
    if (!id) {
      return Yup.string().required('Password is required');
    }
    return Yup.string().optional();
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Name is required'),
    email: Yup.string()
      .email('Invalid email format')
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        'Please enter a valid email address'
      )
      .required('Email is required'),
    password: validatePassword(),
    // password: Yup.string().required("Password is required"),
    mobile_number: Yup.string()
      .min(10, 'Mobile number should be minimum 10 digits')
      .required('Mobile number is required')
  });

  const handelSubmit = async (values: FormikValues) => {
    const formData = {
      name: values.name,
      mobile_number: values.mobile_number,
      email: values.email,
      password: values.password
    };

    if (id) {
      try {
        const res = await dispatch(updateapplicant({ id, data: formData }));

        if (res.payload.status) {
          // setSnackbarSeverity('success');
          // setSnackbarMessage('Applicant Updated Successfully!');
          // setSnackbarOpen(true);
          // toast.success('Applicant Updated Successfully!');

          // Navigate after a short delay
          setTimeout(() => {
            navigate('/users/employee-onboarding');
          }, 2600);
        } else if (res.payload.error) {
          // setSnackbarSeverity('error');
          // setSnackbarMessage(res.payload?.error?.message);
          // setSnackbarOpen(true);
          toast.error(res.payload?.error?.message);
        } else if (res.payload.statusCode === 401) {
          // setSnackbarSeverity('error');
          // setSnackbarMessage(res.payload?.message);
          // setSnackbarOpen(true);
          toast.error(res.payload?.message);
        }
      } catch (error) {
        // Handle unexpected errors
        // setSnackbarSeverity('error');
        // setSnackbarMessage('An unexpected error occurred.');
        // setSnackbarOpen(true);
        toast.error('An unexpected error occurred.');
      }
    } else if (!id) {
      try {
        const res = await dispatch(createapplicant(formData));

        if (res.payload?.status) {
          // setSnackbarSeverity('success');
          // setSnackbarMessage('Applicant Created Successfully!');
          // setSnackbarOpen(true);
          // toast.success('Applicant Created Successfully!');

          // Navigate after a short delay
          setTimeout(() => {
            navigate('/users/employee-onboarding');
          }, 2600);
        } else if (res.payload?.error) {
          // setSnackbarSeverity('error');
          // setSnackbarMessage(res.payload.error.message);
          // setSnackbarOpen(true);
          toast.error(res.payload.error.message);
        } else if (res.payload?.statusCode === 401) {
          // setSnackbarSeverity('error');
          // setSnackbarMessage(res.payload.message);
          // setSnackbarOpen(true);
          toast.error(res.payload.message);
        } else if (res.payload?.statusCode === 400) {
          // setSnackbarSeverity('error');
          // setSnackbarMessage(res.payload.message);
          // setSnackbarOpen(true);
          toast.error(res.payload.message);
        }
      } catch (error) {
        // Handle unexpected errors gracefully
        // setSnackbarSeverity('error');
        // setSnackbarMessage('An unexpected error occurred.');
        // setSnackbarOpen(true);
        toast.error('An unexpected error occurred.');
      }
    }
  };

  const [applicant, setApplicant] = useState({
    name: '',
    mobile_number: '',
    email: '',
    password: ''
  });

  useEffect(() => {
    if (id) {
      dispatch(getapplicant(id)).then((response) => {
        const data = response?.payload?.data;
        setApplicant({
          name: data.name || '',
          mobile_number: data.mobile_number || '',
          email: data.email || '',
          password: ''
        });
      });
    }
  }, [dispatch, id]);

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  return (
    <Shell subMenu={getSubMenu()}>
      <Box
        sx={{
          padding: '0px 100px'
        }}
      >
        <Box
          sx={{
            backgroundColor: '#fff',
            padding: '60px 80px'
          }}
        >
          <Typography
            variant="h4"
            component="div"
            sx={{
              fontSize: '28px',
              fontWeight: 400,
              marginBottom: '40px',
              color: '#3B5864'
            }}
          >
            Applicant Registration Form
          </Typography>
          <AppForm
            initialValues={applicant}
            validationSchema={validationSchema}
            onSubmit={handelSubmit}
          >
            <Box sx={{ width: '100%' }}>
              <Grid
                container
                columnSpacing={2}
                sx={{
                  backgroundColor: '#FAF9F8',
                  borderRadius: '10px',
                  padding: ' 40px 50px 40px 35px',
                  border: '1px solid #EBEBEB',
                  ml: '2px'
                }}
              >
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormInput
                      name="name"
                      label="Name"
                      required
                      placeholder="Enter User Name"
                      containerStyles={{
                        width: {
                          xs: '100%'
                        }
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormPhoneInput
                      name="mobile_number"
                      label="Mobile Number"
                      required
                      containerStyles={{
                        width: '100%'
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <FormInput
                      name="email"
                      label="Email"
                      required={!id}
                      type="email"
                      disabled={!!id}
                      placeholder="Enter user email"
                      containerStyles={{
                        width: '100%',
                        marginTop: '4px'
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box className="field-input">
                    <PasswordInput
                      name="password"
                      label="Password"
                      required={!id}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} />
              </Grid>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'end',
                  width: '100%',
                  marginTop: '10px'
                }}
              >
                <Button
                  onClick={() => navigate('/users/employee-onboarding')}
                  sx={{
                    backgroundColor: 'white2.main',
                    color: 'primaryBlue.main',
                    padding: '10px 30px',
                    boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                    textTransform: 'capitalize',
                    marginRight: '10px',
                    '&:hover': {
                      color: 'primaryBlue.main',
                      boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)',
                      backgroundColor: 'rgba(4, 131, 186, 0.04)'
                    }
                  }}
                >
                  CANCEL
                </Button>
                <Button
                  type="submit"
                  sx={{
                    backgroundColor: 'primaryBlue.main',
                    color: 'white2.main',
                    padding: '10px 35px',
                    boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                    '&:hover': {
                      color: 'white2.main',
                      backgroundColor: '#08366b',
                      boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                    }
                  }}
                >
                  {loadingSpinner ? (
                    <CircularProgress
                      size={24}
                      sx={{ color: 'white', marginLeft: 1 }}
                    />
                  ) : (
                    // eslint-disable-next-line react/jsx-no-useless-fragment
                    <>{id ? 'Update' : 'Save'}</>
                  )}
                </Button>
              </Box>
            </Box>
          </AppForm>
        </Box>
      </Box>
      {/* <SnackbarElement
        message={snackbarMessage}
        statusType={snackbarSeverity}
        snackbarOpen={snackbarOpen}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Shell>
  );
};

export default CreateApplicant;
