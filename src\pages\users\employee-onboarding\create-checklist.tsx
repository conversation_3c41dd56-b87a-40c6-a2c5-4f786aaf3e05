import {
  useEffect
  //  useState
} from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import CreateChecklist from '../../../components/users/employee-onboarding/CreateChecklist';
import { AppDispatch } from '../../../redux/app.store';
import {
  getchecklistdata,
  getempchecklist,
  getformvalues
} from '../../../redux/reducers/user.reducer';
// import { SnackbarElement } from '../../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../../types';

const CreateChecklistPage: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  const getEmpChecklist = async () => {
    try {
      const res = await dispatch(getempchecklist(id));
      if (res?.payload?.statusCode) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     res?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          res?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getFormValues = async () => {
    try {
      const res = await dispatch(getformvalues(id));
      if (res?.payload?.statusCode) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     res?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          res?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getChecklistData = async () => {
    try {
      const res = await dispatch(getchecklistdata(null));
      if (res?.payload?.statusCode) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     res?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          res?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };
  useEffect(() => {
    if (id) {
      getEmpChecklist();
      getFormValues();
    } else {
      getChecklistData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <CreateChecklist />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default CreateChecklistPage;
