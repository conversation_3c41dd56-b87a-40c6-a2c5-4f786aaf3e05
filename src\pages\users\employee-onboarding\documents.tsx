import {
  useEffect
  //  useState
} from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import Documents from '../../../components/users/employee-onboarding/Documents';
import { AppDispatch } from '../../../redux/app.store';
import { getformvalues } from '../../../redux/reducers/user.reducer';
// import { SnackbarElement } from '../../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../../types';

const DocumentsPage: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  const getFormValues = async () => {
    try {
      const res = await dispatch(getformvalues(id));
      if (res?.payload?.statusCode) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     res?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          res?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useEffect(() => {
    if (id) {
      getFormValues();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <Documents />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default DocumentsPage;
