import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import FormDisplay from '../../../components/users/employee-onboarding/FormDisplay';

const FormDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const isProduction = import.meta.env.VITE_APP_MODE === 'production';

  useEffect(() => {
    if (isProduction) {
      // Disable Right Click
      const disableRightClick = (event: MouseEvent) => event.preventDefault();
      document.addEventListener('contextmenu', disableRightClick);

      // Disable Developer Shortcuts
      const disableDevTools = (event: KeyboardEvent) => {
        if (
          event.key === 'F12' ||
          (event.ctrlKey &&
            event.shiftKey &&
            (event.key === 'I' || event.key === 'J')) ||
          (event.ctrlKey && event.key === 'U')
        ) {
          event.preventDefault();
        }
      };
      document.addEventListener('keydown', disableDevTools);

      // Detect DevTools Open and Redirect
      const checkDevTools = () => {
        if (
          window.outerWidth - window.innerWidth > 160 ||
          window.outerHeight - window.innerHeight > 160
        ) {
          alert('Developer tools detected! Redirecting...');
          navigate('/'); // Redirect to another page
        }
      };
      const interval = setInterval(checkDevTools, 2000);

      // Cleanup event listeners
      return () => {
        document.removeEventListener('contextmenu', disableRightClick);
        document.removeEventListener('keydown', disableDevTools);
        clearInterval(interval);
      };
    }
  }, [isProduction, navigate]);

  return <FormDisplay module="onboarding-employee" />;
};

export default FormDisplayPage;
