// Global imports
import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local imports
import UsersList from '../../components/users/UsersList';
import { AppDispatch } from '../../redux/app.store';
import { getusersdata, getorgdata } from '../../redux/reducers/user.reducer';

const Users: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const userType = localStorage.getItem('user_type');
  const [isLoading, setIsLoading] = useState(false);
  const [organizationsList, setOrganizationsList] = useState([]);
  const [usersList, setUsersList] = useState([]);

  const getUsersData = async () => {
    try {
      setIsLoading(true);
      const userResponse = await dispatch(getusersdata(null));
      if (userResponse.payload.status) {
        setUsersList(userResponse.payload.data);
        setIsLoading(false);
      } else {
        setIsLoading(false);

        toast.error(
          userResponse?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const fetchOrganizationsList = async () => {
    try {
      setIsLoading(true);
      const orgResponse = await dispatch(getorgdata(null));
      if (orgResponse.payload.status) {
        setOrganizationsList(orgResponse.payload.data);
      } else {
        toast.error(
          orgResponse?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useMemo(() => {
    if (userType === 'super_admin') {
      fetchOrganizationsList();
    } else if (userType === 'organization') {
      getUsersData();
    }
  }, [userType]);

  return (
    <UsersList
      organizationsList={organizationsList}
      usersList={usersList}
      isLoading={isLoading}
      getUsersData={getUsersData}
      fetchOrganizationsList={fetchOrganizationsList}
    />
  );
};
export default Users;
