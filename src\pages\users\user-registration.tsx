import {
  useMemo
  //  useState
} from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import UserRegistration from '../../components/users/UserRegistration';
import { AppDispatch } from '../../redux/app.store';
import {
  getorgapps,
  getorgdata,
  getuser
} from '../../redux/reducers/user.reducer';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';

const UserRegistrationPage: React.FC = () => {
  const userType = localStorage.getItem('user_type');
  const orgId = localStorage.getItem('org_id');

  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });

  const getOrgApps = async (orgAppsId: string) => {
    try {
      await dispatch(getorgapps(orgAppsId));
    } catch (error) {
      /* empty */
    }
  };

  const getData = async () => {
    try {
      const user = await dispatch(getuser(id));
      if (user?.payload?.id) {
        await getOrgApps(user?.payload?.organization);
      } else {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     user?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        toast.error(
          user?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      // setSnackbarOpen({
      //   status: true,
      //   message: 'Something Went Wrong, Please Try Again Later.'
      // });
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  const getOrgData = async () => {
    try {
      await dispatch(getorgdata(null));
    } catch (error) {
      /* empty */
    }
  };
  useMemo(() => {
    if (id) {
      getData();
    }
    if (userType === 'super_admin') {
      getOrgData();
    }
    if (!id && orgId) {
      getOrgApps(orgId);
    }
  }, []);
  return (
    <>
      <UserRegistration />
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};

export default UserRegistrationPage;
