import { Action, ThunkAction, configureStore } from '@reduxjs/toolkit';

// eslint-disable-next-line import/no-cycle
import rootReducer, { RootState } from './reducers';
import authMiddleware from './auth.middleware';

const appStore = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(authMiddleware)
});

export type AppDispatch = typeof appStore.dispatch;
export type AppState = ReturnType<typeof appStore.getState>;
export type AppThunk = ThunkAction<void, RootState, unknown, Action<string>>;
export default appStore;
