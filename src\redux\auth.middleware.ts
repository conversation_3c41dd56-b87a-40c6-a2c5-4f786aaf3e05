import { Middleware } from '@reduxjs/toolkit';
import { logout } from './reducers/auth.reducer';

const authMiddleware: Middleware =
  ({ dispatch }) =>
  (next) =>
  (action: any) => {
    if (
      action.type.endsWith('/rejected') &&
      action.payload?.statusCode === 401
    ) {
      dispatch(logout());
      const loginRoute = localStorage.getItem('login_route');
      if (window.location.pathname !== loginRoute) {
        window.location.href = loginRoute || '/login';
      }
    }

    return next(action);
  };

export default authMiddleware;
