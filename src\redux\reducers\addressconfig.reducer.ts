import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  createStorageConfig,
  getAddressConfig,
  getEmailConfig,
  getLinkData,
  getLinkRedirectData,
  getOneDriveFolder,
  getStorageConfig,
  selectOneDriveFolder,
  updateStorageConfig,
  postPdfConfig,
  getPdfConfig,
  updatePdfConfig
} from '../../apis/addressconfig';

export const getaddressconfig = createAsyncThunk(
  'getaddressconfig',
  getAddressConfig
);
export const getstorageconfig = createAsyncThunk(
  'getstorageconfig',
  getStorageConfig
);

export const updatestorageconfig = createAsyncThunk(
  'updatestorageconfig',
  updateStorageConfig
);
export const createstorageconfig = createAsyncThunk(
  'createstorageconfig',
  createStorageConfig
);
export const postpdfconfig = createAsyncThunk('postpdfconfig', postPdfConfig);
export const getpdfconfig = createAsyncThunk('getpdfconfig', getPdfConfig);
export const updatepdfconfig = createAsyncThunk(
  'updatepdfconfig',
  updatePdfConfig
);

export const getlinkdata = createAsyncThunk('getlinkdata', getLinkData);

export const getemailconfig = createAsyncThunk(
  'getemailconfig',
  getEmailConfig
);

export const getonedrivefolder = createAsyncThunk(
  'getonedrivefolder',
  getOneDriveFolder
);

export const getlinkredirectdata = createAsyncThunk(
  'getlinkredirectdata',
  getLinkRedirectData
);

export const selectonedrivefolder = createAsyncThunk(
  'selectonedrivefolder',
  selectOneDriveFolder
);

const initialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  addressConfig: {},
  addressConfigValues: {
    apiKey: '',
    hostUrl: ''
  },
  storageConfig: {},
  googleConfigValues: {
    clientId: '',
    secretKey: '',
    backendRedirectUrl: ''
  },
  oneDriveConfigValues: {
    clientId: '',
    tenentId: '',
    secretValue: '',
    backendRedirectUrl: ''
  },
  link: '',
  emailConfig: {},
  emailConfigValues: {
    apiKey: '',
    fromEmail: ''
  },
  oneDriveFolders: [],
  pdfConfig: {},
  pdfConfigValues: {
    orientation: '',
    margin: '',
    sizeValue: ''
  }
};

const addressConfigSlice = createSlice({
  name: 'addressconfig',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getaddressconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getaddressconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.addressConfig = data;

        state.addressConfigValues.apiKey = data?.details.apiKey;
        state.addressConfigValues.hostUrl = data?.details.hostUrl;
      })
      .addCase(getaddressconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getstorageconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getstorageconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.storageConfig = data;
        if (data.name === 'google_drive') {
          state.googleConfigValues.clientId = data.details.clientId;
          state.googleConfigValues.secretKey = data.details.secretKey;
          state.googleConfigValues.backendRedirectUrl =
            data.details.backendRedirectUrl;

          state.oneDriveConfigValues.tenentId = '';
          state.oneDriveConfigValues.clientId = '';
          state.oneDriveConfigValues.secretValue = '';
          state.oneDriveConfigValues.backendRedirectUrl = '';
        } else if (data.name === 'one_drive') {
          state.oneDriveConfigValues.tenentId = data.details.tenentId;
          state.oneDriveConfigValues.clientId = data.details.clientId;
          state.oneDriveConfigValues.secretValue = data.details.secretValue;
          state.oneDriveConfigValues.backendRedirectUrl =
            data.details.backendRedirectUrl;

          state.googleConfigValues.clientId = '';
          state.googleConfigValues.secretKey = '';
          state.googleConfigValues.backendRedirectUrl = '';
        }
      })
      .addCase(getstorageconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(updatestorageconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatestorageconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.addressConfig = data;
      })
      .addCase(updatestorageconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(createstorageconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createstorageconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.addressConfig = data;
      })
      .addCase(createstorageconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(postpdfconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(postpdfconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.addressConfig = data;
      })
      .addCase(postpdfconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getpdfconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getpdfconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.pdfConfig = data;
        if (data?.name === 'pdf_config') {
          state.pdfConfigValues.orientation = data.details.orientation;
          state.pdfConfigValues.margin = data.details.margin;
        }
      })
      .addCase(getpdfconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(updatepdfconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatepdfconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.pdfConfig = data;
      })
      .addCase(updatepdfconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getlinkdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getlinkdata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.link = data;
        window.open(data, '_blank');
      })
      .addCase(getlinkdata.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getemailconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getemailconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.emailConfig = data;
        if (data?.name === 'send-grid') {
          state.emailConfigValues.apiKey = data.details.apiKey;
          state.emailConfigValues.fromEmail = data.details.fromEmail;
        }
      })
      .addCase(getemailconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getonedrivefolder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getonedrivefolder.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload.foldersList;
        if (data.type === 'google_drive') {
          data.map((folder: any) => {
            const newFolder = {
              ...folder,
              isSelected: false,
              folder_name: folder.name
            };
            return newFolder;
          });
        }
        state.oneDriveFolders = data;
      })
      .addCase(getonedrivefolder.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getlinkredirectdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getlinkredirectdata.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getlinkredirectdata.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(selectonedrivefolder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(selectonedrivefolder.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(selectonedrivefolder.rejected, (state) => {
        state.isLoading = false;
      });
  }
});

export default addressConfigSlice.reducer;
