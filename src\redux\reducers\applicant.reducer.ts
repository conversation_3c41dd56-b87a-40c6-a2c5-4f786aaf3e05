import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  createApplicant,
  deleteApplicant,
  getApplicant,
  updateApplicant
} from '../../apis/applicant';

const initialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  loadingSpinner: false,

  user: {
    email: '',
    password: '',
    name: '',
    mobile_number: ''
  }
};

export const createapplicant = createAsyncThunk(
  'createapplicant',
  createApplicant
);
export const updateapplicant = createAsyncThunk(
  'updateapplicant',
  updateApplicant
);
export const getapplicant = createAsyncThunk('getapplicant', getApplicant);
export const deleteapplicant = createAsyncThunk(
  'deleteapplicant',
  deleteApplicant
);

const userSlice = createSlice({
  name: 'applicant',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createapplicant.pending, (state) => {
        state.isLoading = true;
        state.loadingSpinner = true;
      })
      .addCase(createapplicant.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.user = data;
        state.loadingSpinner = false;
      })
      .addCase(createapplicant.rejected, (state) => {
        state.isLoading = false;
        state.loadingSpinner = false;

        // state.loadingError = action.payload;
      });

    builder
      .addCase(updateapplicant.pending, (state) => {
        state.isLoading = true;
        state.loadingSpinner = true;
      })
      .addCase(updateapplicant.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.user = data;
        state.loadingSpinner = false;
      })
      .addCase(updateapplicant.rejected, (state) => {
        state.isLoading = false;
        state.loadingSpinner = false;

        // state.loadingError = action.payload;
      });

    builder
      .addCase(getapplicant.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getapplicant.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload;
        // data.organization = data.organization.organization_id;
        // data.apps = data.apps.map((app: any) => app.app_id);
        state.user = data;
      })
      .addCase(getapplicant.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(deleteapplicant.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteapplicant.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.user = data;
      })
      .addCase(deleteapplicant.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
  }
});

export default userSlice.reducer;
