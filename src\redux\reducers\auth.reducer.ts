import { PayloadAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  fetchOrganizationAdmin,
  fetchSuperAdmin,
  submitLoginForm,
  userValidation
} from '../../apis/login-config';
import { LoginAction, USERTYPE } from '../../types';

interface USER {
  admin_id: string | null;
  id: number;
  name: string | null;
  password: string;
  username: string | null;
  mobile_number: number | null;
  email: string | null;
  logo?: string | undefined;
  apps?: [] | null; // This is for organization user
  avatar?: string;
}

interface DEFAULTDICTTYPES {
  isAuthenticated: boolean;
  accessToken: string | null;
  tokenType: string | null;
  user: USER | null;
  orgData: object | null;
  orgId: string | null;
  userType: USERTYPE['userType'];
  isLoading: boolean;
  errors: object | null;
}

const defaultDict: DEFAULTDICTTYPES = {
  isAuthenticated: false,
  accessToken: null,
  tokenType: null,
  user: null,
  orgData: null,
  orgId: null,
  userType: null,
  isLoading: false,
  errors: {}
};

const initialState = localStorage.getItem('access_token')
  ? {
      ...defaultDict,
      isAuthenticated: true,
      userType: localStorage.getItem('user_type')
    }
  : defaultDict;

export const postLoginForm = createAsyncThunk('postLoginForm', submitLoginForm);
export const getSuperAdminDetails = createAsyncThunk(
  'getSuperAdminDetails',
  fetchSuperAdmin
);
export const getOrganizationAdmin = createAsyncThunk(
  'getOrganizationAdmin',
  fetchOrganizationAdmin
);
export const userValidate = createAsyncThunk('userValidate', userValidation);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    login: (state, action: PayloadAction<LoginAction>) => {
      state.isAuthenticated = action.payload.status;
      state.userType = action.payload.userType;
      state.isLoading = action.payload.loading;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      localStorage.removeItem('access_token');
      localStorage.removeItem('org_id');
      localStorage.removeItem('user_type');
    },
    loading: (state, action) => {
      state.isLoading = action.payload;
    },
    firstTimeUserFetching: (state, action: PayloadAction<{ user: any }>) => {
      state.user = action.payload.user;
      state.isLoading = false;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(postLoginForm.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(postLoginForm.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = !!action.payload.access_token;
        state.userType =
          localStorage.getItem('login_route') === '/login'
            ? 'super_admin'
            : 'organization';
        state.accessToken = action.payload.access_token;
        state.tokenType = action.payload.token_type;
      })
      .addCase(postLoginForm.rejected, (state: any, action) => {
        state.isLoading = false;
        state.errors = action.payload;
      });
    builder
      .addCase(getSuperAdminDetails.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSuperAdminDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.data;
      })
      .addCase(getSuperAdminDetails.rejected, (state: any, action) => {
        state.isLoading = false;
        state.errors = action.payload;
      });
    builder
      .addCase(getOrganizationAdmin.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrganizationAdmin.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(getOrganizationAdmin.rejected, (state: any, action) => {
        state.isLoading = false;
        state.errors = action.payload;
      });
    builder
      .addCase(userValidate.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(userValidate.fulfilled, (state, action) => {
        state.isLoading = false;

        state.orgId = action.payload;
      })
      .addCase(userValidate.rejected, (state: any, action) => {
        state.isLoading = false;
        state.errors = action.payload;
      });
  }
});

export default authSlice.reducer;
export const { login, logout, loading, firstTimeUserFetching } =
  authSlice.actions;
