import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  deleteCaregiverDetails,
  editCaregiver,
  fetchCaregiverDetails,
  fetchCaregivers,
  storeCaregiver
} from '../../apis/caregivers';

export const getCaregivers = createAsyncThunk('getCaregivers', fetchCaregivers);
export const createCaregiver = createAsyncThunk(
  'createCaregiver',
  storeCaregiver
);
export const updateCaregiver = createAsyncThunk(
  'updateCaregiver',
  editCaregiver
);

export const getCaregiverDetails = createAsyncThunk(
  'getCaregiverDetails',
  fetchCaregiverDetails
);

export const removeCaregiver = createAsyncThunk(
  'removeCaregiver',
  deleteCaregiverDetails
);

const initialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  caregiversList: [],
  caregiverDetails: {
    email: '',
    password: '',
    name: '',
    mobile_number: '',
    clients: []
  }
};

const caregiverSlice = createSlice({
  name: 'caregivers',
  initialState,
  reducers: {
    resetCaregiverDetails: (state) => {
      state.caregiverDetails = initialState.caregiverDetails;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCaregivers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCaregivers.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.caregiversList = data;
      })
      .addCase(getCaregivers.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(getCaregiverDetails.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCaregiverDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.caregiverDetails = data;
      })
      .addCase(getCaregiverDetails.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(createCaregiver.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createCaregiver.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createCaregiver.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(updateCaregiver.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateCaregiver.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateCaregiver.rejected, (state) => {
        state.isLoading = false;
      });
  }
});

export default caregiverSlice.reducer;

export const { resetCaregiverDetails } = caregiverSlice.actions;
