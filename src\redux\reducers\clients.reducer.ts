import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  checkFolder,
  getAssessmentClients,
  getClientforms,
  getClients,
  sendPdf,
  exportToCsv
} from '../../apis/clients';

export const getclients = createAsyncThunk('getclients', getClients);
export const getclientforms = createAsyncThunk(
  'getclientforms',
  getClientforms
);
export const getassessmentclients = createAsyncThunk(
  'getassessmentclients',
  getAssessmentClients
);
export const checkfolder = createAsyncThunk('checkfolder', checkFolder);

export const exportToCSV = createAsyncThunk('exportToCSV', exportToCsv);

export const sendpdf = createAsyncThunk('sendpdf', sendPdf);

const initialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  clientList: [],
  clientForms: [],
  assessmentClients: []
};

const clientSlice = createSlice({
  name: 'clients',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getclients.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getclients.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.clientList = data;
      })
      .addCase(getclients.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getclientforms.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getclientforms.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.clientForms = data;
      })
      .addCase(getclientforms.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getassessmentclients.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getassessmentclients.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.assessmentClients = data;
      })
      .addCase(getassessmentclients.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(checkfolder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkfolder.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(checkfolder.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(sendpdf.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(sendpdf.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(sendpdf.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(exportToCSV.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(exportToCSV.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(exportToCSV.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
  }
});

export default clientSlice.reducer;
