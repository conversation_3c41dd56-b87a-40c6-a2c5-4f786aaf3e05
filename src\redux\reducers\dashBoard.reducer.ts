import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { getIndustryAppProcess } from '../../apis/apps';
import dashBoardAppsList from '../../apis/dashBoardApps';

interface DashboardState {
  errors: Record<string, any>;
  isLoading: boolean;
  loadingError: Record<string, any>;
  apps: any;
}

const initialState: DashboardState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  apps: {}
};

export const dashboardapps = createAsyncThunk(
  'dashboardapps',
  dashBoardAppsList
);
export const getindustryappprocess = createAsyncThunk(
  'getindustryappprocess',
  getIndustryAppProcess
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(dashboardapps.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(dashboardapps.fulfilled, (state, action) => {
        state.isLoading = false;
        state.apps = action.payload.data;
      })
      .addCase(dashboardapps.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
  }
});

export default dashboardSlice.reducer;
