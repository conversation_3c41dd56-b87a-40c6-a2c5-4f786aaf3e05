import { combineReducers } from '@reduxjs/toolkit';

import authReducer from './auth.reducer';
import userReducer from './user.reducer';
import formReducer from './form.reducer';
import appsReducer from './apps.reducer';
import orgReducer from './org.reducer';
import addressconfigReducer from './addressconfig.reducer';
import dashBoardReducer from './dashBoard.reducer';
import clientsReducer from './clients.reducer';
import templateReducer from './template.reducer';
import previewReducer from './preview.reducer';
import applicantReducer from './applicant.reducer';
import caregiversReducer from './caregivers.reducer';

const rootReducer = combineReducers({
  auth: authReducer,
  user: userReducer,
  form: formReducer,
  app: appsReducer,
  org: orgReducer,
  applicant: applicantReducer,
  addressconfig: addressconfigReducer,
  dashBoard: dashBoardReducer,
  clients: clientsReducer,
  caregivers: caregiversReducer,
  template: templateReducer,
  preview: previewReducer
});

export type RootState = ReturnType<typeof rootReducer>;
export default rootReducer;
