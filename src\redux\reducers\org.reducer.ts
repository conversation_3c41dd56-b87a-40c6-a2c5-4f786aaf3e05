import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  AppFormConfiguration,
  createOrganization,
  getApps,
  getIndustryTypes,
  getOrganizationDetails,
  getOrganizations,
  updateOrganization,
  deleteOrganization
} from '../../apis/org';

const initialState = {
  errors: {},
  isLoading: false,
  loadingSpinner: false,
  loadingError: {},
  isFormSubmiting: false,
  organizationId: '',
  organizations: [],
  organization: {
    name: '',
    logo: '',
    custom_name: '',
    email: '',
    password: '',
    industry_type_id: '',
    mobile_number: '',
    apps: []
  },
  apps: [],
  industryTypes: [],
  appId: null,
  orgId: null
};

export const getorganizations = createAsyncThunk(
  'getorganizations',
  getOrganizations
);
export const getorganizationdetails = createAsyncThunk(
  'getorganizationdetails',
  getOrganizationDetails
);
export const getapps = createAsyncThunk('getapps', getApps);
export const getindustrytypes = createAsyncThunk(
  'getindustrytypes',
  getIndustryTypes
);
export const createorganization = createAsyncThunk(
  'createorganization',
  createOrganization
);

export const deleteorganization = createAsyncThunk(
  'deleteOrganization',
  deleteOrganization
);

export const updateorganization = createAsyncThunk(
  'updateorganization',
  updateOrganization
);
export const appformconfiguration = createAsyncThunk(
  'appformconfiguration',
  AppFormConfiguration
);

const orgSlice = createSlice({
  name: 'org',
  initialState,
  reducers: {
    updateOrgId: (state, action) => {
      state.orgId = action.payload;
    },
    updateAppId: (state, action) => {
      state.appId = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getorganizations.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getorganizations.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.organizations = data;
      })
      .addCase(getorganizations.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getorganizationdetails.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getorganizationdetails.fulfilled, (state, action) => {
        state.isLoading = false;
        action.payload.data.industry_type_id =
          action.payload.data.industry_type.industry_type_id;
        const { data } = action.payload;
        state.organization = data;
      })
      .addCase(getorganizationdetails.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    // builder
    //   .addCase(getapps.pending, (state) => {
    //     state.isLoading = true;
    //   })
    //   .addCase(getapps.fulfilled, (state, action) => {
    //     state.isLoading = false;
    //     const { data } = action.payload;
    //     state.apps = data;
    //   })
    //   .addCase(getapps.rejected, (state) => {
    //     state.isLoading = false;
    //     // state.loadingError = action.payload;
    //   });
    builder
      .addCase(getindustrytypes.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getindustrytypes.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.industryTypes = data;
      })
      .addCase(getindustrytypes.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(createorganization.pending, (state) => {
        state.loadingSpinner = true;
      })
      .addCase(createorganization.fulfilled, (state, action) => {
        state.loadingSpinner = false;
        const { data } = action.payload;
        state.organization = data;
      })
      .addCase(createorganization.rejected, (state) => {
        state.loadingSpinner = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(updateorganization.pending, (state) => {
        state.loadingSpinner = true;
      })
      .addCase(updateorganization.fulfilled, (state) => {
        state.loadingSpinner = false;
      })
      .addCase(updateorganization.rejected, (state) => {
        state.loadingSpinner = false;
        // state.loadingError = action.payload;
      });
  }
});

export default orgSlice.reducer;

export const { updateOrgId, updateAppId } = orgSlice.actions;
