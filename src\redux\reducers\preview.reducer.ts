import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  form: {},
  formValues: {}
};

const previewSlice = createSlice({
  name: 'preview',
  initialState,
  reducers: {
    updateFormValues: (state, action) => {
      state.formValues = action.payload;
    }
  }
});

export default previewSlice.reducer;

export const { updateFormValues } = previewSlice.actions;
