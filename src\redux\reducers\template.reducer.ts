import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { CreateTemplate, getTemplateForm } from '../../apis/template';
import { getCountriesList, getFormFields } from '../../apis/forms';

// Define TypeScript interfaces for the state
interface TemplateForm {
  section_id?: string;
  name?: string;
  description?: string;
  fields?: any[]; // Replace `any` with the correct type if possible
}

interface TemplateState {
  formFields: any[] | null; // Replace `any` with the correct type if possible
  countriesList: { isoCode: string }[] | null;
  isLoading: boolean;
  loadingError: Record<string, any>; // Replace `any` with the correct type if possible
  templateForm: TemplateForm | null;
  columnIndex: number;
}

// Initialize the state with the appropriate types
const initialState: TemplateState = {
  formFields: null,
  countriesList: null,
  isLoading: false,
  loadingError: {},
  templateForm: null,
  columnIndex: 0
};

// Async actions with proper typing
export const gettemplateform = createAsyncThunk(
  'template/gettemplateform',
  getTemplateForm
);
export const createtemplate = createAsyncThunk(
  'template/createtemplate',
  CreateTemplate
);
export const gettemplateformFields = createAsyncThunk(
  'template/gettemplateformFields',
  getFormFields
);
export const gettemplatecountrieslist = createAsyncThunk(
  'template/gettemplatecountrieslist',
  getCountriesList
);

// Slice definition with proper typing
const templateSlice = createSlice({
  name: 'template',
  initialState,
  reducers: {
    updateColumnIndex: (state, action: PayloadAction<number>) => {
      state.columnIndex = action.payload;
    },
    updateTemplateForm: (state, action: PayloadAction<any>) => {
      state.templateForm = action.payload;
    },
    updateLoadingStaus: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(gettemplateform.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(gettemplateform.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.templateForm = data || {}; // Ensure it is not null
      })
      .addCase(gettemplateform.rejected, (state, action) => {
        state.isLoading = false;
        state.loadingError = action.error; // Capture the error if needed
      });
    builder
      .addCase(createtemplate.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createtemplate.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createtemplate.rejected, (state, action) => {
        state.isLoading = false;
        state.loadingError = action.error; // Capture the error if needed
      });
    builder
      .addCase(gettemplateformFields.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(gettemplateformFields.fulfilled, (state, action) => {
        state.isLoading = false;
        state.formFields = action.payload || []; // Ensure it's an array
      })
      .addCase(gettemplateformFields.rejected, (state, action) => {
        state.isLoading = false;
        state.loadingError = action.error; // Capture the error if needed
      });
    builder
      .addCase(gettemplatecountrieslist.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(gettemplatecountrieslist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.countriesList = action.payload || []; // Ensure it's an array
      })
      .addCase(gettemplatecountrieslist.rejected, (state, action) => {
        state.isLoading = false;
        state.loadingError = action.error; // Capture the error if needed
      });
  }
});

export default templateSlice.reducer;
export const { updateColumnIndex, updateTemplateForm, updateLoadingStaus } =
  templateSlice.actions;
