import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  createChecklist,
  createUser,
  deleteListElement,
  deleteUser,
  fetchUserPaginationData,
  getChecklistData,
  getEmpChecklist,
  getEmployees,
  getEmployee,
  getFormValues,
  getOnBoaedApps,
  getOrgApps,
  getOrgData,
  getUser,
  getUsersData,
  Onboardapllicant,
  saveChecklist,
  sendMailAfterScheduleInterview,
  updateUser
} from '../../apis/user';
import { EmployeeList } from '../../types';

interface User {
  organization: string;
  email: string;
  password: string;
  name: string;
  mobile_number: string;
  role: string;
  apps: string[]; // or any other type if apps are objects
  avatar?: string;
}

interface State {
  errors: Record<string, any>; // or you can define a specific type for errors
  isLoading: boolean;
  loadingError: Record<string, any>; // or you can define a specific type for loadingError
  orgList: any[]; // or a specific type if you know the structure
  users: any[]; // or a specific type if you know the structure
  user: User;
  apps: any[]; // or a specific type if you know the structure
  orgApps: any[]; // or a specific type if you know the structure
  selectedOrg: any[]; // or a specific type if you know the structure
  employeeList: EmployeeList[]; // or a specific type if you know the structure
  empCheckListData: any[]; // or a specific type if you know the structure
  completedChecklist: any[]; // or a specific type if you know the structure
  documentsList: any[]; // or a specific type if you know the structure
  checkListData: any; // or a specific type if you know the structure
  userPaginationData: any[];
  employeeDetails: any; // or a specific type if you know the structure
}

const initialState: State = {
  errors: {},
  isLoading: false,
  loadingError: {},
  orgList: [],
  users: [],
  user: {
    organization:
      localStorage.getItem('user_type') === 'super_admin'
        ? ''
        : localStorage.getItem('org_id') || '',
    email: '',
    password: '',
    name: '',
    mobile_number: '',
    role: '',
    apps: []
  },
  apps: [],
  orgApps: [],
  selectedOrg: [],
  employeeList: [],
  empCheckListData: [],
  completedChecklist: [],
  documentsList: [],
  checkListData: [],
  userPaginationData: [],
  employeeDetails: {}
};

//   errors: {},
//   isLoading: false,
//   loadingError: {},
//   orgList: [],
//   users: [],
//   user: {
//     organization:
//       localStorage.getItem("user_type") == "super_admin"
//         ? ""
//         : localStorage.getItem("org_id"),
//     email: "",
//     password: "",
//     name: "",
//     mobile_number: "",
//     role: "",
//     apps: [],
//   },
//   apps:[],
//   orgApps: [],
//   selectedOrg: [],
//   employeeList: [],
//   empCheckListData: [],
//   completedChecklist: [],
//   documentsList: [],
//   checkListData: [],
// };
export const getorgdata = createAsyncThunk('getorgdata', getOrgData);
export const getorgapps = createAsyncThunk('getorgapps', getOrgApps);
export const getusersdata = createAsyncThunk('getusersdata', getUsersData);
export const createuser = createAsyncThunk('createuser', createUser);
export const updateuser = createAsyncThunk('updateuser', updateUser);
export const getuser = createAsyncThunk('getuser', getUser);
export const getemployees = createAsyncThunk('getemployees', getEmployees);
export const getemployee = createAsyncThunk('getemployee', getEmployee);
export const getonboardapps = createAsyncThunk(
  'getonboardapps',
  getOnBoaedApps
);
export const getempchecklist = createAsyncThunk(
  'getempchecklist',
  getEmpChecklist
);
export const getformvalues = createAsyncThunk('getformvalues', getFormValues);
export const getchecklistdata = createAsyncThunk(
  'getchecklistdata',
  getChecklistData
);
export const savechecklist = createAsyncThunk('savechecklist', saveChecklist);
export const createchecklist = createAsyncThunk(
  'createchecklist',
  createChecklist
);
export const deletelistelement = createAsyncThunk(
  'deletelistelement',
  deleteListElement
);
export const onboardapplicant = createAsyncThunk(
  'onboardapplicant',
  Onboardapllicant
);
export const sendmailafterscheduleinterview = createAsyncThunk(
  'sendmailafterscheduleinterview',
  sendMailAfterScheduleInterview
);
export const deleteuser = createAsyncThunk('deleteuser', deleteUser);
export const userPagination = createAsyncThunk(
  'userPagination',
  fetchUserPaginationData
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getorgdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getorgdata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.orgList = data;
      })
      .addCase(getorgdata.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getorgapps.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getorgapps.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.orgApps = data.apps;
        state.selectedOrg = data;
      })
      .addCase(getorgapps.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getusersdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getusersdata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.users = data;
      })
      .addCase(getusersdata.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getuser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getuser.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload;
        data.organization = data.organization.organization_id;
        data.apps = data.apps.map((app: any) => app.app_id);
        state.user = data;
      })
      .addCase(getuser.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(createuser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createuser.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.user = data;
      })
      .addCase(createuser.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(updateuser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateuser.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.user = data;
      })
      .addCase(updateuser.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getemployees.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getemployees.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.employeeList = data.map((element: any) => {
          const newElement = { ...element, id: element.onboarding_employee_id };
          return newElement;
        });
      })
      .addCase(getemployees.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getempchecklist.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getempchecklist.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.empCheckListData = data?.onboard_org_checklist?.checklist;
        state.completedChecklist = data?.completed_checklist;
      })
      .addCase(getempchecklist.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(getemployee.pending, (state) => {
        state.isLoading = true;
        state.documentsList = [];
      })
      .addCase(getemployee.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.checkListData = data?.onboard_org_checklist?.checklist;
        state.completedChecklist = data?.completed_checklist;
        state.documentsList = data?.documents;
        state.employeeDetails = data;
      })
      .addCase(getemployee.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(getformvalues.pending, (state) => {
        state.isLoading = true;
        state.documentsList = [];
      })
      .addCase(getformvalues.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.documentsList = data;
      })
      .addCase(getformvalues.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getchecklistdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getchecklistdata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.checkListData = data?.checklist;
      })
      .addCase(getchecklistdata.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(savechecklist.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(savechecklist.fulfilled, (state) => {
        state.isLoading = false;
        // const data = action.payload.data;
      })
      .addCase(savechecklist.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getonboardapps.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getonboardapps.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.apps = data;
      })
      .addCase(getonboardapps.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(onboardapplicant.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(onboardapplicant.fulfilled, (state) => {
        state.isLoading = false;
        // const data = action.payload.data;
        // state.apps = data;
      })
      .addCase(onboardapplicant.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(sendmailafterscheduleinterview.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(sendmailafterscheduleinterview.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(sendmailafterscheduleinterview.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(deleteuser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteuser.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.user = data;
      })
      .addCase(deleteuser.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(userPagination.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(userPagination.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userPaginationData = action.payload.data;
      })
      .addCase(userPagination.rejected, (state) => {
        state.isLoading = false;
      });
  }
});

export default userSlice.reducer;
