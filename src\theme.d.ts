import { PaletteColor, PaletteColorOptions } from '@mui/material';

declare module '@mui/material/styles' {
  interface Palette {
    white: PaletteColor;
    input: PaletteColor;
    white2: PaletteColor;
    gray: PaletteColor;
    gray2: PaletteColor;
    gray3: PaletteColor;
    gray4: PaletteColor;
    gray5: PaletteColor;
    blk: PaletteColor;
    blk2: PaletteColor;
    cyanlight: PaletteColor;
    cyan: PaletteColor;
    cyan2: PaletteColor;
    cyan3: PaletteColor;
    dark: PaletteColor;
    red: PaletteColor;
    red2: PaletteColor;
    lightOrange: PaletteColor;
    G3: PaletteColor;
    vi3: PaletteColor;
    vi2: PaletteColor;
    blu: PaletteColor;
    primaryBlue: PaletteColor;
    b1: PaletteColor;
    b2: PaletteColor;
    b3: PaletteColor;
    purple: PaletteColor;
    blue2: PaletteColor;
    blue3: PaletteColor;
    green: PaletteColor;
    gray6: PaletteColor;
    secondary2: PaletteColor;
  }
  interface MainPalette {
    primary: PaletteColor;
    secondary: PaletteColor;
    common: PaletteColor;
  }

  interface PaletteOptions {
    white?: PalletColorOptions;
    white2?: PalletColorOptions;
    input?: PalletColorOptions;
    gray?: PaletteColorOptions;
    gray2?: PaletteColorOptions;
    gray3?: PaletteColorOptions;
    gray4?: PaletteColorOptions;
    gray5?: PaletteColorOptions;
    blk?: PaletteColorOptions;
    blk2?: PaletteColorOptions;
    cyanlight?: PaletteColorOptions;
    cyan?: PaletteColorOptions;
    cyan2?: PaletteColorOptions;
    cyan3?: PaletteColorOptions;
    dark?: PaletteColorOptions;
    red?: PaletteColorOptions;
    red2?: PaletteColorOptions;
    lightOrange?: PaletteColorOptions;
    G3?: PaletteColorOptions;
    vi3?: PaletteColorOptions;
    vi2?: PaletteColorOptions;
    blu?: PaletteColorOptions;
    primaryBlue?: PaletteColorOptions;
    b1?: PaletteColorOptions;
    b2?: PaletteColorOptions;
    b3?: PaletteColorOptions;
    purple?: PaletteColorOptions;
    blue2?: PaletteColorOptions;
    blue3?: PaletteColorOptions;
    green?: PaletteColorOptions;
    gray6?: PaletteColorOptions;
    secondary2?: PaletteColorOptions;
  }
}

// Update the Button's color options to include an ochre option
declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    white: true;
    white2: true;
    gray: true;
    gray2: true;
    gray3: true;
    gray4: true;
    gray5: true;
    blk: true;
    blk2: true;
    cyanlight: true;
    cyan: true;
    cyan2: true;
    cyan3: true;
    dark: true;
    red: true;
    red2: true;
    lightOrange: true;
    G3: true;
    vi3: true;
    vi2: true;
    blu: true;
    primaryBlue: true;
    b1: true;
    b2: true;
    b3: true;
    purple: true;
    blue2: true;
    blue3: true;
    green: true;
    gray6: true;
    secondary2: true;
  }
}
// Update the AppBar's color options to include an ochre option
declare module '@mui/material/AppBar' {
  interface AppBarPropsColorOverrides {
    white: true;
  }
}
