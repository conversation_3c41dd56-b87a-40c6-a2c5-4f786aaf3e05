export interface FormObject {
  name: string;
  description: string;
  icon?: string;
  status: boolean;
  groups: Group[];
}

export interface Group {
  id: string;
  group_title: string;
  group_key: string;
  group_index: string;
  group_description: string;
  is_iterative_or_not: boolean;
  iteration_min_length: number;
  iteration_max_length: number;
  fields: Field[];
}

export interface Field {
  // Define the structure of a field if you have specific properties
  name: string;
  field_index: number;
  field_id: string;
  original_field_id: string;
  type: string;
  input_type: string;
  label: string;
  label_url: string;
  label_url_type: string | null;
  placeHolder: string;
  description_status: boolean;
  description: string;
  validation_schema: ValidationSchema;
  options: string;
  is_iterative_or_not: boolean;
  iteration_min_length: number;
  iteration_max_length: number | null;
  points: number | null;
  checkbox_answer_type: string;
  checkbox_answer_limit: string;
  is_quiz_field: boolean;
  default_country: string;
  default_state: string;
  group_title: string;
  group_key: string;
  value?: string;
}

export interface ValidationSchema {
  required: boolean;
}

export interface ASSESSMENTCLIENTS {
  client_id: string;
  address: string;
  createdAt: string;
  deleted_at: null;
  form_values: string[];
  mobile_number: number;
  name: string;
  storage_folder_name: string;
  updatedAt: string;
  user_id: string;
}

export interface APP {
  app_code: string;
  app_id: string;
  created_at: string;
  deleted_at: null;
  description: string;
  id: number;
  industry_app_process: any;
  name: string;
  orgAppConfiguration: any;
  status: boolean;
  updated_at: string;
}

export interface TEMPLATE {
  name: string;
  desription: string;
  section_id: string;
  fields: Field[];
}
