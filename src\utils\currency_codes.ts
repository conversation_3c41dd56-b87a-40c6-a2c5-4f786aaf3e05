export interface CurrencyOption {
  code: string;
  symbol: string;
  name: string;
  locale: string | null;
}

const currencyOptions: CurrencyOption[] = [
  {
    code: 'AED',
    symbol: 'د.إ',
    name: 'United Arab Emirates Dirham',
    locale: 'ar-AE'
  },
  {
    code: 'AFN',
    symbol: '؋',
    name: 'Afghan Afghani',
    locale: 'fa-AF'
  },
  {
    code: 'ALL',
    symbol: 'L',
    name: 'Albanian Lek',
    locale: 'sq-AL'
  },
  {
    code: 'AMD',
    symbol: '֏',
    name: 'Armenian Dram',
    locale: 'hy-AM'
  },
  {
    code: 'ANG',
    symbol: 'ƒ',
    name: 'Netherlands Antillean Guilder',
    locale: 'nl-CW'
  },
  {
    code: 'AOA',
    symbol: 'Kz',
    name: 'Angolan Kwanza',
    locale: 'pt-AO'
  },
  {
    code: 'ARS',
    symbol: '$',
    name: 'Argentine Peso',
    locale: 'es-AR'
  },
  {
    code: 'AUD',
    symbol: '$',
    name: 'Australian Dollar',
    locale: 'en-AU'
  },
  {
    code: 'AWG',
    symbol: 'ƒ',
    name: 'Aruban Florin',
    locale: 'nl-AW'
  },
  {
    code: 'AZN',
    symbol: '₼',
    name: 'Azerbaijani Manat',
    locale: 'az-AZ'
  },
  {
    code: 'BAM',
    symbol: 'KM',
    name: 'Bosnia and Herzegovina Convertible Mark',
    locale: 'bs-BA'
  },
  {
    code: 'BBD',
    symbol: '$',
    name: 'Barbadian Dollar',
    locale: 'en-BB'
  },
  {
    code: 'BDT',
    symbol: '৳',
    name: 'Bangladeshi Taka',
    locale: 'bn-BD'
  },
  {
    code: 'BGN',
    symbol: 'лв',
    name: 'Bulgarian Lev',
    locale: 'bg-BG'
  },
  {
    code: 'BHD',
    symbol: 'د.ب',
    name: 'Bahraini Dinar',
    locale: 'ar-BH'
  },
  {
    code: 'BIF',
    symbol: 'FBu',
    name: 'Burundian Franc',
    locale: 'rw-BI'
  },
  {
    code: 'BMD',
    symbol: '$',
    name: 'Bermudian Dollar',
    locale: 'en-BM'
  },
  {
    code: 'BND',
    symbol: '$',
    name: 'Brunei Dollar',
    locale: 'ms-BN'
  },
  {
    code: 'BOB',
    symbol: 'Bs.',
    name: 'Bolivian Boliviano',
    locale: 'es-BO'
  },
  {
    code: 'BOV',
    symbol: 'BOV',
    name: 'Bolivian Mvdol',
    locale: 'es-BO'
  },
  {
    code: 'BRL',
    symbol: 'R$',
    name: 'Brazilian Real',
    locale: 'pt-BR'
  },
  {
    code: 'BSD',
    symbol: '$',
    name: 'Bahamian Dollar',
    locale: 'en-BS'
  },
  {
    code: 'BTN',
    symbol: 'Nu.',
    name: 'Bhutanese Ngultrum',
    locale: 'dz-BT'
  },
  {
    code: 'BWP',
    symbol: 'P',
    name: 'Botswana Pula',
    locale: 'en-BW'
  },
  {
    code: 'BYN',
    symbol: 'Br',
    name: 'Belarusian Ruble',
    locale: 'be-BY'
  },
  {
    code: 'BZD',
    symbol: 'BZ$',
    name: 'Belize Dollar',
    locale: 'en-BZ'
  },
  {
    code: 'CAD',
    symbol: '$',
    name: 'Canadian Dollar',
    locale: 'en-CA'
  },
  {
    code: 'CDF',
    symbol: 'FC',
    name: 'Congolese Franc',
    locale: 'fr-CD'
  },
  {
    code: 'CHE',
    symbol: 'CHE',
    name: 'WIR Euro',
    locale: 'de-CH'
  },
  {
    code: 'CHF',
    symbol: 'Fr.',
    name: 'Swiss Franc',
    locale: 'de-CH'
  },
  {
    code: 'CHW',
    symbol: 'CHW',
    name: 'WIR Franc',
    locale: 'fr-CH'
  },
  {
    code: 'CLF',
    symbol: 'UF',
    name: 'Unidad de Fomento',
    locale: 'es-CL'
  },
  {
    code: 'CLP',
    symbol: '$',
    name: 'Chilean Peso',
    locale: 'es-CL'
  },
  {
    code: 'CNY',
    symbol: '¥',
    name: 'Chinese Yuan',
    locale: 'zh-CN'
  },
  {
    code: 'COP',
    symbol: '$',
    name: 'Colombian Peso',
    locale: 'es-CO'
  },
  {
    code: 'COU',
    symbol: 'COU',
    name: 'Unidad de Valor Real',
    locale: 'es-CO'
  },
  {
    code: 'CRC',
    symbol: '₡',
    name: 'Costa Rican Colón',
    locale: 'es-CR'
  },
  {
    code: 'CUC',
    symbol: '$',
    name: 'Cuban Convertible Peso',
    locale: 'es-CU'
  },
  {
    code: 'CUP',
    symbol: '$',
    name: 'Cuban Peso',
    locale: 'es-CU'
  },
  {
    code: 'CVE',
    symbol: '$',
    name: 'Cape Verdean Escudo',
    locale: 'pt-CV'
  },
  {
    code: 'CZK',
    symbol: 'Kč',
    name: 'Czech Koruna',
    locale: 'cs-CZ'
  },
  {
    code: 'DJF',
    symbol: 'Fdj',
    name: 'Djiboutian Franc',
    locale: 'fr-DJ'
  },
  {
    code: 'DKK',
    symbol: 'kr',
    name: 'Danish Krone',
    locale: 'da-DK'
  },
  {
    code: 'DOP',
    symbol: 'RD$',
    name: 'Dominican Peso',
    locale: 'es-DO'
  },
  {
    code: 'DZD',
    symbol: 'د.ج',
    name: 'Algerian Dinar',
    locale: 'ar-DZ'
  },
  {
    code: 'EGP',
    symbol: 'ج.م',
    name: 'Egyptian Pound',
    locale: 'ar-EG'
  },
  {
    code: 'ERN',
    symbol: 'Nfk',
    name: 'Eritrean Nakfa',
    locale: 'ti-ER'
  },
  {
    code: 'ETB',
    symbol: 'Br',
    name: 'Ethiopian Birr',
    locale: 'am-ET'
  },
  {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    locale: 'fr-FR'
  },
  {
    code: 'FJD',
    symbol: '$',
    name: 'Fijian Dollar',
    locale: 'en-FJ'
  },
  {
    code: 'FKP',
    symbol: '£',
    name: 'Falkland Islands Pound',
    locale: 'en-FK'
  },
  {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    locale: 'en-GB'
  },
  {
    code: 'GEL',
    symbol: '₾',
    name: 'Georgian Lari',
    locale: 'ka-GE'
  },
  {
    code: 'GHS',
    symbol: 'GH₵',
    name: 'Ghanaian Cedi',
    locale: 'ak-GH'
  },
  {
    code: 'GIP',
    symbol: '£',
    name: 'Gibraltar Pound',
    locale: 'en-GI'
  },
  {
    code: 'GMD',
    symbol: 'D',
    name: 'Gambian Dalasi',
    locale: 'en-GM'
  },
  {
    code: 'GNF',
    symbol: 'FG',
    name: 'Guinean Franc',
    locale: 'fr-GN'
  },
  {
    code: 'GTQ',
    symbol: 'Q',
    name: 'Guatemalan Quetzal',
    locale: 'es-GT'
  },
  {
    code: 'GYD',
    symbol: '$',
    name: 'Guyanese Dollar',
    locale: 'en-GY'
  },
  {
    code: 'HKD',
    symbol: '$',
    name: 'Hong Kong Dollar',
    locale: 'zh-HK'
  },
  {
    code: 'HNL',
    symbol: 'L',
    name: 'Honduran Lempira',
    locale: 'es-HN'
  },
  {
    code: 'HTG',
    symbol: 'G',
    name: 'Haitian Gourde',
    locale: 'fr-HT'
  },
  {
    code: 'HUF',
    symbol: 'Ft',
    name: 'Hungarian Forint',
    locale: 'hu-HU'
  },
  {
    code: 'IDR',
    symbol: 'Rp',
    name: 'Indonesian Rupiah',
    locale: 'id-ID'
  },
  {
    code: 'ILS',
    symbol: '₪',
    name: 'Israeli New Shekel',
    locale: 'he-IL'
  },
  {
    code: 'INR',
    symbol: '₹',
    name: 'Indian Rupee',
    locale: 'hi-IN'
  },
  {
    code: 'IQD',
    symbol: 'ع.د',
    name: 'Iraqi Dinar',
    locale: 'ar-IQ'
  },
  {
    code: 'IRR',
    symbol: '﷼',
    name: 'Iranian Rial',
    locale: 'fa-IR'
  },
  {
    code: 'ISK',
    symbol: 'kr',
    name: 'Icelandic Króna',
    locale: 'is-IS'
  },
  {
    code: 'JMD',
    symbol: '$',
    name: 'Jamaican Dollar',
    locale: 'en-JM'
  },
  {
    code: 'JOD',
    symbol: 'د.ا',
    name: 'Jordanian Dinar',
    locale: 'ar-JO'
  },
  {
    code: 'JPY',
    symbol: '¥',
    name: 'Japanese Yen',
    locale: 'ja-JP'
  },
  {
    code: 'KES',
    symbol: 'KSh',
    name: 'Kenyan Shilling',
    locale: 'sw-KE'
  },
  {
    code: 'KGS',
    symbol: 'сом',
    name: 'Kyrgyzstani Som',
    locale: 'ky-KG'
  },
  {
    code: 'KHR',
    symbol: '៛',
    name: 'Cambodian Riel',
    locale: 'km-KH'
  },
  {
    code: 'KMF',
    symbol: 'CF',
    name: 'Comorian Franc',
    locale: 'ar-KM'
  },
  {
    code: 'KPW',
    symbol: '₩',
    name: 'North Korean Won',
    locale: 'ko-KP'
  },
  {
    code: 'KRW',
    symbol: '₩',
    name: 'South Korean Won',
    locale: 'ko-KR'
  },
  {
    code: 'KWD',
    symbol: 'د.ك',
    name: 'Kuwaiti Dinar',
    locale: 'ar-KW'
  },
  {
    code: 'KYD',
    symbol: '$',
    name: 'Cayman Islands Dollar',
    locale: 'en-KY'
  },
  {
    code: 'KZT',
    symbol: '₸',
    name: 'Kazakhstani Tenge',
    locale: 'kk-KZ'
  },
  {
    code: 'LAK',
    symbol: '₭',
    name: 'Lao Kip',
    locale: 'lo-LA'
  },
  {
    code: 'LBP',
    symbol: 'ل.ل',
    name: 'Lebanese Pound',
    locale: 'ar-LB'
  },
  {
    code: 'LKR',
    symbol: 'Rs',
    name: 'Sri Lankan Rupee',
    locale: 'si-LK'
  },
  {
    code: 'LRD',
    symbol: '$',
    name: 'Liberian Dollar',
    locale: 'en-LR'
  },
  {
    code: 'LSL',
    symbol: 'M',
    name: 'Lesotho Loti',
    locale: 'st-LS'
  },
  {
    code: 'LYD',
    symbol: 'ل.د',
    name: 'Libyan Dinar',
    locale: 'ar-LY'
  },
  {
    code: 'MAD',
    symbol: 'د.م.',
    name: 'Moroccan Dirham',
    locale: 'ar-MA'
  },
  {
    code: 'MDL',
    symbol: 'L',
    name: 'Moldovan Leu',
    locale: 'ro-MD'
  },
  {
    code: 'MGA',
    symbol: 'Ar',
    name: 'Malagasy Ariary',
    locale: 'mg-MG'
  },
  {
    code: 'MKD',
    symbol: 'ден',
    name: 'Macedonian Denar',
    locale: 'mk-MK'
  },
  {
    code: 'MMK',
    symbol: 'K',
    name: 'Myanmar Kyat',
    locale: 'my-MM'
  },
  {
    code: 'MNT',
    symbol: '₮',
    name: 'Mongolian Tögrög',
    locale: 'mn-MN'
  },
  {
    code: 'MOP',
    symbol: 'MOP$',
    name: 'Macanese Pataca',
    locale: 'zh-MO'
  },
  {
    code: 'MRU',
    symbol: 'UM',
    name: 'Mauritanian Ouguiya',
    locale: 'ar-MR'
  },
  {
    code: 'MUR',
    symbol: 'Rs',
    name: 'Mauritian Rupee',
    locale: 'en-MU'
  },
  {
    code: 'MVR',
    symbol: 'MVR',
    name: 'Maldivian Rufiyaa',
    locale: 'dv-MV'
  },
  {
    code: 'MWK',
    symbol: 'MK',
    name: 'Malawian Kwacha',
    locale: 'ny-MW'
  },
  {
    code: 'MXN',
    symbol: '$',
    name: 'Mexican Peso',
    locale: 'es-MX'
  },
  {
    code: 'MXV',
    symbol: 'MXV',
    name: 'Mexican Unidad de Inversion',
    locale: 'es-MX'
  },
  {
    code: 'MYR',
    symbol: 'RM',
    name: 'Malaysian Ringgit',
    locale: 'ms-MY'
  },
  {
    code: 'MZN',
    symbol: 'MT',
    name: 'Mozambican Metical',
    locale: 'pt-MZ'
  },
  {
    code: 'NAD',
    symbol: '$',
    name: 'Namibian Dollar',
    locale: 'en-NA'
  },
  {
    code: 'NGN',
    symbol: '₦',
    name: 'Nigerian Naira',
    locale: 'en-NG'
  },
  {
    code: 'NIO',
    symbol: 'C$',
    name: 'Nicaraguan Córdoba',
    locale: 'es-NI'
  },
  {
    code: 'NOK',
    symbol: 'kr',
    name: 'Norwegian Krone',
    locale: 'nb-NO'
  },
  {
    code: 'NPR',
    symbol: 'रू',
    name: 'Nepalese Rupee',
    locale: 'ne-NP'
  },
  {
    code: 'NZD',
    symbol: '$',
    name: 'New Zealand Dollar',
    locale: 'en-NZ'
  },
  {
    code: 'OMR',
    symbol: 'ر.ع.',
    name: 'Omani Rial',
    locale: 'ar-OM'
  },
  {
    code: 'PAB',
    symbol: 'B/.',
    name: 'Panamanian Balboa',
    locale: 'es-PA'
  },
  {
    code: 'PEN',
    symbol: 'S/',
    name: 'Peruvian Sol',
    locale: 'es-PE'
  },
  {
    code: 'PGK',
    symbol: 'K',
    name: 'Papua New Guinean Kina',
    locale: 'en-PG'
  },
  {
    code: 'PHP',
    symbol: '₱',
    name: 'Philippine Peso',
    locale: 'en-PH'
  },
  {
    code: 'PKR',
    symbol: 'Rs',
    name: 'Pakistani Rupee',
    locale: 'ur-PK'
  },
  {
    code: 'PLN',
    symbol: 'zł',
    name: 'Polish Złoty',
    locale: 'pl-PL'
  },
  {
    code: 'PYG',
    symbol: '₲',
    name: 'Paraguayan Guaraní',
    locale: 'es-PY'
  },
  {
    code: 'QAR',
    symbol: 'ر.ق',
    name: 'Qatari Riyal',
    locale: 'ar-QA'
  },
  {
    code: 'RON',
    symbol: 'lei',
    name: 'Romanian Leu',
    locale: 'ro-RO'
  },
  {
    code: 'RSD',
    symbol: 'дин.',
    name: 'Serbian Dinar',
    locale: 'sr-RS'
  },
  {
    code: 'RUB',
    symbol: '₽',
    name: 'Russian Ruble',
    locale: 'ru-RU'
  },
  {
    code: 'RWF',
    symbol: 'FRw',
    name: 'Rwandan Franc',
    locale: 'rw-RW'
  },
  {
    code: 'SAR',
    symbol: 'ر.س',
    name: 'Saudi Riyal',
    locale: 'ar-SA'
  },
  {
    code: 'SBD',
    symbol: '$',
    name: 'Solomon Islands Dollar',
    locale: 'en-SB'
  },
  {
    code: 'SCR',
    symbol: 'SR',
    name: 'Seychellois Rupee',
    locale: 'fr-SC'
  },
  {
    code: 'SDG',
    symbol: 'ج.س.',
    name: 'Sudanese Pound',
    locale: 'ar-SD'
  },
  {
    code: 'SEK',
    symbol: 'kr',
    name: 'Swedish Krona',
    locale: 'sv-SE'
  },
  {
    code: 'SGD',
    symbol: '$',
    name: 'Singapore Dollar',
    locale: 'en-SG'
  },
  {
    code: 'SHP',
    symbol: '£',
    name: 'Saint Helena Pound',
    locale: 'en-SH'
  },
  {
    code: 'SLE',
    symbol: 'Le',
    name: 'Sierra Leonean Leone',
    locale: 'en-SL'
  },
  {
    code: 'SOS',
    symbol: 'S',
    name: 'Somali Shilling',
    locale: 'so-SO'
  },
  {
    code: 'SRD',
    symbol: '$',
    name: 'Surinamese Dollar',
    locale: 'nl-SR'
  },
  {
    code: 'SSP',
    symbol: '£',
    name: 'South Sudanese Pound',
    locale: 'en-SS'
  },
  {
    code: 'STN',
    symbol: 'Db',
    name: 'São Tomé and Príncipe Dobra',
    locale: 'pt-ST'
  },
  {
    code: 'SVC',
    symbol: '₡',
    name: 'Salvadoran Colón',
    locale: 'es-SV'
  },
  {
    code: 'SYP',
    symbol: 'ل.س',
    name: 'Syrian Pound',
    locale: 'ar-SY'
  },
  {
    code: 'SZL',
    symbol: 'E',
    name: 'Swazi Lilangeni',
    locale: 'en-SZ'
  },
  {
    code: 'THB',
    symbol: '฿',
    name: 'Thai Baht',
    locale: 'th-TH'
  },
  {
    code: 'TJS',
    symbol: 'ЅМ',
    name: 'Tajikistani Somoni',
    locale: 'tg-TJ'
  },
  {
    code: 'TMT',
    symbol: 'm',
    name: 'Turkmenistan Manat',
    locale: 'tk-TM'
  },
  {
    code: 'TND',
    symbol: 'د.ت',
    name: 'Tunisian Dinar',
    locale: 'ar-TN'
  },
  {
    code: 'TOP',
    symbol: 'T$',
    name: 'Tongan Paʻanga',
    locale: 'to-TO'
  },
  {
    code: 'TRY',
    symbol: '₺',
    name: 'Turkish Lira',
    locale: 'tr-TR'
  },
  {
    code: 'TTD',
    symbol: '$',
    name: 'Trinidad and Tobago Dollar',
    locale: 'en-TT'
  },
  {
    code: 'TWD',
    symbol: 'NT$',
    name: 'New Taiwan Dollar',
    locale: 'zh-TW'
  },
  {
    code: 'TZS',
    symbol: 'TSh',
    name: 'Tanzanian Shilling',
    locale: 'sw-TZ'
  },
  {
    code: 'UAH',
    symbol: '₴',
    name: 'Ukrainian Hryvnia',
    locale: 'uk-UA'
  },
  {
    code: 'UGX',
    symbol: 'USh',
    name: 'Ugandan Shilling',
    locale: 'sw-UG'
  },
  {
    code: 'USD',
    symbol: '$',
    name: 'United States Dollar',
    locale: 'en-US'
  },
  {
    code: 'USN',
    symbol: 'USN',
    name: 'United States Dollar (Next day)',
    locale: 'en-US'
  },
  {
    code: 'UYI',
    symbol: 'UYI',
    name: 'Uruguay Peso en Unidades Indexadas',
    locale: 'es-UY'
  },
  {
    code: 'UYU',
    symbol: '$U',
    name: 'Uruguayan Peso',
    locale: 'es-UY'
  },
  {
    code: 'UYW',
    symbol: 'UYW',
    name: 'Unidad Previsional',
    locale: 'es-UY'
  },
  {
    code: 'UZS',
    symbol: 'сўм',
    name: 'Uzbekistani Som',
    locale: 'uz-UZ'
  },
  {
    code: 'VED',
    symbol: 'VED',
    name: 'Venezuelan Digital Bolívar',
    locale: 'es-VE'
  },
  {
    code: 'VES',
    symbol: 'Bs.S',
    name: 'Venezuelan Sovereign Bolívar',
    locale: 'es-VE'
  },
  {
    code: 'VND',
    symbol: '₫',
    name: 'Vietnamese Đồng',
    locale: 'vi-VN'
  },
  {
    code: 'VUV',
    symbol: 'VT',
    name: 'Vanuatu Vatu',
    locale: 'en-VU'
  },
  {
    code: 'WST',
    symbol: 'WS$',
    name: 'Samoan Tala',
    locale: 'sm-WS'
  },
  {
    code: 'XAF',
    symbol: 'FCFA',
    name: 'Central African CFA Franc',
    locale: 'fr-CF'
  },
  {
    code: 'XAG',
    symbol: 'XAG',
    name: 'Silver',
    locale: null
  },
  {
    code: 'XAU',
    symbol: 'XAU',
    name: 'Gold',
    locale: null
  },
  {
    code: 'XBA',
    symbol: 'XBA',
    name: 'European Composite Unit',
    locale: null
  },
  {
    code: 'XBB',
    symbol: 'XBB',
    name: 'European Monetary Unit',
    locale: null
  },
  {
    code: 'XBC',
    symbol: 'XBC',
    name: 'European Unit of Account 9',
    locale: null
  },
  {
    code: 'XBD',
    symbol: 'XBD',
    name: 'European Unit of Account 17',
    locale: null
  },
  {
    code: 'XCD',
    symbol: 'EC$',
    name: 'East Caribbean Dollar',
    locale: 'en-GD'
  },
  {
    code: 'XDR',
    symbol: 'XDR',
    name: 'Special Drawing Rights',
    locale: null
  },
  {
    code: 'XOF',
    symbol: 'CFA',
    name: 'West African CFA Franc',
    locale: 'fr-CI'
  },
  {
    code: 'XPD',
    symbol: 'XPD',
    name: 'Palladium',
    locale: null
  },
  {
    code: 'XPF',
    symbol: 'CFPF',
    name: 'CFP Franc',
    locale: 'fr-PF'
  },
  {
    code: 'XPT',
    symbol: 'XPT',
    name: 'Platinum',
    locale: null
  },
  {
    code: 'XSU',
    symbol: 'XSU',
    name: 'Sucre',
    locale: null
  },
  {
    code: 'XTS',
    symbol: 'XTS',
    name: 'Testing Currency Code',
    locale: null
  },
  {
    code: 'XUA',
    symbol: 'XUA',
    name: 'ADB Unit of Account',
    locale: null
  },
  {
    code: 'XXX',
    symbol: 'XXX',
    name: 'No currency',
    locale: null
  },
  {
    code: 'YER',
    symbol: '﷼',
    name: 'Yemeni Rial',
    locale: 'ar-YE'
  },
  {
    code: 'ZAR',
    symbol: 'R',
    name: 'South African Rand',
    locale: 'af-ZA'
  },
  {
    code: 'ZMW',
    symbol: 'ZK',
    name: 'Zambian Kwacha',
    locale: 'en-ZM'
  },
  {
    code: 'ZWG',
    symbol: 'ZWG',
    name: 'Zimbabwean RTGS Dollar',
    locale: 'en-ZW'
  }
];

export default currencyOptions;
