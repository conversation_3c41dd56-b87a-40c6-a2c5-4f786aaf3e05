export function useNumberToWord(number: any) {
  if (number < 0) return false;

  // Arrays to hold words for single-digit, double-digit, and below-hundred numbers
  const singleDigit = [
    '',
    'One',
    'Two',
    'Three',
    'Four',
    'Five',
    'Six',
    'Seven',
    'Eight',
    'Nine'
  ];
  const doubleDigit = [
    'Ten',
    'Eleven',
    'Twelve',
    'Thirteen',
    'Fourteen',
    'Fifteen',
    'Sixteen',
    'Seventeen',
    'Eighteen',
    'Nineteen'
  ];
  const belowHundred = [
    'Twenty',
    'Thirty',
    'Forty',
    'Fifty',
    'Sixty',
    'Seventy',
    'Eighty',
    'Ninety'
  ];

  if (number === 0) return 'Zero';

  const translate = (n: number) => {
    let word: any = '';
    if (n < 10) {
      word = `${singleDigit[n]} `;
    } else if (n < 20) {
      word = `${doubleDigit[n - 10]} `;
    } else if (n < 100) {
      const rem = translate(n % 10);
      word = `${belowHundred[(n - (n % 10)) / 10 - 2]} ${rem}`;
    } else if (n < 1000) {
      word = `${singleDigit[Math.trunc(n / 100)]} Hundred ${translate(n % 100)}`;
    }
    return word;
  };

  const result: any = translate(number);
  return result.trim();
}

export default useNumberToWord;
