import moment from 'moment';
import striptags from 'striptags';
import { FIELD, GROUP } from '../types';

export const formatText = (
  value: string,
  format: 'underscore' | 'plain' = 'underscore'
): string => {
  if (value && typeof value === 'string') {
    switch (format) {
      case 'plain':
        return value.replace(/[_-]/g, ' ').replace(/[\s]{1,}/g, ' ');
      case 'underscore':
        return value.replace(/\s/g, '_').replace(/[\s]{1,}/g, ' ');
      default:
        return value.replace(/[\s]{1,}/g, ' ');
    }
  } else {
    return '';
  }
};

export const formatParagraph = (word: string) =>
  word && `${word.charAt(0).toUpperCase()}${word.slice(1)}`;

export const trimString = (str: string): string => str && str.trim();

export const dateFormat = (date: Date, dateformat = 'DD MMM YYYY'): string =>
  moment(date).format(dateformat);

export const timeFormat = (date: Date, format = 'H:mm'): string =>
  moment(date).format(format);

export const addYears = (date: Date, count: number): Date => {
  const cloneDate = new Date(date);
  return new Date(cloneDate.setFullYear(cloneDate.getFullYear() + count));
};

export const removeHtmlTags = (html: string): string => striptags(html);

export const capitalizeWords = (text: string): string =>
  text.replace(/(?:^|\s|[\\([{])+\S/g, (match) => match.toUpperCase());
// text.replace(/(^\w{1})|(\s+\w{1})/g, (letter) => letter.toUpperCase());

export const generateEmptyObjectForArraySections = (group: GROUP) => {
  const fields: any = {};
  group.fields.forEach((field: FIELD) => {
    let values: any[] | string = '';
    if (field.is_iterative_or_not) {
      values = [''];
      if (field.input_type === 'checkbox') {
        values = [[]];
      }
    } else {
      values = '';
      if (field.input_type === 'checkbox') {
        values = [];
      }
    }
    if (field.type === 'signature') {
      if (field.is_iterative_or_not) {
        fields[`${field.name}_agree`] = [false];
        fields[`${field.name}_date`] = [''];
      } else {
        fields[`${field.name}_agree`] = false;
        fields[`${field.name}_date`] = '';
      }
    }
    fields[field.name] = values;
  });
  return fields;
};

export const getFormTileText = (text: string): string => {
  const textArr = text.split(' ');
  if (textArr.length === 1) {
    return textArr.join('')?.toUpperCase();
  }
  return capitalizeWords(textArr.join(' ')?.toLowerCase());
};
const windowWidth = window.innerWidth;

export const getCanvasWidth = () => {
  let returnValue = windowWidth;
  if (windowWidth >= 900) {
    returnValue = (windowWidth / 100) * 56;
  } else if (windowWidth >= 600) {
    returnValue = (windowWidth / 100) * 70;
  } else {
    returnValue = (windowWidth / 100) * 86;
  }
  return returnValue;
};

export const toPascalCase = (str: string) => {
  return str
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
};

export const debounce = <T extends (...args: any[]) => void>(
  fn: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout>;

  return function (...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
};
