/**
 * Storage value in local storage
 * @param key string
 * @param value string
 */
export const setStorageItem = (key: string, value: string): void => {
  localStorage.setItem(key, value);
};

/**
 * Get the stored value in local storage
 * @param key
 * @returns string | null
 */
export const getStorageItem = (key: string): string | null => {
  return localStorage.getItem(key);
};

/**
 * Make first letter capital for a string
 * @param str string
 * @returns string
 */
export const capitalizeFirstLetter = (str: unknown): string => {
  if (typeof str !== 'string') return '';

  return str.charAt(0).toUpperCase() + str.slice(1);
};
