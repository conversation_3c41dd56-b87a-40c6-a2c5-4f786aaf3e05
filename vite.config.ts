import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
// import federation from "@originjs/vite-plugin-federation";
import dynamicImport from "vite-plugin-dynamic-import";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    dynamicImport(),
    // federation({
    //   remotes: {
    //     klezacomponents: "http://localhost:4173/assets/klezacomponents.js",
    //   },
    //   name: "klezafab",
    //   shared: ["react"],
    //   filename: "klezafab.js",
    // }),
  ],
  build: {
    minify: true,
    target: "esnext",
    cssCodeSplit: true,
    modulePreload: false,
  },

});
