// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Documents/office/kleza-fab-react/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/Documents/office/kleza-fab-react/node_modules/@vitejs/plugin-react-swc/index.mjs";
import dynamicImport from "file:///C:/Users/<USER>/Documents/office/kleza-fab-react/node_modules/vite-plugin-dynamic-import/dist/index.mjs";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    dynamicImport()
    // federation({
    //   remotes: {
    //     klezacomponents: "http://localhost:4173/assets/klezacomponents.js",
    //   },
    //   name: "klezafab",
    //   shared: ["react"],
    //   filename: "klezafab.js",
    // }),
  ],
  build: {
    minify: true,
    target: "esnext",
    cssCodeSplit: true,
    modulePreload: false
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
